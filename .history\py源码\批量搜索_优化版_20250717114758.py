"""
批量搜索工具 - 优化版
集成所有性能优化和功能增强
"""

import sys
import os
import time
import threading
import configparser
import shutil
from datetime import datetime
from typing import List, Dict, Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QTextEdit, QListWidget, QCheckBox, QGroupBox,
                             QMessageBox, QFileDialog, QFormLayout, QSplitter,
                             QTabWidget, QScrollArea, QComboBox, QSpinBox,
                             QGridLayout, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor

# 导入优化模块
from optimized_search_engine import OptimizedSearchEngine, SearchPattern, SearchMode
from memory_optimized_processor import StreamingFileProcessor, MemoryMonitor
from advanced_search_modes import SearchModeManager
from enhanced_ui_components import (SearchHistoryManager, EnhancedProgressWidget,
                                   SearchHistoryWidget, SearchStatisticsWidget,
                                   EnhancedResultWidget)


class OptimizedSearchThread(QThread):
    """优化的搜索线程"""
    
    finished = pyqtSignal(dict, dict, float)  # 结果, 统计信息, 搜索时间
    progress = pyqtSignal(str, int, int)  # 消息, 当前进度, 总进度
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, search_params: Dict):
        super().__init__()
        self.search_params = search_params
        self.search_engine = OptimizedSearchEngine()
        self.file_processor = StreamingFileProcessor()
        self.search_manager = SearchModeManager()
        self._cancelled = False
    
    def cancel(self):
        """取消搜索"""
        self._cancelled = True
    
    def run(self):
        """执行搜索"""
        try:
            start_time = time.time()
            
            # 解析搜索参数
            search_terms = self.search_params['search_terms']
            search_path = self.search_params['search_path']
            extensions = self.search_params['extensions']
            search_mode = self.search_params.get('search_mode', 'contains')
            case_sensitive = self.search_params.get('case_sensitive', False)
            enable_fuzzy = self.search_params.get('enable_fuzzy', False)
            enable_advanced = self.search_params.get('enable_advanced', False)
            
            self.progress.emit("初始化搜索引擎...", 0, 100)
            
            # 创建搜索模式
            patterns = []
            for term in search_terms:
                if enable_advanced:
                    # 使用高级搜索解析
                    expressions = self.search_manager.parser.parse_search_query(term)
                    for expr in expressions:
                        mode = SearchMode(expr.mode) if expr.mode in [m.value for m in SearchMode] else SearchMode.CONTAINS
                        pattern = SearchPattern(
                            pattern=expr.pattern,
                            mode=mode,
                            case_sensitive=expr.case_sensitive or case_sensitive
                        )
                        patterns.append(pattern)
                else:
                    # 使用基本搜索模式
                    mode = SearchMode(search_mode) if search_mode in [m.value for m in SearchMode] else SearchMode.CONTAINS
                    pattern = SearchPattern(
                        pattern=term,
                        mode=mode,
                        case_sensitive=case_sensitive
                    )
                    patterns.append(pattern)
            
            if self._cancelled:
                return
            
            self.progress.emit("开始搜索文件...", 10, 100)
            
            # 执行搜索
            def progress_callback(message):
                if not self._cancelled:
                    self.progress.emit(message, 50, 100)
            
            results = self.search_engine.search_optimized(
                search_path=search_path,
                patterns=patterns,
                extensions=extensions,
                progress_callback=progress_callback
            )
            
            if self._cancelled:
                return
            
            self.progress.emit("处理搜索结果...", 80, 100)
            
            # 获取统计信息
            statistics = self.search_engine.get_search_statistics(results)
            
            # 添加搜索时间
            search_time = time.time() - start_time
            statistics['search_time'] = search_time
            
            self.progress.emit("搜索完成", 100, 100)
            
            # 发送结果
            self.finished.emit(results, statistics, search_time)
            
        except Exception as e:
            self.error.emit(f"搜索出错: {str(e)}")


class OptimizedBatchSearchApp(QMainWindow):
    """优化的批量搜索应用"""
    
    def __init__(self):
        super().__init__()
        self.init_components()
        self.init_ui()
        self.load_settings()
        
        # 搜索线程
        self.search_thread = None
        
        # 内存监控
        self.memory_monitor = MemoryMonitor()
        self.memory_monitor.start_monitoring(self.on_memory_update)
    
    def init_components(self):
        """初始化组件"""
        self.history_manager = SearchHistoryManager()
        self.search_engine = OptimizedSearchEngine()
        self.file_processor = StreamingFileProcessor()

        # 配置管理
        self.config = configparser.ConfigParser()
        self.config_file = "batch_search_config.ini"
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("批量搜索工具 - 优化版")
        self.setGeometry(300, 200, 1200, 800)
        self.setMinimumSize(1000, 700)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建顶部设置区域
        self.create_settings_area(main_layout)
        
        # 创建主要工作区域
        self.create_main_area(main_layout)
        
        # 创建底部操作区域
        self.create_bottom_operations(main_layout)

        # 创建底部状态区域
        self.create_status_area(main_layout)

        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_settings_area(self, parent_layout):
        """创建设置区域"""
        settings_group = QGroupBox("搜索设置")
        settings_layout = QFormLayout()
        
        # 搜索路径
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_path)
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_btn)
        settings_layout.addRow("搜索路径:", path_layout)
        
        # 搜索选项
        options_layout = QHBoxLayout()
        
        self.case_sensitive_check = QCheckBox("区分大小写")
        self.fuzzy_search_check = QCheckBox("模糊搜索")
        self.advanced_search_check = QCheckBox("高级搜索")
        self.color_match_check = QCheckBox("色彩匹配")
        self.color_match_check.setChecked(True)
        
        options_layout.addWidget(self.case_sensitive_check)
        options_layout.addWidget(self.fuzzy_search_check)
        options_layout.addWidget(self.advanced_search_check)
        options_layout.addWidget(self.color_match_check)
        options_layout.addStretch()
        
        settings_layout.addRow("选项:", options_layout)
        
        # 搜索模式
        mode_layout = QHBoxLayout()
        self.search_mode_combo = QComboBox()
        self.search_mode_combo.addItems([
            "包含匹配", "精确匹配", "正则表达式", "通配符", 
            "开头匹配", "结尾匹配", "排除匹配"
        ])
        mode_layout.addWidget(self.search_mode_combo)
        
        # 内存限制
        self.memory_limit_spin = QSpinBox()
        self.memory_limit_spin.setRange(100, 2000)
        self.memory_limit_spin.setValue(500)
        self.memory_limit_spin.setSuffix(" MB")
        mode_layout.addWidget(QLabel("内存限制:"))
        mode_layout.addWidget(self.memory_limit_spin)
        mode_layout.addStretch()
        
        settings_layout.addRow("搜索模式:", mode_layout)
        
        settings_group.setLayout(settings_layout)
        parent_layout.addWidget(settings_group)
    
    def create_main_area(self, parent_layout):
        """创建主要工作区域"""
        # 创建水平分割器
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割比例
        main_splitter.setSizes([400, 800])
        
        parent_layout.addWidget(main_splitter)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout()
        
        # 搜索输入区域
        input_group = QGroupBox("搜索内容")
        input_layout = QVBoxLayout()
        
        self.input_text = QTextEdit()
        self.input_text.setMaximumHeight(150)
        self.input_text.setPlaceholderText("每行一个搜索词，支持高级搜索语法:\n"
                                          "regex:pattern - 正则表达式\n"
                                          "exact:word - 精确匹配\n"
                                          "wild:*.txt - 通配符\n"
                                          "exclude:temp - 排除匹配")
        input_layout.addWidget(self.input_text)
        
        # 搜索按钮
        button_layout = QHBoxLayout()
        self.search_btn = QPushButton("开始搜索")
        self.search_btn.clicked.connect(self.start_search)
        self.cancel_btn = QPushButton("取消搜索")
        self.cancel_btn.clicked.connect(self.cancel_search)
        self.cancel_btn.setEnabled(False)
        
        button_layout.addWidget(self.search_btn)
        button_layout.addWidget(self.cancel_btn)
        input_layout.addLayout(button_layout)
        
        input_group.setLayout(input_layout)
        left_layout.addWidget(input_group)
        
        # 文件扩展名选择
        ext_group = QGroupBox("文件类型")
        ext_layout = QVBoxLayout()

        # 操作按钮行
        button_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_extensions)
        self.select_none_btn = QPushButton("全不选")
        self.select_none_btn.clicked.connect(self.select_none_extensions)

        button_layout.addWidget(self.select_all_btn)
        button_layout.addWidget(self.select_none_btn)
        button_layout.addStretch()
        ext_layout.addLayout(button_layout)

        # 扩展名网格布局
        self.ext_checkboxes = {}
        self.ext_grid_widget = QWidget()
        self.ext_grid_layout = QGridLayout()
        self.ext_grid_widget.setLayout(self.ext_grid_layout)

        # 滚动区域
        ext_scroll = QScrollArea()
        ext_scroll.setWidget(self.ext_grid_widget)
        ext_scroll.setWidgetResizable(True)
        ext_scroll.setMaximumHeight(120)
        ext_layout.addWidget(ext_scroll)

        # 自定义扩展名输入
        custom_layout = QHBoxLayout()
        self.custom_ext_input = QLineEdit()
        self.custom_ext_input.setPlaceholderText("输入新扩展名，如 .txt")
        self.add_ext_btn = QPushButton("添加")
        self.add_ext_btn.clicked.connect(self.add_custom_extension)

        custom_layout.addWidget(QLabel("自定义:"))
        custom_layout.addWidget(self.custom_ext_input)
        custom_layout.addWidget(self.add_ext_btn)
        ext_layout.addLayout(custom_layout)

        ext_group.setLayout(ext_layout)
        left_layout.addWidget(ext_group)

        # 初始化默认扩展名
        self.init_default_extensions()
        
        # 搜索历史
        self.history_widget = SearchHistoryWidget(self.history_manager)
        self.history_widget.history_selected.connect(self.load_from_history)
        left_layout.addWidget(self.history_widget)
        
        left_widget.setLayout(left_layout)
        return left_widget
    
    def create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 搜索结果标签页
        self.result_widget = EnhancedResultWidget()
        self.tab_widget.addTab(self.result_widget, "搜索结果")
        
        # 统计信息标签页
        self.stats_widget = SearchStatisticsWidget()
        self.tab_widget.addTab(self.stats_widget, "统计信息")
        
        right_layout.addWidget(self.tab_widget)
        right_widget.setLayout(right_layout)
        return right_widget

    def create_bottom_operations(self, parent_layout):
        """创建底部操作区域"""
        operations_group = QGroupBox("文件操作")
        operations_layout = QFormLayout()

        # 另存为操作
        save_layout = QHBoxLayout()
        self.save_path_edit = QLineEdit()
        self.save_path_edit.setPlaceholderText("选择文件另存目录")
        self.save_path_btn = QPushButton("浏览")
        self.save_path_btn.clicked.connect(self.browse_save_path)
        self.save_files_btn = QPushButton("另存选中文件")
        self.save_files_btn.clicked.connect(self.save_selected_files)
        self.save_files_btn.setEnabled(False)  # 初始禁用

        save_layout.addWidget(self.save_path_edit)
        save_layout.addWidget(self.save_path_btn)
        save_layout.addWidget(self.save_files_btn)
        operations_layout.addRow("另存为:", save_layout)

        # 另存选项
        save_options_layout = QHBoxLayout()
        self.keep_structure_check = QCheckBox("保持目录结构")
        self.keep_structure_check.setChecked(True)
        self.overwrite_check = QCheckBox("覆盖已存在文件")
        self.overwrite_check.setChecked(False)
        self.save_all_btn = QPushButton("另存全部文件")
        self.save_all_btn.clicked.connect(self.save_all_files)
        self.save_all_btn.setEnabled(False)  # 初始禁用

        save_options_layout.addWidget(self.keep_structure_check)
        save_options_layout.addWidget(self.overwrite_check)
        save_options_layout.addStretch()
        save_options_layout.addWidget(self.save_all_btn)
        operations_layout.addRow("选项:", save_options_layout)

        operations_group.setLayout(operations_layout)
        parent_layout.addWidget(operations_group)
    
    def create_status_area(self, parent_layout):
        """创建状态区域"""
        # 进度显示
        self.progress_widget = EnhancedProgressWidget()
        parent_layout.addWidget(self.progress_widget)
        
        # 内存使用显示
        memory_layout = QHBoxLayout()
        self.memory_label = QLabel("内存使用: 0 MB")
        self.memory_label.setStyleSheet("color: #666; font-size: 11px;")
        memory_layout.addWidget(self.memory_label)
        memory_layout.addStretch()
        
        memory_widget = QWidget()
        memory_widget.setLayout(memory_layout)
        parent_layout.addWidget(memory_widget)
    
    def init_default_extensions(self):
        """初始化默认扩展名"""
        default_exts = ['.DWG', '.DXF', '.PDF', '.xlsx', '.docx', '.txt', '.jpg', '.png', '.dwg', '.dxf']

        # 从配置文件加载扩展名
        saved_exts = self.load_extensions_from_config()
        if saved_exts:
            # 合并默认扩展名和保存的扩展名
            all_exts = list(set(default_exts + saved_exts))
        else:
            all_exts = default_exts

        self.update_extension_grid(all_exts)

    def update_extension_grid(self, extensions):
        """更新扩展名网格布局"""
        # 清空现有的复选框
        for checkbox in self.ext_checkboxes.values():
            checkbox.setParent(None)
        self.ext_checkboxes.clear()

        # 按3列排列
        columns = 3
        for i, ext in enumerate(sorted(extensions)):
            row = i // columns
            col = i % columns

            checkbox = QCheckBox(ext)
            checkbox.setChecked(True)  # 默认选中
            self.ext_checkboxes[ext] = checkbox
            self.ext_grid_layout.addWidget(checkbox, row, col)

    def add_custom_extension(self):
        """添加自定义扩展名"""
        ext_text = self.custom_ext_input.text().strip()
        if not ext_text:
            return

        # 确保扩展名以点开头
        if not ext_text.startswith('.'):
            ext_text = '.' + ext_text

        # 检查是否已存在
        if ext_text in self.ext_checkboxes:
            QMessageBox.information(self, "提示", f"扩展名 {ext_text} 已存在")
            return

        # 添加新的扩展名
        current_exts = list(self.ext_checkboxes.keys())
        current_exts.append(ext_text)
        self.update_extension_grid(current_exts)

        # 清空输入框
        self.custom_ext_input.clear()

        # 保存到配置文件
        self.save_extensions_to_config()

        QMessageBox.information(self, "成功", f"已添加扩展名: {ext_text}")

    def select_all_extensions(self):
        """全选扩展名"""
        for checkbox in self.ext_checkboxes.values():
            checkbox.setChecked(True)

    def select_none_extensions(self):
        """全不选扩展名"""
        for checkbox in self.ext_checkboxes.values():
            checkbox.setChecked(False)

    def load_extensions_from_config(self) -> List[str]:
        """从配置文件加载扩展名"""
        if not os.path.exists(self.config_file):
            return []

        try:
            self.config.read(self.config_file, encoding='utf-8')
            if self.config.has_section('Extensions'):
                return [ext for ext in self.config.options('Extensions')]
        except Exception:
            pass

        return []

    def save_extensions_to_config(self):
        """保存扩展名到配置文件"""
        try:
            if not self.config.has_section('Extensions'):
                self.config.add_section('Extensions')

            # 清空现有配置
            self.config.remove_section('Extensions')
            self.config.add_section('Extensions')

            # 保存所有扩展名
            for ext in self.ext_checkboxes.keys():
                self.config.set('Extensions', ext, 'true')

            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception:
            pass

    def browse_path(self):
        """浏览搜索路径"""
        path = QFileDialog.getExistingDirectory(self, "选择搜索路径")
        if path:
            self.path_edit.setText(path)

    def browse_save_path(self):
        """浏览另存路径"""
        path = QFileDialog.getExistingDirectory(self, "选择文件另存目录")
        if path:
            self.save_path_edit.setText(path)

    def save_selected_files(self):
        """另存选中的文件"""
        save_path = self.save_path_edit.text().strip()
        if not save_path:
            QMessageBox.critical(self, "错误", "请先选择另存目录")
            return

        if not os.path.exists(save_path):
            QMessageBox.critical(self, "错误", "另存目录不存在")
            return

        # 获取选中的文件
        selected_files = self._get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要另存的文件")
            return

        self._perform_save_operation(selected_files, save_path)

    def save_all_files(self):
        """另存全部搜索结果文件"""
        save_path = self.save_path_edit.text().strip()
        if not save_path:
            QMessageBox.critical(self, "错误", "请先选择另存目录")
            return

        if not os.path.exists(save_path):
            QMessageBox.critical(self, "错误", "另存目录不存在")
            return

        # 获取所有搜索结果文件
        all_files = []
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # 搜索结果标签页
            if hasattr(self.result_widget, 'results_data'):
                all_files = self.result_widget.results_data

        if not all_files:
            QMessageBox.information(self, "提示", "没有搜索结果文件可以另存")
            return

        reply = QMessageBox.question(self, "确认另存",
                                   f"确定要另存全部 {len(all_files)} 个文件吗？",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            self._perform_save_operation(all_files, save_path)

    def _get_selected_files(self):
        """获取选中的文件列表"""
        selected_files = []
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # 搜索结果标签页
            # 检查当前是列表视图还是表格视图
            current_view = self.result_widget.result_stack.currentIndex()

            if current_view == 0:  # 列表视图
                selected_items = self.result_widget.list_widget.selectedItems()
                for item in selected_items:
                    result_data = item.data(Qt.UserRole)
                    if result_data:
                        selected_files.append(result_data)

            elif current_view == 1:  # 表格视图
                selected_rows = set()
                for item in self.result_widget.table_widget.selectedItems():
                    selected_rows.add(item.row())

                for row in selected_rows:
                    if row < len(self.result_widget.results_data):
                        selected_files.append(self.result_widget.results_data[row])

        return selected_files

    def _perform_save_operation(self, files_to_save, save_path):
        """执行另存操作"""
        if not files_to_save:
            return

        # 创建进度对话框
        from PyQt5.QtWidgets import QProgressDialog
        progress_dialog = QProgressDialog("正在另存文件...", "取消", 0, len(files_to_save), self)
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.show()

        saved_count = 0
        failed_files = []

        keep_structure = self.keep_structure_check.isChecked()
        overwrite = self.overwrite_check.isChecked()

        for i, file_data in enumerate(files_to_save):
            if progress_dialog.wasCanceled():
                break

            progress_dialog.setValue(i)
            progress_dialog.setLabelText(f"正在处理: {file_data['filename']}")
            QApplication.processEvents()

            try:
                source_path = file_data['file_path']
                if not os.path.exists(source_path):
                    failed_files.append(f"{file_data['filename']} - 源文件不存在")
                    continue

                # 确定目标路径
                if keep_structure:
                    # 保持目录结构
                    relative_path = file_data.get('relative_path', '')
                    if relative_path and relative_path != '.':
                        target_dir = os.path.join(save_path, relative_path)
                    else:
                        target_dir = save_path
                else:
                    # 所有文件保存到同一目录
                    target_dir = save_path

                # 创建目标目录
                os.makedirs(target_dir, exist_ok=True)

                target_path = os.path.join(target_dir, file_data['filename'])

                # 检查文件是否已存在
                if os.path.exists(target_path) and not overwrite:
                    # 生成新的文件名
                    base_name, ext = os.path.splitext(file_data['filename'])
                    counter = 1
                    while os.path.exists(target_path):
                        new_name = f"{base_name}_{counter}{ext}"
                        target_path = os.path.join(target_dir, new_name)
                        counter += 1

                # 复制文件
                shutil.copy2(source_path, target_path)
                saved_count += 1

            except Exception as e:
                failed_files.append(f"{file_data['filename']} - {str(e)}")

        progress_dialog.setValue(len(files_to_save))
        progress_dialog.close()

        # 显示结果
        message = f"另存操作完成！\n成功另存: {saved_count} 个文件"
        if failed_files:
            message += f"\n失败: {len(failed_files)} 个文件"
            if len(failed_files) <= 5:
                message += "\n失败文件:\n" + "\n".join(failed_files)
            else:
                message += f"\n失败文件:\n" + "\n".join(failed_files[:5]) + f"\n... 还有 {len(failed_files)-5} 个"

        QMessageBox.information(self, "另存完成", message)
    
    def load_from_history(self, query: str, path: str, extensions: List[str]):
        """从历史记录加载搜索"""
        self.input_text.setPlainText(query)
        self.path_edit.setText(path)
        
        # 更新扩展名选择
        for ext, checkbox in self.ext_checkboxes.items():
            checkbox.setChecked(ext in extensions)
    
    def start_search(self):
        """开始搜索"""
        # 验证输入
        search_terms = [term.strip() for term in self.input_text.toPlainText().splitlines() if term.strip()]
        search_path = self.path_edit.text().strip()
        selected_exts = [ext for ext, checkbox in self.ext_checkboxes.items() if checkbox.isChecked()]
        
        if not search_path:
            QMessageBox.critical(self, "错误", "请选择搜索路径")
            return
        
        if not search_terms:
            QMessageBox.critical(self, "错误", "请输入搜索内容")
            return
        
        if not selected_exts:
            QMessageBox.critical(self, "错误", "请至少选择一个文件类型")
            return
        
        # 准备搜索参数
        search_params = {
            'search_terms': search_terms,
            'search_path': search_path,
            'extensions': selected_exts,
            'search_mode': self.get_search_mode(),
            'case_sensitive': self.case_sensitive_check.isChecked(),
            'enable_fuzzy': self.fuzzy_search_check.isChecked(),
            'enable_advanced': self.advanced_search_check.isChecked()
        }
        
        # 更新UI状态
        self.search_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        
        # 开始进度显示
        self.progress_widget.start_progress("准备搜索...")
        
        # 创建并启动搜索线程
        self.search_thread = OptimizedSearchThread(search_params)
        self.search_thread.finished.connect(self.on_search_finished)
        self.search_thread.progress.connect(self.on_search_progress)
        self.search_thread.error.connect(self.on_search_error)
        self.search_thread.start()
    
    def cancel_search(self):
        """取消搜索"""
        if self.search_thread and self.search_thread.isRunning():
            self.search_thread.cancel()
            self.search_thread.wait(3000)  # 等待3秒
        
        self.reset_search_ui()
    
    def get_search_mode(self) -> str:
        """获取搜索模式"""
        mode_map = {
            "包含匹配": "contains",
            "精确匹配": "exact",
            "正则表达式": "regex",
            "通配符": "wildcard",
            "开头匹配": "starts_with",
            "结尾匹配": "ends_with",
            "排除匹配": "exclude"
        }
        return mode_map.get(self.search_mode_combo.currentText(), "contains")
    
    def on_search_progress(self, message: str, current: int, total: int):
        """搜索进度更新"""
        self.progress_widget.update_progress(message, current, total)
        self.statusBar().showMessage(message)
    
    def on_search_finished(self, results: Dict, statistics: Dict, search_time: float):
        """搜索完成"""
        self.reset_search_ui()
        
        # 处理结果
        total_matches = sum(len(matches) for matches in results.values())
        
        # 转换结果格式用于显示
        display_results = []
        for pattern, matches in results.items():
            for match in matches:
                display_results.append({
                    'filename': match.filename,
                    'file_path': match.file_path,
                    'relative_path': match.relative_path,
                    'file_size': match.file_size,
                    'modified_time': match.modified_time,
                    'matched_patterns': match.matched_patterns
                })
        
        # 更新结果显示
        self.result_widget.update_results(display_results)
        
        # 更新统计信息
        self.stats_widget.update_statistics(statistics)
        
        # 添加到搜索历史
        search_terms = [term.strip() for term in self.input_text.toPlainText().splitlines() if term.strip()]
        search_path = self.path_edit.text().strip()
        selected_exts = [ext for ext, checkbox in self.ext_checkboxes.items() if checkbox.isChecked()]
        
        self.history_manager.add_search(
            query='\n'.join(search_terms),
            path=search_path,
            results_count=total_matches,
            search_time=search_time,
            extensions=selected_exts
        )
        
        # 刷新历史显示
        self.history_widget.refresh_history()
        
        # 完成进度显示
        self.progress_widget.finish_progress(f"搜索完成，找到 {total_matches} 个匹配文件")
        
        # 显示结果消息
        QMessageBox.information(self, "搜索完成", 
                               f"搜索完成！\n"
                               f"匹配文件: {total_matches} 个\n"
                               f"搜索耗时: {search_time:.2f} 秒")
    
    def on_search_error(self, error_message: str):
        """搜索错误"""
        self.reset_search_ui()
        self.progress_widget.finish_progress("搜索出错")
        QMessageBox.critical(self, "搜索错误", error_message)
    
    def reset_search_ui(self):
        """重置搜索UI状态"""
        self.search_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
    
    def on_memory_update(self, memory_mb: float):
        """内存使用更新"""
        self.memory_label.setText(f"内存使用: {memory_mb:.1f} MB")
        
        # 如果内存使用过高，显示警告颜色
        if memory_mb > self.memory_limit_spin.value():
            self.memory_label.setStyleSheet("color: red; font-size: 11px;")
        else:
            self.memory_label.setStyleSheet("color: #666; font-size: 11px;")
    
    def load_settings(self):
        """加载设置"""
        if not os.path.exists(self.config_file):
            return

        try:
            self.config.read(self.config_file, encoding='utf-8')

            # 加载搜索路径
            if self.config.has_option('Settings', 'search_path'):
                self.path_edit.setText(self.config.get('Settings', 'search_path'))

            # 加载搜索选项
            if self.config.has_option('Settings', 'case_sensitive'):
                self.case_sensitive_check.setChecked(self.config.getboolean('Settings', 'case_sensitive'))

            if self.config.has_option('Settings', 'fuzzy_search'):
                self.fuzzy_search_check.setChecked(self.config.getboolean('Settings', 'fuzzy_search'))

            if self.config.has_option('Settings', 'advanced_search'):
                self.advanced_search_check.setChecked(self.config.getboolean('Settings', 'advanced_search'))

            if self.config.has_option('Settings', 'color_match'):
                self.color_match_check.setChecked(self.config.getboolean('Settings', 'color_match'))

            # 加载搜索模式
            if self.config.has_option('Settings', 'search_mode'):
                mode_text = self.config.get('Settings', 'search_mode')
                index = self.search_mode_combo.findText(mode_text)
                if index >= 0:
                    self.search_mode_combo.setCurrentIndex(index)

            # 加载内存限制
            if self.config.has_option('Settings', 'memory_limit'):
                self.memory_limit_spin.setValue(self.config.getint('Settings', 'memory_limit'))

            # 加载扩展名选择状态
            if self.config.has_section('ExtensionStates'):
                for ext, checkbox in self.ext_checkboxes.items():
                    if self.config.has_option('ExtensionStates', ext):
                        checkbox.setChecked(self.config.getboolean('ExtensionStates', ext))

        except Exception:
            pass

    def save_settings(self):
        """保存设置"""
        try:
            if not self.config.has_section('Settings'):
                self.config.add_section('Settings')

            # 保存搜索设置
            self.config.set('Settings', 'search_path', self.path_edit.text())
            self.config.set('Settings', 'case_sensitive', str(self.case_sensitive_check.isChecked()))
            self.config.set('Settings', 'fuzzy_search', str(self.fuzzy_search_check.isChecked()))
            self.config.set('Settings', 'advanced_search', str(self.advanced_search_check.isChecked()))
            self.config.set('Settings', 'color_match', str(self.color_match_check.isChecked()))
            self.config.set('Settings', 'search_mode', self.search_mode_combo.currentText())
            self.config.set('Settings', 'memory_limit', str(self.memory_limit_spin.value()))

            # 保存扩展名选择状态
            if not self.config.has_section('ExtensionStates'):
                self.config.add_section('ExtensionStates')

            for ext, checkbox in self.ext_checkboxes.items():
                self.config.set('ExtensionStates', ext, str(checkbox.isChecked()))

            # 保存扩展名列表
            self.save_extensions_to_config()

            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)

        except Exception:
            pass
    
    def closeEvent(self, event):
        """程序关闭事件"""
        # 停止内存监控
        self.memory_monitor.stop_monitoring()
        
        # 取消正在进行的搜索
        if self.search_thread and self.search_thread.isRunning():
            self.search_thread.cancel()
            self.search_thread.wait(3000)
        
        # 保存设置
        self.save_settings()
        
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建并显示主窗口
    window = OptimizedBatchSearchApp()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
