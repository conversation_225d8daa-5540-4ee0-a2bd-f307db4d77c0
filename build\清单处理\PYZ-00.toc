('C:\\Users\\<USER>\\Desktop\\python工具箱\\build\\清单处理\\PYZ-00.pyz',
 [('PIL',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\Miniconda3\\envs\\Excel\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Miniconda3\\envs\\Excel\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Miniconda3\\envs\\Excel\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Miniconda3\\envs\\Excel\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Miniconda3\\envs\\Excel\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Miniconda3\\envs\\Excel\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\Miniconda3\\envs\\Excel\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Miniconda3\\envs\\Excel\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Miniconda3\\envs\\Excel\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Miniconda3\\envs\\Excel\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Miniconda3\\envs\\Excel\\lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'D:\\Miniconda3\\envs\\Excel\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Miniconda3\\envs\\Excel\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Miniconda3\\envs\\Excel\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\Miniconda3\\envs\\Excel\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Miniconda3\\envs\\Excel\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Miniconda3\\envs\\Excel\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Miniconda3\\envs\\Excel\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Miniconda3\\envs\\Excel\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Miniconda3\\envs\\Excel\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Miniconda3\\envs\\Excel\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\Miniconda3\\envs\\Excel\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Miniconda3\\envs\\Excel\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Miniconda3\\envs\\Excel\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\Miniconda3\\envs\\Excel\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\Miniconda3\\envs\\Excel\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('ezdxf',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\__init__.py',
   'PYMODULE'),
  ('ezdxf._options',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\_options.py',
   'PYMODULE'),
  ('ezdxf.acc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\__init__.py',
   'PYMODULE'),
  ('ezdxf.acis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\__init__.py',
   'PYMODULE'),
  ('ezdxf.acis.abstract',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\abstract.py',
   'PYMODULE'),
  ('ezdxf.acis.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\api.py',
   'PYMODULE'),
  ('ezdxf.acis.cache',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\cache.py',
   'PYMODULE'),
  ('ezdxf.acis.const',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\const.py',
   'PYMODULE'),
  ('ezdxf.acis.dbg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\dbg.py',
   'PYMODULE'),
  ('ezdxf.acis.dxf',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\dxf.py',
   'PYMODULE'),
  ('ezdxf.acis.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\entities.py',
   'PYMODULE'),
  ('ezdxf.acis.hdr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\hdr.py',
   'PYMODULE'),
  ('ezdxf.acis.mesh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\mesh.py',
   'PYMODULE'),
  ('ezdxf.acis.sab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\sab.py',
   'PYMODULE'),
  ('ezdxf.acis.sat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\sat.py',
   'PYMODULE'),
  ('ezdxf.acis.type_hints',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\type_hints.py',
   'PYMODULE'),
  ('ezdxf.addons',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\__init__.py',
   'PYMODULE'),
  ('ezdxf.addons.dimlines',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\dimlines.py',
   'PYMODULE'),
  ('ezdxf.addons.importer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\importer.py',
   'PYMODULE'),
  ('ezdxf.addons.menger_sponge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\menger_sponge.py',
   'PYMODULE'),
  ('ezdxf.addons.mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\mixins.py',
   'PYMODULE'),
  ('ezdxf.addons.mtextsurrogate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\mtextsurrogate.py',
   'PYMODULE'),
  ('ezdxf.addons.mtxpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\mtxpl.py',
   'PYMODULE'),
  ('ezdxf.addons.r12writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\r12writer.py',
   'PYMODULE'),
  ('ezdxf.addons.sierpinski_pyramid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\sierpinski_pyramid.py',
   'PYMODULE'),
  ('ezdxf.addons.tablepainter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\tablepainter.py',
   'PYMODULE'),
  ('ezdxf.addons.xqt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\xqt.py',
   'PYMODULE'),
  ('ezdxf.audit',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\audit.py',
   'PYMODULE'),
  ('ezdxf.bbox',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\bbox.py',
   'PYMODULE'),
  ('ezdxf.colors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\colors.py',
   'PYMODULE'),
  ('ezdxf.disassemble',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\disassemble.py',
   'PYMODULE'),
  ('ezdxf.document',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\document.py',
   'PYMODULE'),
  ('ezdxf.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\__init__.py',
   'PYMODULE'),
  ('ezdxf.entities.acad_proxy_entity',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\acad_proxy_entity.py',
   'PYMODULE'),
  ('ezdxf.entities.acad_table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\acad_table.py',
   'PYMODULE'),
  ('ezdxf.entities.acis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\acis.py',
   'PYMODULE'),
  ('ezdxf.entities.appdata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\appdata.py',
   'PYMODULE'),
  ('ezdxf.entities.appid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\appid.py',
   'PYMODULE'),
  ('ezdxf.entities.arc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\arc.py',
   'PYMODULE'),
  ('ezdxf.entities.attrib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\attrib.py',
   'PYMODULE'),
  ('ezdxf.entities.block',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\block.py',
   'PYMODULE'),
  ('ezdxf.entities.blockrecord',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\blockrecord.py',
   'PYMODULE'),
  ('ezdxf.entities.boundary_paths',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\boundary_paths.py',
   'PYMODULE'),
  ('ezdxf.entities.circle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\circle.py',
   'PYMODULE'),
  ('ezdxf.entities.copy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\copy.py',
   'PYMODULE'),
  ('ezdxf.entities.dictionary',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dictionary.py',
   'PYMODULE'),
  ('ezdxf.entities.dimension',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dimension.py',
   'PYMODULE'),
  ('ezdxf.entities.dimstyle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dimstyle.py',
   'PYMODULE'),
  ('ezdxf.entities.dimstyleoverride',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dimstyleoverride.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfclass',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfclass.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfentity',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfentity.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfgfx',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfgfx.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfgroups',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfgroups.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfns',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfns.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfobj',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfobj.py',
   'PYMODULE'),
  ('ezdxf.entities.ellipse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\ellipse.py',
   'PYMODULE'),
  ('ezdxf.entities.factory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\factory.py',
   'PYMODULE'),
  ('ezdxf.entities.geodata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\geodata.py',
   'PYMODULE'),
  ('ezdxf.entities.gradient',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\gradient.py',
   'PYMODULE'),
  ('ezdxf.entities.hatch',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\hatch.py',
   'PYMODULE'),
  ('ezdxf.entities.helix',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\helix.py',
   'PYMODULE'),
  ('ezdxf.entities.idbuffer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\idbuffer.py',
   'PYMODULE'),
  ('ezdxf.entities.image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\image.py',
   'PYMODULE'),
  ('ezdxf.entities.insert',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\insert.py',
   'PYMODULE'),
  ('ezdxf.entities.layer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\layer.py',
   'PYMODULE'),
  ('ezdxf.entities.layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\layout.py',
   'PYMODULE'),
  ('ezdxf.entities.leader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\leader.py',
   'PYMODULE'),
  ('ezdxf.entities.light',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\light.py',
   'PYMODULE'),
  ('ezdxf.entities.line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\line.py',
   'PYMODULE'),
  ('ezdxf.entities.ltype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\ltype.py',
   'PYMODULE'),
  ('ezdxf.entities.lwpolyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\lwpolyline.py',
   'PYMODULE'),
  ('ezdxf.entities.material',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\material.py',
   'PYMODULE'),
  ('ezdxf.entities.mesh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mesh.py',
   'PYMODULE'),
  ('ezdxf.entities.mleader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mleader.py',
   'PYMODULE'),
  ('ezdxf.entities.mline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mline.py',
   'PYMODULE'),
  ('ezdxf.entities.mpolygon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mpolygon.py',
   'PYMODULE'),
  ('ezdxf.entities.mtext',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mtext.py',
   'PYMODULE'),
  ('ezdxf.entities.mtext_columns',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mtext_columns.py',
   'PYMODULE'),
  ('ezdxf.entities.objectcollection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\objectcollection.py',
   'PYMODULE'),
  ('ezdxf.entities.oleframe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\oleframe.py',
   'PYMODULE'),
  ('ezdxf.entities.pattern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\pattern.py',
   'PYMODULE'),
  ('ezdxf.entities.point',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\point.py',
   'PYMODULE'),
  ('ezdxf.entities.polygon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\polygon.py',
   'PYMODULE'),
  ('ezdxf.entities.polyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\polyline.py',
   'PYMODULE'),
  ('ezdxf.entities.shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\shape.py',
   'PYMODULE'),
  ('ezdxf.entities.solid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\solid.py',
   'PYMODULE'),
  ('ezdxf.entities.spatial_filter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\spatial_filter.py',
   'PYMODULE'),
  ('ezdxf.entities.spline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\spline.py',
   'PYMODULE'),
  ('ezdxf.entities.subentity',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\subentity.py',
   'PYMODULE'),
  ('ezdxf.entities.sun',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\sun.py',
   'PYMODULE'),
  ('ezdxf.entities.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\table.py',
   'PYMODULE'),
  ('ezdxf.entities.temporary_transform',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\temporary_transform.py',
   'PYMODULE'),
  ('ezdxf.entities.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\text.py',
   'PYMODULE'),
  ('ezdxf.entities.textstyle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\textstyle.py',
   'PYMODULE'),
  ('ezdxf.entities.tolerance',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\tolerance.py',
   'PYMODULE'),
  ('ezdxf.entities.ucs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\ucs.py',
   'PYMODULE'),
  ('ezdxf.entities.underlay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\underlay.py',
   'PYMODULE'),
  ('ezdxf.entities.view',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\view.py',
   'PYMODULE'),
  ('ezdxf.entities.viewport',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\viewport.py',
   'PYMODULE'),
  ('ezdxf.entities.visualstyle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\visualstyle.py',
   'PYMODULE'),
  ('ezdxf.entities.vport',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\vport.py',
   'PYMODULE'),
  ('ezdxf.entities.xdata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\xdata.py',
   'PYMODULE'),
  ('ezdxf.entities.xdict',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\xdict.py',
   'PYMODULE'),
  ('ezdxf.entities.xline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\xline.py',
   'PYMODULE'),
  ('ezdxf.entitydb',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entitydb.py',
   'PYMODULE'),
  ('ezdxf.enums',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\enums.py',
   'PYMODULE'),
  ('ezdxf.explode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\explode.py',
   'PYMODULE'),
  ('ezdxf.eztypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\eztypes.py',
   'PYMODULE'),
  ('ezdxf.filemanagement',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\filemanagement.py',
   'PYMODULE'),
  ('ezdxf.fonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\__init__.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_face',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_face.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_manager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_manager.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_measurements',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_measurements.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_synonyms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_synonyms.py',
   'PYMODULE'),
  ('ezdxf.fonts.fonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\fonts.py',
   'PYMODULE'),
  ('ezdxf.fonts.glyphs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\glyphs.py',
   'PYMODULE'),
  ('ezdxf.fonts.lff',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\lff.py',
   'PYMODULE'),
  ('ezdxf.fonts.shapefile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\shapefile.py',
   'PYMODULE'),
  ('ezdxf.fonts.ttfonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\ttfonts.py',
   'PYMODULE'),
  ('ezdxf.graphicsfactory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\graphicsfactory.py',
   'PYMODULE'),
  ('ezdxf.groupby',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\groupby.py',
   'PYMODULE'),
  ('ezdxf.layouts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\__init__.py',
   'PYMODULE'),
  ('ezdxf.layouts.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\base.py',
   'PYMODULE'),
  ('ezdxf.layouts.blocklayout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\blocklayout.py',
   'PYMODULE'),
  ('ezdxf.layouts.layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\layout.py',
   'PYMODULE'),
  ('ezdxf.layouts.layouts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\layouts.py',
   'PYMODULE'),
  ('ezdxf.lldxf',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\__init__.py',
   'PYMODULE'),
  ('ezdxf.lldxf.attributes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\attributes.py',
   'PYMODULE'),
  ('ezdxf.lldxf.const',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\const.py',
   'PYMODULE'),
  ('ezdxf.lldxf.encoding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\encoding.py',
   'PYMODULE'),
  ('ezdxf.lldxf.extendedtags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\extendedtags.py',
   'PYMODULE'),
  ('ezdxf.lldxf.hdrvars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\hdrvars.py',
   'PYMODULE'),
  ('ezdxf.lldxf.loader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\loader.py',
   'PYMODULE'),
  ('ezdxf.lldxf.packedtags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\packedtags.py',
   'PYMODULE'),
  ('ezdxf.lldxf.repair',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\repair.py',
   'PYMODULE'),
  ('ezdxf.lldxf.tagger',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\tagger.py',
   'PYMODULE'),
  ('ezdxf.lldxf.tags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\tags.py',
   'PYMODULE'),
  ('ezdxf.lldxf.tagwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\tagwriter.py',
   'PYMODULE'),
  ('ezdxf.lldxf.types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\types.py',
   'PYMODULE'),
  ('ezdxf.lldxf.validator',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\validator.py',
   'PYMODULE'),
  ('ezdxf.math',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\__init__.py',
   'PYMODULE'),
  ('ezdxf.math._bezier3p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_bezier3p.py',
   'PYMODULE'),
  ('ezdxf.math._bezier4p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_bezier4p.py',
   'PYMODULE'),
  ('ezdxf.math._bspline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_bspline.py',
   'PYMODULE'),
  ('ezdxf.math._construct',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_construct.py',
   'PYMODULE'),
  ('ezdxf.math._ctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_ctypes.py',
   'PYMODULE'),
  ('ezdxf.math._mapbox_earcut',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_mapbox_earcut.py',
   'PYMODULE'),
  ('ezdxf.math._matrix44',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_matrix44.py',
   'PYMODULE'),
  ('ezdxf.math._vector',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_vector.py',
   'PYMODULE'),
  ('ezdxf.math.arc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\arc.py',
   'PYMODULE'),
  ('ezdxf.math.bbox',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bbox.py',
   'PYMODULE'),
  ('ezdxf.math.bezier',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bezier.py',
   'PYMODULE'),
  ('ezdxf.math.bezier_interpolation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bezier_interpolation.py',
   'PYMODULE'),
  ('ezdxf.math.box',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\box.py',
   'PYMODULE'),
  ('ezdxf.math.bspline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bspline.py',
   'PYMODULE'),
  ('ezdxf.math.bulge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bulge.py',
   'PYMODULE'),
  ('ezdxf.math.circle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\circle.py',
   'PYMODULE'),
  ('ezdxf.math.construct2d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\construct2d.py',
   'PYMODULE'),
  ('ezdxf.math.construct3d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\construct3d.py',
   'PYMODULE'),
  ('ezdxf.math.curvetools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\curvetools.py',
   'PYMODULE'),
  ('ezdxf.math.ellipse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\ellipse.py',
   'PYMODULE'),
  ('ezdxf.math.eulerspiral',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\eulerspiral.py',
   'PYMODULE'),
  ('ezdxf.math.linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\linalg.py',
   'PYMODULE'),
  ('ezdxf.math.line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\line.py',
   'PYMODULE'),
  ('ezdxf.math.offset2d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\offset2d.py',
   'PYMODULE'),
  ('ezdxf.math.parametrize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\parametrize.py',
   'PYMODULE'),
  ('ezdxf.math.perlin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\perlin.py',
   'PYMODULE'),
  ('ezdxf.math.polyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\polyline.py',
   'PYMODULE'),
  ('ezdxf.math.shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\shape.py',
   'PYMODULE'),
  ('ezdxf.math.transformtools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\transformtools.py',
   'PYMODULE'),
  ('ezdxf.math.triangulation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\triangulation.py',
   'PYMODULE'),
  ('ezdxf.math.ucs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\ucs.py',
   'PYMODULE'),
  ('ezdxf.messenger',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\messenger.py',
   'PYMODULE'),
  ('ezdxf.msgtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\msgtypes.py',
   'PYMODULE'),
  ('ezdxf.npshapes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\npshapes.py',
   'PYMODULE'),
  ('ezdxf.path',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\__init__.py',
   'PYMODULE'),
  ('ezdxf.path.commands',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\commands.py',
   'PYMODULE'),
  ('ezdxf.path.converter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\converter.py',
   'PYMODULE'),
  ('ezdxf.path.nesting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\nesting.py',
   'PYMODULE'),
  ('ezdxf.path.path',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\path.py',
   'PYMODULE'),
  ('ezdxf.path.shapes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\shapes.py',
   'PYMODULE'),
  ('ezdxf.path.tools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\tools.py',
   'PYMODULE'),
  ('ezdxf.protocols',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\protocols.py',
   'PYMODULE'),
  ('ezdxf.proxygraphic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\proxygraphic.py',
   'PYMODULE'),
  ('ezdxf.query',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\query.py',
   'PYMODULE'),
  ('ezdxf.queryparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\queryparser.py',
   'PYMODULE'),
  ('ezdxf.render',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\__init__.py',
   'PYMODULE'),
  ('ezdxf.render.abstract_mtext_renderer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\abstract_mtext_renderer.py',
   'PYMODULE'),
  ('ezdxf.render.arrows',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\arrows.py',
   'PYMODULE'),
  ('ezdxf.render.curves',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\curves.py',
   'PYMODULE'),
  ('ezdxf.render.dim_base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_base.py',
   'PYMODULE'),
  ('ezdxf.render.dim_curved',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_curved.py',
   'PYMODULE'),
  ('ezdxf.render.dim_diameter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_diameter.py',
   'PYMODULE'),
  ('ezdxf.render.dim_linear',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_linear.py',
   'PYMODULE'),
  ('ezdxf.render.dim_ordinate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_ordinate.py',
   'PYMODULE'),
  ('ezdxf.render.dim_radius',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_radius.py',
   'PYMODULE'),
  ('ezdxf.render.dimension',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dimension.py',
   'PYMODULE'),
  ('ezdxf.render.forms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\forms.py',
   'PYMODULE'),
  ('ezdxf.render.hatching',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\hatching.py',
   'PYMODULE'),
  ('ezdxf.render.leader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\leader.py',
   'PYMODULE'),
  ('ezdxf.render.mesh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\mesh.py',
   'PYMODULE'),
  ('ezdxf.render.mleader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\mleader.py',
   'PYMODULE'),
  ('ezdxf.render.mline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\mline.py',
   'PYMODULE'),
  ('ezdxf.render.point',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\point.py',
   'PYMODULE'),
  ('ezdxf.render.polyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\polyline.py',
   'PYMODULE'),
  ('ezdxf.render.r12spline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\r12spline.py',
   'PYMODULE'),
  ('ezdxf.render.trace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\trace.py',
   'PYMODULE'),
  ('ezdxf.reorder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\reorder.py',
   'PYMODULE'),
  ('ezdxf.sections',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\__init__.py',
   'PYMODULE'),
  ('ezdxf.sections.acdsdata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\acdsdata.py',
   'PYMODULE'),
  ('ezdxf.sections.blocks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\blocks.py',
   'PYMODULE'),
  ('ezdxf.sections.classes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\classes.py',
   'PYMODULE'),
  ('ezdxf.sections.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\entities.py',
   'PYMODULE'),
  ('ezdxf.sections.header',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\header.py',
   'PYMODULE'),
  ('ezdxf.sections.headervars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\headervars.py',
   'PYMODULE'),
  ('ezdxf.sections.objects',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\objects.py',
   'PYMODULE'),
  ('ezdxf.sections.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\table.py',
   'PYMODULE'),
  ('ezdxf.sections.tables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\tables.py',
   'PYMODULE'),
  ('ezdxf.tools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\__init__.py',
   'PYMODULE'),
  ('ezdxf.tools._iso_pattern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\_iso_pattern.py',
   'PYMODULE'),
  ('ezdxf.tools.binarydata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\binarydata.py',
   'PYMODULE'),
  ('ezdxf.tools.codepage',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\codepage.py',
   'PYMODULE'),
  ('ezdxf.tools.complex_ltype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\complex_ltype.py',
   'PYMODULE'),
  ('ezdxf.tools.crypt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\crypt.py',
   'PYMODULE'),
  ('ezdxf.tools.handle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\handle.py',
   'PYMODULE'),
  ('ezdxf.tools.indexing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\indexing.py',
   'PYMODULE'),
  ('ezdxf.tools.juliandate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\juliandate.py',
   'PYMODULE'),
  ('ezdxf.tools.pattern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\pattern.py',
   'PYMODULE'),
  ('ezdxf.tools.standards',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\standards.py',
   'PYMODULE'),
  ('ezdxf.tools.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\text.py',
   'PYMODULE'),
  ('ezdxf.tools.text_layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\text_layout.py',
   'PYMODULE'),
  ('ezdxf.tools.text_size',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\text_size.py',
   'PYMODULE'),
  ('ezdxf.tools.zipmanager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\zipmanager.py',
   'PYMODULE'),
  ('ezdxf.transform',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\transform.py',
   'PYMODULE'),
  ('ezdxf.units',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\units.py',
   'PYMODULE'),
  ('ezdxf.version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\version.py',
   'PYMODULE'),
  ('ezdxf.xref',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\xref.py',
   'PYMODULE'),
  ('fileinput', 'D:\\Miniconda3\\envs\\Excel\\lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Miniconda3\\envs\\Excel\\lib\\fnmatch.py', 'PYMODULE'),
  ('fontTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\__init__.py',
   'PYMODULE'),
  ('fontTools.agl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\agl.py',
   'PYMODULE'),
  ('fontTools.cffLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFF2ToCFF',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\CFF2ToCFF.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFFToCFF2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\CFFToCFF2.py',
   'PYMODULE'),
  ('fontTools.cffLib.specializer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\specializer.py',
   'PYMODULE'),
  ('fontTools.cffLib.transforms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\transforms.py',
   'PYMODULE'),
  ('fontTools.cffLib.width',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\width.py',
   'PYMODULE'),
  ('fontTools.colorLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.colorLib.builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\errors.py',
   'PYMODULE'),
  ('fontTools.colorLib.geometry',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\geometry.py',
   'PYMODULE'),
  ('fontTools.colorLib.table_builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\table_builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.unbuilder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\unbuilder.py',
   'PYMODULE'),
  ('fontTools.config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\config\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.split',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\split.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.statNames',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\statNames.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\types.py',
   'PYMODULE'),
  ('fontTools.encodings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\encodings\\__init__.py',
   'PYMODULE'),
  ('fontTools.encodings.StandardEncoding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\encodings\\StandardEncoding.py',
   'PYMODULE'),
  ('fontTools.encodings.codecs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\encodings\\codecs.py',
   'PYMODULE'),
  ('fontTools.feaLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.ast',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\ast.py',
   'PYMODULE'),
  ('fontTools.feaLib.error',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\error.py',
   'PYMODULE'),
  ('fontTools.feaLib.location',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\location.py',
   'PYMODULE'),
  ('fontTools.feaLib.lookupDebugInfo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\lookupDebugInfo.py',
   'PYMODULE'),
  ('fontTools.misc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.arrayTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\arrayTools.py',
   'PYMODULE'),
  ('fontTools.misc.classifyTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\classifyTools.py',
   'PYMODULE'),
  ('fontTools.misc.cliTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\cliTools.py',
   'PYMODULE'),
  ('fontTools.misc.configTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\configTools.py',
   'PYMODULE'),
  ('fontTools.misc.cython',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\cython.py',
   'PYMODULE'),
  ('fontTools.misc.dictTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\dictTools.py',
   'PYMODULE'),
  ('fontTools.misc.encodingTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\encodingTools.py',
   'PYMODULE'),
  ('fontTools.misc.etree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\etree.py',
   'PYMODULE'),
  ('fontTools.misc.filenames',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\filenames.py',
   'PYMODULE'),
  ('fontTools.misc.fixedTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\fixedTools.py',
   'PYMODULE'),
  ('fontTools.misc.intTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\intTools.py',
   'PYMODULE'),
  ('fontTools.misc.iterTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\iterTools.py',
   'PYMODULE'),
  ('fontTools.misc.lazyTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\lazyTools.py',
   'PYMODULE'),
  ('fontTools.misc.loggingTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\loggingTools.py',
   'PYMODULE'),
  ('fontTools.misc.macCreatorType',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\macCreatorType.py',
   'PYMODULE'),
  ('fontTools.misc.macRes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\macRes.py',
   'PYMODULE'),
  ('fontTools.misc.plistlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\plistlib\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.psCharStrings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\psCharStrings.py',
   'PYMODULE'),
  ('fontTools.misc.roundTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\roundTools.py',
   'PYMODULE'),
  ('fontTools.misc.sstruct',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\sstruct.py',
   'PYMODULE'),
  ('fontTools.misc.textTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\textTools.py',
   'PYMODULE'),
  ('fontTools.misc.timeTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\timeTools.py',
   'PYMODULE'),
  ('fontTools.misc.transform',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\transform.py',
   'PYMODULE'),
  ('fontTools.misc.treeTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\treeTools.py',
   'PYMODULE'),
  ('fontTools.misc.vector',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\vector.py',
   'PYMODULE'),
  ('fontTools.misc.visitor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\visitor.py',
   'PYMODULE'),
  ('fontTools.misc.xmlReader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\xmlReader.py',
   'PYMODULE'),
  ('fontTools.misc.xmlWriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\xmlWriter.py',
   'PYMODULE'),
  ('fontTools.otlLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\builder.py',
   'PYMODULE'),
  ('fontTools.otlLib.error',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\error.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\optimize\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize.gpos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\optimize\\gpos.py',
   'PYMODULE'),
  ('fontTools.pens',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\__init__.py',
   'PYMODULE'),
  ('fontTools.pens.basePen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\basePen.py',
   'PYMODULE'),
  ('fontTools.pens.boundsPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\boundsPen.py',
   'PYMODULE'),
  ('fontTools.pens.filterPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\filterPen.py',
   'PYMODULE'),
  ('fontTools.pens.pointPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\pointPen.py',
   'PYMODULE'),
  ('fontTools.pens.recordingPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\recordingPen.py',
   'PYMODULE'),
  ('fontTools.pens.reverseContourPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\reverseContourPen.py',
   'PYMODULE'),
  ('fontTools.pens.t2CharStringPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\t2CharStringPen.py',
   'PYMODULE'),
  ('fontTools.pens.transformPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\transformPen.py',
   'PYMODULE'),
  ('fontTools.ttLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.macUtils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\macUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.reorderGlyphs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\reorderGlyphs.py',
   'PYMODULE'),
  ('fontTools.ttLib.sfnt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\sfnt.py',
   'PYMODULE'),
  ('fontTools.ttLib.standardGlyphOrder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\standardGlyphOrder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.B_A_S_E_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\B_A_S_E_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.BitmapGlyphMetrics',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\BitmapGlyphMetrics.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_D_T_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_L_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F__2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_O_L_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_O_L_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_P_A_L_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_P_A_L_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D_S_I_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\D_S_I_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D__e_b_g',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\D__e_b_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.DefaultTable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\DefaultTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_D_T_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_L_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F_F_T_M_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\F_F_T_M_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F__e_a_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\F__e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_D_E_F_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_D_E_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_M_A_P_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_M_A_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_K_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_K_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_O_S_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_O_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_S_U_B_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_S_U_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_a_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_o_c',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_o_c.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.H_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\H_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.J_S_T_F_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\J_S_T_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.L_T_S_H_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\L_T_S_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_A_T_H_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_A_T_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_E_T_A_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_E_T_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.O_S_2f_2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\O_S_2f_2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_I_N_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_I_N_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_T_A_T_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_T_A_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_V_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_V_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_f',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_l',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_l.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_B_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_D_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_D_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_J_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_J_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_P_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_S_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_V_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_V_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__0.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__1',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__1.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__3.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__5.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_T_F_A_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_T_F_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.TupleVariation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\TupleVariation.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_A_R_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_A_R_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_D_M_X_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_D_M_X_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_O_R_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_O_R_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_n_k_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_n_k_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._b_s_l_n',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_b_s_l_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_i_d_g',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_i_d_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_m_a_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_m_a_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_e_a_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_p_g_m',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_p_g_m.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_a_s_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_a_s_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_c_i_d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_c_i_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_l_y_f',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_l_y_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_d_m_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_d_m_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_e_a_d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_e_a_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_h_e_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_m_t_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._k_e_r_n',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_k_e_r_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_c_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_c_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_o_c_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_o_c_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_t_a_g',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_t_a_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_a_x_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_a_x_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_e_t_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_e_t_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._n_a_m_e',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_n_a_m_e.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._o_p_b_d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_o_p_b_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_o_s_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_o_s_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_e_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_e_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_o_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_o_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._s_b_i_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_s_b_i_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._t_r_a_k',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_t_r_a_k.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_h_e_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_m_t_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.asciiTable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\asciiTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.grUtils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\grUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otBase',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otBase.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otConverters',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otConverters.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otData',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otData.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTables.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTraverse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTraverse.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixGlyph',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixGlyph.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixStrike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixStrike.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.ttProgram',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\ttProgram.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttCollection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttCollection.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttFont',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttFont.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttGlyphSet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttGlyphSet.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttVisitor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttVisitor.py',
   'PYMODULE'),
  ('fontTools.ttLib.woff2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\woff2.py',
   'PYMODULE'),
  ('fontTools.ttx',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttx.py',
   'PYMODULE'),
  ('fontTools.unicode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\unicode.py',
   'PYMODULE'),
  ('fontTools.varLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.varLib.builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\builder.py',
   'PYMODULE'),
  ('fontTools.varLib.cff',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\cff.py',
   'PYMODULE'),
  ('fontTools.varLib.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\errors.py',
   'PYMODULE'),
  ('fontTools.varLib.featureVars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\featureVars.py',
   'PYMODULE'),
  ('fontTools.varLib.merger',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\merger.py',
   'PYMODULE'),
  ('fontTools.varLib.models',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\models.py',
   'PYMODULE'),
  ('fontTools.varLib.multiVarStore',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\multiVarStore.py',
   'PYMODULE'),
  ('fontTools.varLib.mvar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\mvar.py',
   'PYMODULE'),
  ('fontTools.varLib.stat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\stat.py',
   'PYMODULE'),
  ('fontTools.varLib.varStore',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\varStore.py',
   'PYMODULE'),
  ('fractions', 'D:\\Miniconda3\\envs\\Excel\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Miniconda3\\envs\\Excel\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Miniconda3\\envs\\Excel\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Miniconda3\\envs\\Excel\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Miniconda3\\envs\\Excel\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Miniconda3\\envs\\Excel\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Miniconda3\\envs\\Excel\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Miniconda3\\envs\\Excel\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Miniconda3\\envs\\Excel\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Miniconda3\\envs\\Excel\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\Miniconda3\\envs\\Excel\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\Miniconda3\\envs\\Excel\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Miniconda3\\envs\\Excel\\lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Miniconda3\\envs\\Excel\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Miniconda3\\envs\\Excel\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Miniconda3\\envs\\Excel\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Miniconda3\\envs\\Excel\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Miniconda3\\envs\\Excel\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Miniconda3\\envs\\Excel\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Miniconda3\\envs\\Excel\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Miniconda3\\envs\\Excel\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Miniconda3\\envs\\Excel\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Miniconda3\\envs\\Excel\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Miniconda3\\envs\\Excel\\lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\Miniconda3\\envs\\Excel\\lib\\optparse.py', 'PYMODULE'),
  ('pandas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Miniconda3\\envs\\Excel\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Miniconda3\\envs\\Excel\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Miniconda3\\envs\\Excel\\lib\\pickle.py', 'PYMODULE'),
  ('pickletools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\pickletools.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Miniconda3\\envs\\Excel\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Miniconda3\\envs\\Excel\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\Miniconda3\\envs\\Excel\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Miniconda3\\envs\\Excel\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\Miniconda3\\envs\\Excel\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Miniconda3\\envs\\Excel\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Miniconda3\\envs\\Excel\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pytz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\Miniconda3\\envs\\Excel\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Miniconda3\\envs\\Excel\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Miniconda3\\envs\\Excel\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\Miniconda3\\envs\\Excel\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Miniconda3\\envs\\Excel\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Miniconda3\\envs\\Excel\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\Miniconda3\\envs\\Excel\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Miniconda3\\envs\\Excel\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Miniconda3\\envs\\Excel\\lib\\signal.py', 'PYMODULE'),
  ('six',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\Miniconda3\\envs\\Excel\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\Miniconda3\\envs\\Excel\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Miniconda3\\envs\\Excel\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Miniconda3\\envs\\Excel\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Miniconda3\\envs\\Excel\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Miniconda3\\envs\\Excel\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Miniconda3\\envs\\Excel\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Miniconda3\\envs\\Excel\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Miniconda3\\envs\\Excel\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Miniconda3\\envs\\Excel\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Miniconda3\\envs\\Excel\\lib\\threading.py', 'PYMODULE'),
  ('timeit', 'D:\\Miniconda3\\envs\\Excel\\lib\\timeit.py', 'PYMODULE'),
  ('token', 'D:\\Miniconda3\\envs\\Excel\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Miniconda3\\envs\\Excel\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Miniconda3\\envs\\Excel\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Miniconda3\\envs\\Excel\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'D:\\Miniconda3\\envs\\Excel\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\Miniconda3\\envs\\Excel\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Miniconda3\\envs\\Excel\\lib\\webbrowser.py', 'PYMODULE'),
  ('xlrd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\__init__.py',
   'PYMODULE'),
  ('xlrd.biffh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\biffh.py',
   'PYMODULE'),
  ('xlrd.book',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\book.py',
   'PYMODULE'),
  ('xlrd.compdoc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE'),
  ('xlrd.formatting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE'),
  ('xlrd.formula',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE'),
  ('xlrd.info',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\info.py',
   'PYMODULE'),
  ('xlrd.sheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\sheet.py',
   'PYMODULE'),
  ('xlrd.timemachine',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE'),
  ('xlrd.xldate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE'),
  ('xlsxwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.color',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('xml', 'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Miniconda3\\envs\\Excel\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Miniconda3\\envs\\Excel\\lib\\zipimport.py', 'PYMODULE')])
