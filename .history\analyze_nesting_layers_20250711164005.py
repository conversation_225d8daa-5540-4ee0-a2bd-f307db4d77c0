#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析B34.dxf文件的嵌套层数
"""

import sys
import os

# 添加py源码目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'py源码'))

try:
    from 检查dxf图形是否有多个图形 import DXFCheckThread
    import ezdxf
    print("✓ 成功导入所需模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)

def analyze_nesting_layers():
    """分析嵌套层数"""
    file_path = r"C:\Users\<USER>\Desktop\250540科索沃仓库排版\新建文件夹\NC_dxf\B34.dxf"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        return
    
    print(f"分析文件: {file_path}")
    
    try:
        # 读取DXF文件
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        # 获取所有POLYLINE实体
        polylines = []
        for entity in msp:
            if entity.dxftype() in ['POLYLINE', 'LWPOLYLINE']:
                polylines.append(entity)
        
        print(f"POLYLINE实体数量: {len(polylines)}")
        
        # 创建检测器
        checker = DXFCheckThread(".")
        
        # 获取每个POLYLINE的边界框和详细信息
        polyline_info = []
        for i, polyline in enumerate(polylines):
            bbox = checker.get_entity_bounding_box(polyline)
            is_closed = checker.is_closed_polyline(polyline)
            
            if bbox:
                # 计算面积和中心点
                width = bbox['max_x'] - bbox['min_x']
                height = bbox['max_y'] - bbox['min_y']
                area = width * height
                center_x = (bbox['min_x'] + bbox['max_x']) / 2
                center_y = (bbox['min_y'] + bbox['max_y']) / 2
                
                polyline_info.append({
                    'index': i,
                    'entity': polyline,
                    'bbox': bbox,
                    'area': area,
                    'center': (center_x, center_y),
                    'width': width,
                    'height': height,
                    'is_closed': is_closed
                })
                
                print(f"POLYLINE {i+1}:")
                print(f"  边界框: ({bbox['min_x']:.1f}, {bbox['min_y']:.1f}) - ({bbox['max_x']:.1f}, {bbox['max_y']:.1f})")
                print(f"  尺寸: {width:.1f} x {height:.1f}")
                print(f"  面积: {area:.1f}")
                print(f"  中心: ({center_x:.1f}, {center_y:.1f})")
                print(f"  封闭: {is_closed}")
        
        # 按面积排序（从大到小）
        polyline_info.sort(key=lambda x: x['area'], reverse=True)
        
        print(f"\n按面积排序的POLYLINE:")
        for i, info in enumerate(polyline_info):
            print(f"{i+1}. POLYLINE {info['index']+1}: 面积={info['area']:.1f}")
        
        # 分析嵌套关系
        print(f"\n分析嵌套关系:")
        nesting_relationships = []
        
        for i in range(len(polyline_info)):
            for j in range(len(polyline_info)):
                if i == j:
                    continue
                
                outer = polyline_info[i]
                inner = polyline_info[j]
                
                # 检查inner是否在outer内部
                if is_bbox_inside(inner['bbox'], outer['bbox']):
                    nesting_relationships.append((outer['index'], inner['index']))
                    print(f"  POLYLINE {inner['index']+1} 在 POLYLINE {outer['index']+1} 内部")
        
        # 构建嵌套层次结构
        print(f"\n构建嵌套层次结构:")
        layers = build_nesting_layers(polyline_info, nesting_relationships)
        
        max_depth = 0
        for layer_depth, entities in layers.items():
            print(f"第{layer_depth}层: {[info['index']+1 for info in entities]}")
            max_depth = max(max_depth, layer_depth)
        
        print(f"\n最大嵌套深度: {max_depth}")
        print(f"是否达到3层以上嵌套: {'是' if max_depth >= 3 else '否'}")
        
    except Exception as e:
        print(f"✗ 分析失败: {e}")
        import traceback
        traceback.print_exc()

def is_bbox_inside(inner_bbox, outer_bbox):
    """检查一个边界框是否在另一个边界框内部"""
    tolerance = 1.0
    return (
        outer_bbox['min_x'] + tolerance < inner_bbox['min_x'] and
        inner_bbox['max_x'] < outer_bbox['max_x'] - tolerance and
        outer_bbox['min_y'] + tolerance < inner_bbox['min_y'] and
        inner_bbox['max_y'] < outer_bbox['max_y'] - tolerance
    )

def build_nesting_layers(polyline_info, relationships):
    """构建嵌套层次结构"""
    # 创建包含关系图
    contains = {}  # outer_index -> [inner_indices]
    contained_by = {}  # inner_index -> [outer_indices]
    
    for outer_idx, inner_idx in relationships:
        if outer_idx not in contains:
            contains[outer_idx] = []
        contains[outer_idx].append(inner_idx)
        
        if inner_idx not in contained_by:
            contained_by[inner_idx] = []
        contained_by[inner_idx].append(outer_idx)
    
    # 找到最外层（不被任何其他图形包含）
    all_indices = set(info['index'] for info in polyline_info)
    outermost = all_indices - set(contained_by.keys())
    
    # 递归构建层次
    layers = {}
    visited = set()
    
    def assign_layer(index, depth):
        if index in visited:
            return
        visited.add(index)
        
        if depth not in layers:
            layers[depth] = []
        
        # 找到对应的polyline_info
        info = next(info for info in polyline_info if info['index'] == index)
        layers[depth].append(info)
        
        # 处理被这个图形包含的图形
        if index in contains:
            for inner_index in contains[index]:
                assign_layer(inner_index, depth + 1)
    
    # 从最外层开始
    for outer_index in outermost:
        assign_layer(outer_index, 1)
    
    return layers

if __name__ == "__main__":
    analyze_nesting_layers()
