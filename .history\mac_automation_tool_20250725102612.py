#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MAC地址修改自动化工具
支持自动启动MAC修改软件并执行MAC地址修改操作
"""

import os
import sys
import time
import re
import random
import subprocess
import logging
from typing import Optional, Tuple
import pyautogui
import pywinauto
from pywinauto import Application, findwindows
from pywinauto.controls.uiawrapper import UIAWrapper

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mac_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MACAutomationTool:
    """MAC地址修改自动化工具类"""
    
    def __init__(self, software_path: str = None):
        """
        初始化MAC自动化工具
        
        Args:
            software_path: MAC修改软件的路径，如果为None则尝试自动查找
        """
        self.software_path = software_path
        self.app = None
        self.main_window = None
        self.process = None
        
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
        # 软件可能的名称列表
        self.software_names = [
            "MAC修改器",
            "MAC修改器.exe",
            "mac修改器",
            "mac修改器.exe"
        ]
        
        # 界面元素名称（根据截图推测）
        self.ui_elements = {
            'network_card_combo': '网卡信息',
            'current_mac_edit': '当前MAC',
            'modify_mac_edit': '修改MAC',
            'random_button': '随机生成MAC',
            'modify_button': '修改',
            'exit_button': '退出'
        }
    
    def find_software_path(self) -> Optional[str]:
        """
        自动查找MAC修改软件路径
        
        Returns:
            软件路径，如果未找到返回None
        """
        logger.info("正在查找MAC修改软件...")
        
        # 检查桌面
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # 检查当前目录
        search_paths = [
            desktop_path,
            os.getcwd(),
            "."
        ]
        
        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue
                
            for filename in os.listdir(search_path):
                if any(name.lower() in filename.lower() for name in self.software_names):
                    full_path = os.path.join(search_path, filename)
                    if os.path.isfile(full_path) and filename.endswith('.exe'):
                        logger.info(f"找到MAC修改软件: {full_path}")
                        return full_path
        
        logger.warning("未找到MAC修改软件")
        return None
    
    def start_software(self) -> bool:
        """
        启动MAC修改软件
        
        Returns:
            启动成功返回True，失败返回False
        """
        try:
            if not self.software_path:
                self.software_path = self.find_software_path()
                
            if not self.software_path or not os.path.exists(self.software_path):
                logger.error("MAC修改软件路径无效或文件不存在")
                return False
            
            logger.info(f"正在启动软件: {self.software_path}")
            
            # 启动软件
            self.process = subprocess.Popen([self.software_path])
            time.sleep(3)  # 等待软件启动
            
            # 连接到应用程序
            try:
                self.app = Application().connect(process=self.process.pid)
            except:
                # 如果通过进程ID连接失败，尝试通过窗口标题连接
                windows = findwindows.find_windows(title_re=".*MAC.*")
                if windows:
                    self.app = Application().connect(handle=windows[0])
                else:
                    logger.error("无法连接到MAC修改软件窗口")
                    return False
            
            # 获取主窗口
            self.main_window = self.app.top_window()
            logger.info("软件启动成功，已连接到主窗口")
            return True
            
        except Exception as e:
            logger.error(f"启动软件失败: {str(e)}")
            return False
    
    def validate_mac_address(self, mac: str) -> bool:
        """
        验证MAC地址格式
        
        Args:
            mac: MAC地址字符串
            
        Returns:
            格式正确返回True，否则返回False
        """
        # MAC地址格式: XX-XX-XX-XX-XX-XX 或 XX:XX:XX:XX:XX:XX
        pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
        return bool(re.match(pattern, mac))
    
    def generate_random_mac(self) -> str:
        """
        生成随机MAC地址
        
        Returns:
            随机生成的MAC地址字符串
        """
        # 生成6个随机的十六进制字节
        mac_bytes = [random.randint(0x00, 0xFF) for _ in range(6)]
        
        # 确保第一个字节是偶数（单播地址）
        mac_bytes[0] = mac_bytes[0] & 0xFE
        
        # 格式化为XX-XX-XX-XX-XX-XX格式
        mac_address = '-'.join([f'{byte:02X}' for byte in mac_bytes])
        
        logger.info(f"生成随机MAC地址: {mac_address}")
        return mac_address
    
    def find_control_by_text(self, text: str) -> Optional[UIAWrapper]:
        """
        根据文本查找控件
        
        Args:
            text: 要查找的文本
            
        Returns:
            找到的控件，未找到返回None
        """
        try:
            if not self.main_window:
                return None
                
            # 尝试多种方式查找控件
            search_methods = [
                lambda: self.main_window.child_window(title=text),
                lambda: self.main_window.child_window(title_re=f".*{text}.*"),
                lambda: self.main_window.child_window(control_type="Edit", title_re=f".*{text}.*"),
                lambda: self.main_window.child_window(control_type="Button", title=text),
                lambda: self.main_window.child_window(control_type="ComboBox", title_re=f".*{text}.*")
            ]
            
            for method in search_methods:
                try:
                    control = method()
                    if control.exists():
                        return control
                except:
                    continue
                    
            return None
            
        except Exception as e:
            logger.error(f"查找控件失败 '{text}': {str(e)}")
            return None
    
    def clear_and_input_mac(self, mac_address: str) -> bool:
        """
        清空修改MAC输入框并输入新的MAC地址

        Args:
            mac_address: 要输入的MAC地址

        Returns:
            操作成功返回True，失败返回False
        """
        try:
            if not self.validate_mac_address(mac_address):
                logger.error(f"MAC地址格式无效: {mac_address}")
                return False

            logger.info(f"正在输入MAC地址: {mac_address}")

            # 查找修改MAC输入框 - 改进的识别逻辑
            modify_mac_edit = None

            try:
                # 方法1: 获取所有编辑框控件
                edit_controls = self.main_window.children(control_type="Edit")
                logger.info(f"找到 {len(edit_controls)} 个编辑框控件")

                # 打印所有编辑框的信息用于调试
                for i, control in enumerate(edit_controls):
                    try:
                        text = control.get_value()
                        logger.info(f"编辑框 {i}: 内容='{text}'")
                    except:
                        logger.info(f"编辑框 {i}: 无法获取内容")

                # 根据MAC地址软件的界面布局，第二个编辑框通常是修改MAC输入框
                if len(edit_controls) >= 2:
                    modify_mac_edit = edit_controls[1]
                    logger.info("使用第二个编辑框作为修改MAC输入框")
                elif len(edit_controls) == 1:
                    modify_mac_edit = edit_controls[0]
                    logger.info("只有一个编辑框，使用它作为修改MAC输入框")

            except Exception as e:
                logger.error(f"获取编辑框控件失败: {e}")

            # 方法2: 如果上面的方法失败，尝试通过坐标定位
            if not modify_mac_edit:
                try:
                    logger.info("尝试通过窗口坐标定位修改MAC输入框...")

                    # 获取窗口位置和大小
                    window_rect = self.main_window.rectangle()
                    logger.info(f"窗口位置: {window_rect}")

                    # 根据您的截图，修改MAC输入框大概在窗口的中下部分
                    # 计算大概位置（这个需要根据实际软件调整）
                    input_x = window_rect.left + 300  # 输入框大概在窗口中间偏右
                    input_y = window_rect.top + 147   # 根据截图估算的Y坐标

                    logger.info(f"尝试点击坐标: ({input_x}, {input_y})")

                    # 点击输入框位置
                    pyautogui.click(input_x, input_y)
                    time.sleep(0.5)

                    # 全选并删除现有内容
                    pyautogui.hotkey('ctrl', 'a')
                    time.sleep(0.2)
                    pyautogui.press('delete')
                    time.sleep(0.2)

                    # 输入新的MAC地址
                    pyautogui.typewrite(mac_address)
                    time.sleep(0.5)

                    logger.info("通过坐标定位成功输入MAC地址")
                    return True

                except Exception as e:
                    logger.error(f"坐标定位方法失败: {e}")

            # 方法3: 使用控件操作
            if modify_mac_edit:
                try:
                    # 点击输入框
                    modify_mac_edit.click_input()
                    time.sleep(0.5)

                    # 全选并删除现有内容
                    pyautogui.hotkey('ctrl', 'a')
                    time.sleep(0.2)
                    pyautogui.press('delete')
                    time.sleep(0.2)

                    # 输入新的MAC地址
                    pyautogui.typewrite(mac_address)
                    time.sleep(0.5)

                    logger.info("MAC地址输入完成")
                    return True

                except Exception as e:
                    logger.error(f"控件操作失败: {e}")

            logger.error("所有输入方法都失败了")
            return False

        except Exception as e:
            logger.error(f"输入MAC地址失败: {str(e)}")
            return False
    
    def click_modify_button(self) -> bool:
        """
        点击修改按钮
        
        Returns:
            操作成功返回True，失败返回False
        """
        try:
            logger.info("正在点击修改按钮...")
            
            # 查找修改按钮
            modify_button = self.find_control_by_text("修改")
            
            if not modify_button:
                # 尝试查找所有按钮
                buttons = self.main_window.children(control_type="Button")
                for button in buttons:
                    try:
                        if "修改" in button.window_text():
                            modify_button = button
                            break
                    except:
                        continue
            
            if not modify_button:
                logger.error("未找到修改按钮")
                return False
            
            # 点击修改按钮
            modify_button.click_input()
            time.sleep(1)
            
            logger.info("修改按钮点击完成")
            return True
            
        except Exception as e:
            logger.error(f"点击修改按钮失败: {str(e)}")
            return False
    
    def modify_mac_address(self, mac_address: str = None) -> bool:
        """
        执行MAC地址修改操作
        
        Args:
            mac_address: 要设置的MAC地址，如果为None则生成随机MAC
            
        Returns:
            操作成功返回True，失败返回False
        """
        try:
            if not mac_address:
                mac_address = self.generate_random_mac()
            
            logger.info(f"开始修改MAC地址为: {mac_address}")
            
            # 确保软件已启动
            if not self.main_window:
                if not self.start_software():
                    return False
            
            # 输入MAC地址
            if not self.clear_and_input_mac(mac_address):
                return False
            
            # 点击修改按钮
            if not self.click_modify_button():
                return False
            
            logger.info("MAC地址修改操作完成")
            return True
            
        except Exception as e:
            logger.error(f"修改MAC地址失败: {str(e)}")
            return False
    
    def close_software(self):
        """关闭MAC修改软件"""
        try:
            if self.main_window:
                # 尝试点击退出按钮
                exit_button = self.find_control_by_text("退出")
                if exit_button:
                    exit_button.click_input()
                else:
                    # 如果找不到退出按钮，直接关闭窗口
                    self.main_window.close()
                
            if self.process:
                self.process.terminate()
                
            logger.info("软件已关闭")
            
        except Exception as e:
            logger.error(f"关闭软件失败: {str(e)}")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        self.close_software()


def main():
    """主函数 - 命令行界面"""
    print("=" * 50)
    print("MAC地址修改自动化工具")
    print("=" * 50)
    
    tool = MACAutomationTool()
    
    try:
        while True:
            print("\n请选择操作:")
            print("1. 使用随机MAC地址")
            print("2. 输入自定义MAC地址")
            print("3. 退出")
            
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == '1':
                print("\n正在使用随机MAC地址修改...")
                if tool.modify_mac_address():
                    print("✓ MAC地址修改成功!")
                else:
                    print("✗ MAC地址修改失败!")
                    
            elif choice == '2':
                mac = input("\n请输入MAC地址 (格式: XX-XX-XX-XX-XX-XX): ").strip()
                if tool.validate_mac_address(mac):
                    print(f"\n正在修改MAC地址为: {mac}")
                    if tool.modify_mac_address(mac):
                        print("✓ MAC地址修改成功!")
                    else:
                        print("✗ MAC地址修改失败!")
                else:
                    print("✗ MAC地址格式无效! 请使用格式: XX-XX-XX-XX-XX-XX")
                    
            elif choice == '3':
                print("\n正在退出...")
                break
                
            else:
                print("✗ 无效选择，请重新输入!")
                
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {str(e)}")
    finally:
        tool.close_software()
        print("程序已退出")


if __name__ == "__main__":
    main()
