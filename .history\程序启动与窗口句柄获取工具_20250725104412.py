#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序启动与窗口句柄获取工具
功能：启动指定的可执行文件并获取其窗口句柄

依赖包安装：
pip install pywin32

作者：Augment Agent
创建时间：2025-07-25
"""

import subprocess
import time
import sys
import os
from typing import Optional, List, Tuple
import logging

try:
    import win32gui
    import win32process
    import win32con
except ImportError:
    print("错误：缺少pywin32库，请运行以下命令安装：")
    print("pip install pywin32")
    sys.exit(1)


class ProcessWindowManager:
    """进程和窗口管理器"""
    
    def __init__(self, exe_path: str):
        """
        初始化管理器
        
        Args:
            exe_path (str): 要启动的可执行文件路径
        """
        self.exe_path = exe_path
        self.process = None
        self.window_handles = []
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('window_manager.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def validate_exe_path(self) -> bool:
        """
        验证可执行文件路径是否有效
        
        Returns:
            bool: 路径有效返回True，否则返回False
        """
        if not os.path.exists(self.exe_path):
            self.logger.error(f"可执行文件不存在: {self.exe_path}")
            return False
        
        if not os.path.isfile(self.exe_path):
            self.logger.error(f"路径不是文件: {self.exe_path}")
            return False
        
        if not self.exe_path.lower().endswith('.exe'):
            self.logger.warning(f"文件可能不是可执行文件: {self.exe_path}")
        
        return True
    
    def start_process(self) -> bool:
        """
        启动指定的可执行文件
        
        Returns:
            bool: 启动成功返回True，否则返回False
        """
        if not self.validate_exe_path():
            return False
        
        try:
            self.logger.info(f"正在启动程序: {self.exe_path}")
            
            # 使用subprocess启动程序
            self.process = subprocess.Popen(
                [self.exe_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            # 等待程序启动
            time.sleep(2)
            
            # 检查进程是否仍在运行
            if self.process.poll() is None:
                self.logger.info(f"程序启动成功，进程ID: {self.process.pid}")
                return True
            else:
                self.logger.error("程序启动后立即退出")
                return False
                
        except FileNotFoundError:
            self.logger.error(f"找不到可执行文件: {self.exe_path}")
            return False
        except PermissionError:
            self.logger.error(f"没有权限执行文件: {self.exe_path}")
            return False
        except Exception as e:
            self.logger.error(f"启动程序时发生错误: {str(e)}")
            return False
    
    def enum_windows_callback(self, hwnd: int, windows: List[Tuple[int, str, int]]) -> bool:
        """
        枚举窗口的回调函数
        
        Args:
            hwnd (int): 窗口句柄
            windows (List): 窗口信息列表
            
        Returns:
            bool: 继续枚举返回True
        """
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            try:
                _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                windows.append((hwnd, window_text, process_id))
            except:
                pass
        return True
    
    def get_all_windows(self) -> List[Tuple[int, str, int]]:
        """
        获取所有可见窗口
        
        Returns:
            List[Tuple[int, str, int]]: 窗口信息列表 (句柄, 标题, 进程ID)
        """
        windows = []
        try:
            win32gui.EnumWindows(self.enum_windows_callback, windows)
        except Exception as e:
            self.logger.error(f"枚举窗口时发生错误: {str(e)}")
        return windows
    
    def find_process_windows(self, max_attempts: int = 10, wait_interval: float = 1.0) -> List[int]:
        """
        查找与启动进程相关的窗口句柄
        
        Args:
            max_attempts (int): 最大尝试次数
            wait_interval (float): 每次尝试间隔时间（秒）
            
        Returns:
            List[int]: 窗口句柄列表
        """
        if not self.process:
            self.logger.error("进程未启动，无法查找窗口")
            return []
        
        process_id = self.process.pid
        self.logger.info(f"正在查找进程ID {process_id} 的窗口...")
        
        for attempt in range(max_attempts):
            windows = self.get_all_windows()
            process_windows = []
            
            for hwnd, title, pid in windows:
                if pid == process_id:
                    process_windows.append(hwnd)
                    self.logger.info(f"找到窗口: 句柄={hwnd}, 标题='{title}'")
            
            if process_windows:
                self.window_handles = process_windows
                return process_windows
            
            self.logger.info(f"第 {attempt + 1} 次尝试未找到窗口，等待 {wait_interval} 秒后重试...")
            time.sleep(wait_interval)
        
        self.logger.warning(f"经过 {max_attempts} 次尝试，未找到进程的窗口")
        return []
    
    def get_window_info(self, hwnd: int) -> dict:
        """
        获取窗口详细信息
        
        Args:
            hwnd (int): 窗口句柄
            
        Returns:
            dict: 窗口信息字典
        """
        try:
            info = {
                'handle': hwnd,
                'title': win32gui.GetWindowText(hwnd),
                'class_name': win32gui.GetClassName(hwnd),
                'is_visible': win32gui.IsWindowVisible(hwnd),
                'is_enabled': win32gui.IsWindowEnabled(hwnd),
                'rect': win32gui.GetWindowRect(hwnd)
            }
            
            try:
                _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                info['process_id'] = process_id
            except:
                info['process_id'] = None
            
            return info
        except Exception as e:
            self.logger.error(f"获取窗口信息时发生错误: {str(e)}")
            return {}
    
    def print_window_details(self):
        """打印所有找到的窗口详细信息"""
        if not self.window_handles:
            self.logger.info("没有找到窗口")
            return
        
        print("\n" + "="*60)
        print("窗口详细信息:")
        print("="*60)
        
        for i, hwnd in enumerate(self.window_handles, 1):
            info = self.get_window_info(hwnd)
            if info:
                print(f"\n窗口 {i}:")
                print(f"  句柄: {info['handle']}")
                print(f"  标题: '{info['title']}'")
                print(f"  类名: {info['class_name']}")
                print(f"  可见: {info['is_visible']}")
                print(f"  启用: {info['is_enabled']}")
                print(f"  位置: {info['rect']}")
                print(f"  进程ID: {info['process_id']}")
    
    def cleanup(self):
        """清理资源"""
        if self.process:
            try:
                if self.process.poll() is None:
                    self.logger.info("正在终止进程...")
                    self.process.terminate()
                    time.sleep(1)
                    if self.process.poll() is None:
                        self.process.kill()
                        self.logger.info("强制终止进程")
            except Exception as e:
                self.logger.error(f"清理进程时发生错误: {str(e)}")


def main():
    """主函数"""
    # 指定要启动的可执行文件路径
    exe_path = r"C:\Users\<USER>\Desktop\网卡修改.exe"
    
    # 创建进程窗口管理器
    manager = ProcessWindowManager(exe_path)
    
    try:
        # 启动程序
        if not manager.start_process():
            print("程序启动失败")
            return
        
        # 查找窗口句柄
        window_handles = manager.find_process_windows()
        
        if window_handles:
            print(f"\n成功找到 {len(window_handles)} 个窗口")
            print("窗口句柄列表:", window_handles)
            
            # 打印窗口详细信息
            manager.print_window_details()
            
            # 返回主窗口句柄（通常是第一个）
            main_window = window_handles[0]
            print(f"\n主窗口句柄: {main_window}")
            
        else:
            print("未找到程序窗口")
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行过程中发生错误: {str(e)}")
    finally:
        # 清理资源（可选，如果需要保持程序运行可以注释掉）
        # manager.cleanup()
        pass


if __name__ == "__main__":
    main()
