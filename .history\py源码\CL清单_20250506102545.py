import tkinter as tk
from tkinter import filedialog
from tkinter import messagebox
import os
import shutil
import datetime
import openpyxl
import xlrd
from openpyxl import Workbook
from tkinter import ttk  # 用于日期选择控件


class DateRangeDialog(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.title("设置日期范围")
        self.geometry("250x130+650+500")

        # 开始日期
        tk.Label(self, text="开始日期:").grid(row=0, column=0, padx=5, pady=5)
        self.start_date = ttk.Entry(self)
        self.start_date.grid(row=0, column=1, padx=5, pady=5)

        # 结束日期
        tk.Label(self, text="结束日期:").grid(row=1, column=0, padx=5, pady=5)
        self.end_date = ttk.Entry(self)
        self.end_date.grid(row=1, column=1, padx=5, pady=5)

        # 设置默认值为今天
        today = datetime.date.today().strftime("%Y-%m-%d")
        self.start_date.insert(0, today)
        self.end_date.insert(0, today)

        # 确认按钮 
        tk.Button(self, text="确定", command=self.destroy, height=1, width=8).grid(row=2, column=0, columnspan=2, pady=10)


class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        # 设置主窗口最小尺寸
        #固定窗口位置
        self.master.geometry("480x300+600+450")
        self.master.minsize(480, 300)
        self.master.title("Excel处理工具")
        self.pack(fill="both", expand=True)
        self.create_widgets()
        self.entry1.insert(0, r"C:\SW自定义\Doctor\ListBOM")

        # 默认日期范围设置为今天
        self.today = datetime.date.today()
        self.date_range = {
            'start': datetime.datetime.combine(self.today, datetime.time.min),
            'end': datetime.datetime.combine(self.today, datetime.time.max)
        }

    def create_widgets(self):
        # 使用place布局并确保窗口足够大
        self.label1 = tk.Label(self, text="默认路径")
        self.label1.place(x=20, y=20)

        self.entry1 = tk.Entry(self, width=50)
        self.entry1.place(x=100, y=20)

        # 日期范围按钮
        self.date_btn = tk.Button(self, text="设置日期范围", command=self.set_date_range)
        self.date_btn.place(x=260, y=100)
        # 文件处理按钮
        self.button1 = tk.Button(self, text="显示并处理xls文件", command=self.show_and_process_xls)
        self.button1.place(x=20, y=100)
        # 粘贴路径
        self.label3 = tk.Label(self, text="粘贴路径")
        self.label3.place(x=20, y=60)

        self.entry3 = tk.Entry(self, width=50)
        self.entry3.place(x=100, y=60)
        # 复制按钮
        self.button3 = tk.Button(self, text="执行复制粘贴操作", command=self.copy_xls)
        self.button3.place(x=140, y=100)
        # 结果显示文本框
        self.text = tk.Text(self, width=61, height=10)
        self.text.place(x=20, y=140)

    def process_excel_file(self, file_path, file_name):
        """处理单个Excel文件的通用方法"""
        try:
            # 只处理.xls文件
            if not file_name.lower().endswith('.xls'):
                return False, None, "不是.xls格式文件"

            # 使用xlrd读取.xls文件
            xls_wb = xlrd.open_workbook(file_path)
            xls_sheet = xls_wb.sheet_by_index(0)

            # 将数据转换为openpyxl格式
            wb = Workbook()
            sheet = wb.active
            for row in range(xls_sheet.nrows):
                for col in range(xls_sheet.ncols):
                    sheet.cell(row=row + 1, column=col + 1, value=xls_sheet.cell_value(row, col))

            # 1. 取消所有合并单元格
            merged_cells = list(sheet.merged_cells.ranges)
            for merged_range in merged_cells:
                sheet.unmerge_cells(str(merged_range))
                # 全选单元格并居中
                sheet.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')

            # 2. 检查整个表格是否有"@自动数量"
            has_serial = False
            for row in sheet.iter_rows():
                for cell in row:
                    if cell.value and "@自动数量" in str(cell.value):
                        has_serial = True
                        break
                if has_serial:
                    break

            # 3. 获取D3单元格内容作为新文件名
            new_name = str(sheet['D3'].value) + ".xls"

            # 4. 删除前3行
            sheet.delete_rows(1, 3)

            # 5. 删除第一列
            sheet.delete_cols(1)

            # 6. 修改表头
            for cell in sheet[1]:
                if cell.value and "@自动层级" in str(cell.value):
                    cell.value = "序号"
                elif cell.value and "@自动数量" in str(cell.value):
                    cell.value = "数量"
            # 7. 全部居中，加边框
            for row in sheet.iter_rows():
                for cell in row:
                    cell.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')
                    cell.border = openpyxl.styles.Border(left=openpyxl.styles.Side(border_style='thin'),
                                                         right=openpyxl.styles.Side(border_style='thin'),
                                                         top=openpyxl.styles.Side(border_style='thin'),
                                                         bottom=openpyxl.styles.Side(border_style='thin'))

            return True, wb, new_name

        except Exception as e:
            return False, None, f"处理文件时出错: {str(e)}"

    def set_date_range(self):
        dialog = DateRangeDialog(self)
        self.wait_window(dialog)  # 等待对话框关闭

        try:
            # 解析日期
            if dialog and hasattr(dialog, 'start_date') and dialog.start_date.winfo_exists():
                start = datetime.datetime.strptime(dialog.start_date.get(), "%Y-%m-%d")
            else:
                # 处理对话框已关闭的情况
                # print("日期对话框已关闭")
                return
            end = datetime.datetime.strptime(dialog.end_date.get(), "%Y-%m-%d")

            # 确保结束日期不小于开始日期
            if end < start:
                messagebox.showerror("错误", "结束日期不能早于开始日期")
                return

            # 更新日期范围
            self.date_range['start'] = datetime.datetime.combine(start.date(), datetime.time.min)
            self.date_range['end'] = datetime.datetime.combine(end.date(), datetime.time.max)

            messagebox.showinfo("提示", f"日期范围已设置为: {start.date()} 至 {end.date()}")

        except ValueError:
            messagebox.showerror("错误", "日期格式不正确，请使用YYYY-MM-DD格式")

    def show_and_process_xls(self):
        path = self.entry1.get()
        if not os.path.exists(path):
            messagebox.showerror("错误", "路径不存在")
            return

        valid_files = []
        skipped_files = {
            'wrong_date': [],
            'wrong_format': [],
            'no_serial': [],
            'error': []
        }

        with os.scandir(path) as entries:
            for entry in entries:
                if entry.name.startswith('~$'):
                    continue

                if entry.name.lower().endswith('.xls') and entry.is_file():
                    modify_time = datetime.datetime.fromtimestamp(entry.stat().st_mtime)
                    if not (self.date_range['start'] <= modify_time <= self.date_range['end']):
                        skipped_files['wrong_date'].append(entry.name)
                        continue

                    file_path = os.path.join(path, entry.name)

                    # 检查文件是否被占用
                    try:
                        with open(file_path, 'a+b') as f:
                            pass
                    except IOError:
                        messagebox.showwarning("警告", f"文件 {entry.name} 可能被其他程序占用")
                        skipped_files['error'].append(entry.name)
                        continue

                    success, wb, result = self.process_excel_file(file_path, entry.name)

                    if not success:
                        if "不是.xls格式文件" in result:
                            skipped_files['wrong_format'].append(entry.name)
                        elif "缺少'序号'表头" in result:
                            skipped_files['no_serial'].append(entry.name)
                        else:
                            skipped_files['error'].append(entry.name)
                            messagebox.showwarning("警告", f"{entry.name}: {result}")
                        continue

                    # 保存修改后的文件
                    new_name = result
                    new_path = os.path.join(path, new_name)
                    wb.save(new_path)

                    # 如果文件名有变化，删除原文件
                    if new_name != entry.name:
                        try:
                            os.remove(file_path)
                        except Exception as e:
                            messagebox.showwarning("警告", f"删除原文件 {entry.name} 失败: {str(e)}")

                    valid_files.append(new_name)

        # 显示跳过文件的原因
        if not valid_files:
            msg = "没有找到符合条件的.xls文件\n\n"
            if skipped_files['wrong_date']:
                msg += f"非当天文件: {', '.join(skipped_files['wrong_date'][:3])}"
                if len(skipped_files['wrong_date']) > 3:
                    msg += f" 等{len(skipped_files['wrong_date'])}个\n"
                else:
                    msg += "\n"

            if skipped_files['wrong_format']:
                msg += f"非.xls文件: {', '.join(skipped_files['wrong_format'][:3])}"
                if len(skipped_files['wrong_format']) > 3:
                    msg += f" 等{len(skipped_files['wrong_format'])}个\n"
                else:
                    msg += "\n"

            if skipped_files['no_serial']:
                msg += f"缺少'序号'表头: {', '.join(skipped_files['no_serial'][:3])}"
                if len(skipped_files['no_serial']) > 3:
                    msg += f" 等{len(skipped_files['no_serial'])}个\n"

            if skipped_files['error']:
                msg += f"处理出错: {', '.join(skipped_files['error'][:3])}"
                if len(skipped_files['error']) > 3:
                    msg += f" 等{len(skipped_files['error'])}个"

            messagebox.showinfo("提示", msg)
            return

        self.text.delete("1.0", "end")
        for file in valid_files:
            self.text.insert("end", file + "\n")

    def modify_xls(self):
        path = self.entry1.get()
        if not os.path.exists(path):
            messagebox.showerror("错误", "路径不存在")
            return

        files = self.text.get("1.0", "end-1c").split("\n")
        if not files or files == ['']:
            messagebox.showinfo("提示", "没有选择文件")
            return

        for file in files:
            if file == '':
                continue
            if not os.path.exists(os.path.join(path, file)):
                messagebox.showerror("错误", f"文件 {file} 不存在")
                return
            messagebox.showinfo("提示", f"文件 {file} 修改成功")

    def copy_xls(self):
        target_path = self.entry3.get()
        if not os.path.exists(target_path):
            messagebox.showerror("错误", "目标路径不存在")
            return

        source_path = self.entry1.get()
        files = self.text.get("1.0", "end-1c").split("\n")

        if not files or files == ['']:
            messagebox.showinfo("提示", "没有选择文件")
            return

        for file in files:
            if file == '':
                continue

            src_file = os.path.join(source_path, file)
            dst_file = os.path.join(target_path, file)

            if not os.path.exists(src_file):
                messagebox.showerror("错误", f"源文件 {file} 不存在")
                continue

            if os.path.abspath(src_file) == os.path.abspath(dst_file):
                messagebox.showwarning("警告", f"跳过 {file} - 源路径和目标路径相同")
                continue

            try:
                shutil.copy(src_file, dst_file)
                messagebox.showinfo("提示", f"文件 {file} 复制成功")
            except Exception as e:
                messagebox.showerror("错误", f"复制 {file} 失败: {str(e)}")


root = tk.Tk()
app = Application(master=root)
app.mainloop()
