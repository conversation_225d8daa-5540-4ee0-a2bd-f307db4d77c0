#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel前缀更新逻辑
验证带连字符前缀的零件编号能够正确更新
"""

import os
import sys
import pandas as pd
import shutil
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

def test_excel_prefix_update():
    """测试Excel前缀更新逻辑"""
    print("=" * 80)
    print("Excel前缀更新逻辑测试")
    print("=" * 80)
    
    # 创建测试环境
    test_dir = create_test_environment()
    
    # 执行测试
    success = run_prefix_update_test(test_dir)
    
    if success:
        print(f"\n✅ Excel前缀更新逻辑测试通过！")
        return True
    else:
        print(f"\n❌ Excel前缀更新逻辑测试失败！")
        return False

def create_test_environment():
    """创建测试环境"""
    test_dir = os.path.join(os.path.dirname(__file__), "test_data", "excel_prefix_test")
    
    # 清理并重新创建目录
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
    os.makedirs(test_dir)
    
    # 创建测试Excel文件，包含各种前缀场景
    data = {
        '序号': [1, 2, 3, 4, 5, 6],
        '零件编号': ['LP22', '2-LP22', 'C38', 'A-C38', 'D12', '12-D12'],
        '零件名称': ['支撑板1', '支撑板2', '连接件1', '连接件2', '固定块1', '固定块2'],
        '材质': ['Q235', 'Q235', 'Q345', 'Q345', 'Q235', 'Q235'],
        '数量': [2, 1, 4, 2, 1, 3],
        '备注': ['', '', '', '', '', '']
    }
    
    df = pd.DataFrame(data)
    excel_file = os.path.join(test_dir, "前缀更新测试清单.xlsx")
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=False)
    
    print(f"✓ 创建测试环境: {test_dir}")
    print(f"✓ 测试Excel文件: 前缀更新测试清单.xlsx")
    print(f"✓ 原始数据: {data['零件编号']}")
    
    return test_dir

def run_prefix_update_test(test_dir):
    """运行前缀更新测试"""
    try:
        # 导入处理类
        from 批量零件编号处理工具_Qt import ProcessingThread
        
        # 测试参数 - 关键测试用例
        part_names = ['LP22A', 'C38B', 'D12C']  # 输入的零件名称
        sheet_name = "Sheet1"
        column_name = "零件编号"
        output_dir = os.path.join(test_dir, "output")
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n测试参数:")
        print(f"  输入零件名称: {part_names}")
        print(f"  工作表: {sheet_name}")
        print(f"  目标列: {column_name}")
        
        # 创建处理实例
        processor = ProcessingThread(part_names, test_dir, output_dir, sheet_name, column_name)
        
        # 步骤1：记录处理前状态
        print(f"\n{'='*25} 步骤1：处理前状态 {'='*25}")
        excel_file = os.path.join(test_dir, "前缀更新测试清单.xlsx")
        df_before = pd.read_excel(excel_file, sheet_name=sheet_name)
        original_data = list(df_before[column_name])
        print(f"处理前数据: {original_data}")
        
        # 步骤2：解析零件名称
        print(f"\n{'='*25} 步骤2：零件名称解析 {'='*25}")
        processed_parts = processor.parse_part_names()
        print(f"解析结果:")
        for part in processed_parts:
            print(f"  {part['original_name']} → 匹配目标: {part['match_target']}")
        
        # 步骤3：测试匹配逻辑
        print(f"\n{'='*25} 步骤3：匹配逻辑测试 {'='*25}")
        df = pd.read_excel(excel_file, sheet_name=sheet_name)
        
        for part_info in processed_parts:
            match_target = part_info['match_target']
            original_name = part_info['original_name']
            
            print(f"\n测试: {original_name} (匹配目标: {match_target})")
            
            # 查找匹配项
            matched_indices = processor.find_flexible_matches(df, match_target)
            
            if matched_indices:
                print(f"  ✓ 找到 {len(matched_indices)} 个匹配项:")
                for idx in matched_indices:
                    old_value = df.loc[idx, column_name]
                    match_type = processor.get_excel_match_type(str(old_value), match_target)
                    new_value = processor.generate_new_excel_value(str(old_value), match_target, original_name)
                    print(f"    第{idx+2}行: '{old_value}' → '{new_value}' ({match_type})")
            else:
                print(f"  ✗ 没有匹配项")
        
        # 步骤4：执行Excel处理
        print(f"\n{'='*25} 步骤4：执行Excel处理 {'='*25}")
        excel_success = processor.process_excel_files(processed_parts)
        print(f"Excel处理结果: {'成功' if excel_success else '失败'}")
        
        if not excel_success:
            print(f"错误信息: {processor.stats['errors']}")
            return False
        
        # 步骤5：验证处理结果
        print(f"\n{'='*25} 步骤5：验证处理结果 {'='*25}")
        print(f"处理统计:")
        print(f"  匹配数量: {processor.stats['excel_matched']}")
        print(f"  更新数量: {processor.stats['excel_updated']}")
        print(f"  错误数量: {len(processor.stats['errors'])}")
        
        # 检查处理后的数据
        df_after = pd.read_excel(excel_file, sheet_name=sheet_name)
        updated_data = list(df_after[column_name])
        print(f"\n数据变化:")
        print(f"  处理前: {original_data}")
        print(f"  处理后: {updated_data}")
        
        # 步骤6：验证具体的更新结果
        print(f"\n{'='*25} 步骤6：验证更新结果 {'='*25}")
        
        # 预期的更新结果
        expected_updates = {
            'LP22': 'LP22A',      # 精确匹配
            '2-LP22': '2-LP22A',  # 前缀匹配 - 关键测试用例
            'C38': 'C38B',        # 精确匹配
            'A-C38': 'A-C38B',    # 前缀匹配
            'D12': 'D12C',        # 精确匹配
            '12-D12': '12-D12C'   # 前缀匹配
        }
        
        all_correct = True
        for i, (original, updated) in enumerate(zip(original_data, updated_data)):
            if original in expected_updates:
                expected = expected_updates[original]
                if updated == expected:
                    print(f"  ✓ 第{i+2}行: '{original}' → '{updated}' (正确)")
                else:
                    print(f"  ✗ 第{i+2}行: '{original}' → '{updated}' (期望: '{expected}')")
                    all_correct = False
            else:
                if original == updated:
                    print(f"  ○ 第{i+2}行: '{original}' (未变化，正确)")
                else:
                    print(f"  ? 第{i+2}行: '{original}' → '{updated}' (意外变化)")
        
        # 步骤7：检查备份和输出文件
        print(f"\n{'='*25} 步骤7：检查备份和输出 {'='*25}")
        
        # 检查备份文件
        backup_file = excel_file + '.backup'
        if os.path.exists(backup_file):
            print(f"✓ 备份文件已创建: {os.path.basename(backup_file)}")
        else:
            print(f"✗ 备份文件未创建")
            all_correct = False
        
        # 检查输出文件
        output_files = os.listdir(output_dir)
        excel_output_files = [f for f in output_files if f.endswith(('.xlsx', '.xls', '.xlsm'))]
        
        if excel_output_files:
            print(f"✓ 输出文件已创建: {excel_output_files}")
            
            # 验证输出文件内容
            output_excel = os.path.join(output_dir, excel_output_files[0])
            df_output = pd.read_excel(output_excel, sheet_name=sheet_name)
            output_data = list(df_output[column_name])
            
            if output_data == updated_data:
                print(f"✓ 输出文件内容正确")
            else:
                print(f"✗ 输出文件内容不正确")
                print(f"  期望: {updated_data}")
                print(f"  实际: {output_data}")
                all_correct = False
        else:
            print(f"✗ 输出文件未创建")
            all_correct = False
        
        # 步骤8：关键测试用例验证
        print(f"\n{'='*25} 步骤8：关键测试用例验证 {'='*25}")
        
        # 重点验证"2-LP22" → "2-LP22A"的更新
        key_test_passed = False
        for i, (original, updated) in enumerate(zip(original_data, updated_data)):
            if original == '2-LP22':
                if updated == '2-LP22A':
                    print(f"✅ 关键测试用例通过: '2-LP22' → '2-LP22A'")
                    key_test_passed = True
                else:
                    print(f"❌ 关键测试用例失败: '2-LP22' → '{updated}' (期望: '2-LP22A')")
                break
        
        if not key_test_passed and '2-LP22' not in original_data:
            print(f"⚠️ 关键测试用例数据不存在: '2-LP22'")
        
        # 最终结果
        print(f"\n{'='*25} 最终结果 {'='*25}")
        if all_correct and key_test_passed:
            print(f"🎉 所有测试通过！Excel前缀更新逻辑工作正常")
            print(f"  - 精确匹配正确工作")
            print(f"  - 前缀匹配正确工作")
            print(f"  - 关键测试用例 '2-LP22' → '2-LP22A' 成功")
            print(f"  - 备份机制正常")
            print(f"  - 输出文件正确")
            return True
        else:
            print(f"❌ 测试失败")
            if not all_correct:
                print(f"  - 数据更新不正确")
            if not key_test_passed:
                print(f"  - 关键测试用例失败")
            return False
        
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    try:
        success = test_excel_prefix_update()
        if success:
            print(f"\n🎯 Excel前缀更新逻辑修复成功！")
        else:
            print(f"\n💥 Excel前缀更新逻辑需要进一步修复")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
