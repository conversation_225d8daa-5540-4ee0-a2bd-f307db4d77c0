import os
import glob
import ezdxf

class DXFChecker:
    def check_multiple_graphics(self, entities):
        """检查是否包含多个独立的图形 - 基于几何连接性分析"""
        if len(entities) < 2:  # 至少需要2个实体才可能是多图形
            return False
        
        # 获取所有实体的关键点（端点、中心点等）
        entity_points = []
        for i, entity in enumerate(entities):
            points = self.get_entity_key_points(entity)
            if points:
                entity_points.append({'index': i, 'entity': entity, 'points': points})
        
        if len(entity_points) < 2:
            return False
        
        # 使用连接性分析检测分离的图形组
        groups = self.cluster_by_connectivity(entity_points)
        
        # 如果有多个明显分离的组，则认为是多图形
        return len(groups) > 1
    
    def get_entity_key_points(self, entity):
        """获取实体的关键点（端点、中心点等）"""
        try:
            entity_type = entity.dxftype()
            
            if entity_type == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                return [(start.x, start.y), (end.x, end.y)]
            
            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                return [(center.x, center.y)]
            
            elif entity_type == 'ARC':
                center = entity.dxf.center
                return [(center.x, center.y)]
            
            elif entity_type in ['POLYLINE', 'LWPOLYLINE']:
                points = []
                if hasattr(entity, 'vertices'):
                    for vertex in entity.vertices:
                        if hasattr(vertex, 'dxf') and hasattr(vertex.dxf, 'location'):
                            loc = vertex.dxf.location
                            points.append((loc.x, loc.y))
                elif hasattr(entity, 'get_points'):
                    for point in entity.get_points():
                        if isinstance(point, (list, tuple)):
                            points.append((point[0], point[1]))
                        else:
                            points.append((point.x, point.y))
                return points
            
        except Exception:
            pass
        
        return []
    
    def cluster_by_connectivity(self, entity_points):
        """基于几何连接性和包含关系将实体分组"""
        if not entity_points:
            return []
        
        groups = []
        used = [False] * len(entity_points)
        tolerance = 1.0  # 连接容差，可以根据需要调整
        
        for i, entity_data in enumerate(entity_points):
            if used[i]:
                continue
            
            # 创建新组
            group = [i]
            used[i] = True
            
            # 使用广度优先搜索找到所有连接的实体
            queue = [i]
            while queue:
                current_idx = queue.pop(0)
                current_entity = entity_points[current_idx]['entity']
                current_points = entity_points[current_idx]['points']
                
                # 检查与其他未使用实体的连接性
                for j, other_entity_data in enumerate(entity_points):
                    if used[j] or current_idx == j:
                        continue
                    
                    other_entity = other_entity_data['entity']
                    other_points = other_entity_data['points']
                    
                    # 检查连接性：1) 点相近 2) 包含关系
                    if (self.are_entities_connected(current_points, other_points, tolerance) or
                        self.are_entities_spatially_related(current_entity, other_entity)):
                        group.append(j)
                        used[j] = True
                        queue.append(j)
            
            groups.append(group)
        
        return groups
    
    def are_entities_connected(self, points1, points2, tolerance):
        """检查两组点是否有相近的点（表示实体连接）"""
        for p1 in points1:
            for p2 in points2:
                distance = ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5
                if distance <= tolerance:
                    return True
        return False
    
    def are_entities_spatially_related(self, entity1, entity2):
        """检查两个实体是否有空间关系（包含、重叠等）"""
        try:
            bbox1 = self.get_entity_bounding_box(entity1)
            bbox2 = self.get_entity_bounding_box(entity2)
            
            if not bbox1 or not bbox2:
                return False
            
            # 检查包含关系（更宽松的包含检测）
            if self.is_bbox_contained_loose(bbox1, bbox2) or self.is_bbox_contained_loose(bbox2, bbox1):
                return True
            
            # 检查重叠关系
            if self.do_bboxes_overlap(bbox1, bbox2):
                return True
            
            # 检查是否距离很近（可能是设计的一部分）
            distance = self.calculate_bbox_distance(bbox1, bbox2)
            # 计算平均尺寸
            avg_size1 = ((bbox1['max_x'] - bbox1['min_x']) + (bbox1['max_y'] - bbox1['min_y'])) / 2
            avg_size2 = ((bbox2['max_x'] - bbox2['min_x']) + (bbox2['max_y'] - bbox2['min_y'])) / 2
            avg_size = (avg_size1 + avg_size2) / 2
            
            # 如果距离小于平均尺寸的50%，认为它们相关（增加阈值）
            threshold = max(avg_size * 0.5, 10)
            return distance <= threshold
            
        except Exception:
            return False
    
    def get_entity_bounding_box(self, entity):
        """获取实体的边界框"""
        try:
            entity_type = entity.dxftype()
            
            if entity_type == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                return {
                    'min_x': min(start.x, end.x),
                    'max_x': max(start.x, end.x),
                    'min_y': min(start.y, end.y),
                    'max_y': max(start.y, end.y)
                }
            
            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                radius = entity.dxf.radius
                return {
                    'min_x': center.x - radius,
                    'max_x': center.x + radius,
                    'min_y': center.y - radius,
                    'max_y': center.y + radius
                }
            
        except Exception:
            pass
        
        return None
    
    def is_bbox_contained(self, bbox1, bbox2):
        """检查bbox1是否完全包含在bbox2内"""
        return (bbox2['min_x'] <= bbox1['min_x'] <= bbox1['max_x'] <= bbox2['max_x'] and
                bbox2['min_y'] <= bbox1['min_y'] <= bbox1['max_y'] <= bbox2['max_y'])

    def is_bbox_contained_loose(self, bbox1, bbox2):
        """检查bbox1是否大致包含在bbox2内（宽松检测）"""
        # 计算重叠面积
        overlap_x = max(0, min(bbox1['max_x'], bbox2['max_x']) - max(bbox1['min_x'], bbox2['min_x']))
        overlap_y = max(0, min(bbox1['max_y'], bbox2['max_y']) - max(bbox1['min_y'], bbox2['min_y']))
        overlap_area = overlap_x * overlap_y

        # 计算较小边界框的面积
        area1 = (bbox1['max_x'] - bbox1['min_x']) * (bbox1['max_y'] - bbox1['min_y'])
        area2 = (bbox2['max_x'] - bbox2['min_x']) * (bbox2['max_y'] - bbox2['min_y'])
        smaller_area = min(area1, area2)

        # 如果重叠面积占较小边界框面积的80%以上，认为有包含关系
        if smaller_area > 0:
            overlap_ratio = overlap_area / smaller_area
            return overlap_ratio >= 0.8

        return False
    
    def do_bboxes_overlap(self, bbox1, bbox2):
        """检查两个边界框是否重叠"""
        return not (bbox1['max_x'] < bbox2['min_x'] or bbox2['max_x'] < bbox1['min_x'] or
                   bbox1['max_y'] < bbox2['min_y'] or bbox2['max_y'] < bbox1['min_y'])
    
    def calculate_bbox_distance(self, bbox1, bbox2):
        """计算两个边界框之间的最短距离"""
        # 如果边界框重叠，距离为0
        if self.do_bboxes_overlap(bbox1, bbox2):
            return 0
        
        # 计算中心点之间的距离
        center1_x = (bbox1['min_x'] + bbox1['max_x']) / 2
        center1_y = (bbox1['min_y'] + bbox1['max_y']) / 2
        center2_x = (bbox2['min_x'] + bbox2['max_x']) / 2
        center2_y = (bbox2['min_y'] + bbox2['max_y']) / 2
        
        dx = center2_x - center1_x
        dy = center2_y - center1_y
        
        return (dx * dx + dy * dy) ** 0.5

def test_containment_detection():
    """测试包含关系检测"""
    checker = DXFChecker()
    test_folder = "测试包含关系"
    
    if not os.path.exists(test_folder):
        print(f"测试文件夹 '{test_folder}' 不存在，请先运行 '测试包含关系.py'")
        return
    
    dxf_files = glob.glob(os.path.join(test_folder, "*.dxf"))
    
    print("测试包含关系检测:")
    print("=" * 50)
    
    for dxf_file in dxf_files:
        filename = os.path.basename(dxf_file)
        print(f"\n检查文件: {filename}")
        
        try:
            doc = ezdxf.readfile(dxf_file)
            msp = doc.modelspace()
            
            # 获取图形实体
            graphic_entities = []
            for entity in msp:
                if entity.dxftype() not in ['TEXT', 'MTEXT', 'DIMENSION', 'INSERT', 'ATTRIB', 'ATTDEF']:
                    graphic_entities.append(entity)
            
            print(f"  实体数量: {len(graphic_entities)}")
            
            # 显示实体类型和边界框
            for i, entity in enumerate(graphic_entities):
                entity_type = entity.dxftype()
                bbox = checker.get_entity_bounding_box(entity)
                if bbox:
                    print(f"    实体{i+1}: {entity_type} - 边界框: ({bbox['min_x']:.1f},{bbox['min_y']:.1f}) - ({bbox['max_x']:.1f},{bbox['max_y']:.1f})")
                else:
                    print(f"    实体{i+1}: {entity_type} - 无法获取边界框")
            
            # 检测结果
            is_multiple = checker.check_multiple_graphics(graphic_entities)
            
            if is_multiple:
                entity_points = []
                for i, entity in enumerate(graphic_entities):
                    points = checker.get_entity_key_points(entity)
                    if points:
                        entity_points.append({'index': i, 'entity': entity, 'points': points})
                
                groups = checker.cluster_by_connectivity(entity_points)
                print(f"  ⚠️ 检测结果: 多图形 (发现 {len(groups)} 个分离的组)")
                for i, group in enumerate(groups):
                    print(f"    组{i+1}: 包含实体 {[g+1 for g in group]}")
            else:
                print(f"  ✓ 检测结果: 单图形")
            
        except Exception as e:
            print(f"  ✗ 读取文件出错: {str(e)}")

if __name__ == "__main__":
    test_containment_detection()
