#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清单处理工具打包脚本
使用PyInstaller将Qt程序打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    print("检查依赖包...")
    
    required_packages = [
        'PyQt5',
        'pandas', 
        'pyinstaller',
        'openpyxl'  # pandas读取Excel需要
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PyQt5':
                import PyQt5
            elif package == 'pandas':
                import pandas
            elif package == 'pyinstaller':
                import PyInstaller
            elif package == 'openpyxl':
                import openpyxl
            print(f"✓ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} - 未安装")
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请先安装缺少的包:")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    
    print("✓ 所有依赖包检查通过")
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['清单处理_Qt.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas._libs.tslibs.timedeltas',
        'pandas._libs.tslibs.np_datetime',
        'pandas._libs.tslibs.nattype',
        'pandas._libs.skiplist',
        'openpyxl',
        'xlrd',
        'xlsxwriter',
        'numpy.random._pickle',
        'numpy.random._common',
        'numpy.random._bounded_integers',
        'numpy.random.bit_generator',
        'numpy.random.mtrand',
        'numpy.random.common',
        'numpy.random.bounded_integers',
        'numpy.random.entropy'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy.random._pickle',
        'tkinter'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='清单处理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='../mylogo.ico',  # 图标文件路径
    version='version_info.txt'  # 版本信息文件
)
'''
    
    with open('清单处理工具.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建PyInstaller规格文件: 清单处理工具.spec")

def create_version_info():
    """创建版本信息文件"""
    version_content = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'材料管理系统'),
         StringStruct(u'FileDescription', u'清单处理工具'),
         StringStruct(u'FileVersion', u'*******'),
         StringStruct(u'InternalName', u'清单处理工具'),
         StringStruct(u'LegalCopyright', u'Copyright © 2024'),
         StringStruct(u'OriginalFilename', u'清单处理工具.exe'),
         StringStruct(u'ProductName', u'清单处理工具'),
         StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print("✓ 创建版本信息文件: version_info.txt")

def build_exe():
    """执行打包"""
    print("\n开始打包...")
    
    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✓ 清理build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("✓ 清理dist目录")
    
    # 执行PyInstaller
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        '清单处理工具.spec'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 打包成功!")
            
            # 检查生成的exe文件
            exe_path = Path('dist/清单处理工具.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ 生成的exe文件: {exe_path}")
                print(f"✓ 文件大小: {size_mb:.1f} MB")
                
                # 创建使用说明
                create_readme()
                
                return True
            else:
                print("✗ 未找到生成的exe文件")
                return False
        else:
            print("✗ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 打包过程出错: {str(e)}")
        return False

def create_readme():
    """创建使用说明文件"""
    readme_content = '''# 清单处理工具 使用说明

## 文件说明
- 清单处理工具.exe - 主程序
- list_processor_config.ini - 配置文件（首次运行后自动生成）

## 使用方法
1. 双击运行"清单处理工具.exe"
2. 选择包含"清单"和"NC_dxf"文件夹的项目根目录
3. 根据需要设置处理选项：
   - 处理完成后显示提醒
   - 自动创建DXF压缩包
4. 点击"开始处理"按钮
5. 根据弹出的对话框进行确认操作
6. 查看处理结果

## 功能特点
- 全局花纹板关键词检测（花、花纹、花纹板、HWB、HW）
- PL前缀数据格式确认
- 乘法格式自动处理（x替换为*）
- DXF文件ZIP压缩功能
- 智能提醒和用户交互

## 注意事项
- 确保项目目录包含"清单"和"NC_dxf"两个子文件夹
- 处理过程中请勿关闭程序
- 建议在处理前备份重要文件
- 如遇问题，请查看程序日志信息

## 系统要求
- Windows 7/8/10/11
- 无需安装Python环境
- 建议内存2GB以上

版本: 1.0.0
更新日期: 2024年
'''
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 创建使用说明文件: dist/使用说明.txt")

def main():
    """主函数"""
    print("=== 清单处理工具打包脚本 ===")
    print()
    
    # 检查当前目录
    if not os.path.exists('清单处理_Qt.py'):
        print("✗ 未找到清单处理_Qt.py文件")
        print("请确保在正确的目录下运行此脚本")
        return False
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    print()
    
    # 创建配置文件
    create_spec_file()
    create_version_info()
    
    print()
    
    # 执行打包
    success = build_exe()
    
    if success:
        print()
        print("=== 打包完成 ===")
        print("生成的文件位于 dist 目录中:")
        print("- 清单处理工具.exe")
        print("- 使用说明.txt")
        print()
        print("您可以将整个 dist 目录复制到其他计算机上使用")
    else:
        print()
        print("=== 打包失败 ===")
        print("请检查错误信息并重试")
    
    return success

if __name__ == '__main__':
    main()
