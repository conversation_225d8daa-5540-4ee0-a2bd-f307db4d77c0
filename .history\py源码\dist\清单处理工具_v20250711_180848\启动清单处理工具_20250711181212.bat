@echo off
chcp 65001 >nul
title 清单处理工具启动器

echo ========================================
echo 清单处理工具 v2025.07.11
echo ========================================
echo.
echo 正在启动程序，请稍候...
echo.

REM 检查exe文件是否存在
if not exist "清单处理工具.exe" (
    echo 错误：找不到 清单处理工具.exe 文件！
    echo 请确保此批处理文件与exe文件在同一目录下。
    pause
    exit /b 1
)

REM 启动程序
start "" "清单处理工具.exe"

REM 等待程序启动
timeout /t 3 /nobreak >nul

echo 程序已启动！
echo.
echo 如果程序没有正常启动，请：
echo 1. 检查是否有杀毒软件阻止
echo 2. 尝试以管理员身份运行
echo 3. 检查系统是否满足运行要求
echo.
echo 按任意键关闭此窗口...
pause >nul
