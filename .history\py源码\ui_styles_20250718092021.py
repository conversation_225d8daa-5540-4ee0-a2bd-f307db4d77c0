"""
UI样式表模块
提供现代化的界面样式和主题
"""

# 经典白色主题色彩配置
COLORS = {
    'primary': '#333333',      # 主色调 - 深灰
    'secondary': '#0078d4',    # 次要色 - 蓝色
    'accent': '#d13438',       # 强调色 - 红色
    'success': '#107c10',      # 成功色 - 绿色
    'warning': '#ff8c00',      # 警告色 - 橙色
    'background': '#ffffff',   # 背景色 - 白色
    'surface': '#ffffff',      # 表面色 - 白色
    'border': '#d1d1d1',       # 边框色 - 浅灰
    'text_primary': '#333333', # 主文字色 - 深灰
    'text_secondary': '#666666', # 次要文字色 - 中灰
    'hover': '#e1e1e1',        # 悬停色 - 浅灰
    'selected': '#cce8ff',     # 选中色 - 浅蓝
}

def get_main_window_style():
    """获取主窗口样式"""
    return f"""
    QMainWindow {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
        font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
        font-size: 9pt;
    }}
    
    QWidget {{
        background-color: transparent;
        color: {COLORS['text_primary']};
    }}
    """

def get_group_box_style():
    """获取分组框样式"""
    return f"""
    QGroupBox {{
        font-weight: bold;
        font-size: 9pt;
        color: {COLORS['text_primary']};
        border: 1px solid {COLORS['border']};
        border-radius: 4px;
        margin-top: 10px;
        padding-top: 6px;
        background-color: {COLORS['surface']};
    }}

    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 6px 0 6px;
        color: {COLORS['text_primary']};
        background-color: {COLORS['surface']};
    }}
    """

def get_button_style():
    """获取按钮样式"""
    return f"""
    QPushButton {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 1px solid {COLORS['border']};
        border-radius: 4px;
        padding: 6px 12px;
        font-weight: normal;
        font-size: 9pt;
        min-height: 20px;
    }}

    QPushButton:hover {{
        background-color: {COLORS['hover']};
        border-color: {COLORS['secondary']};
    }}

    QPushButton:pressed {{
        background-color: {COLORS['selected']};
        border-color: {COLORS['secondary']};
    }}

    QPushButton:disabled {{
        background-color: #f5f5f5;
        color: {COLORS['text_secondary']};
        border-color: #e0e0e0;
    }}

    /* 特殊按钮样式 */
    QPushButton[buttonType="primary"] {{
        background-color: {COLORS['secondary']};
        color: white;
        border-color: {COLORS['secondary']};
    }}

    QPushButton[buttonType="primary"]:hover {{
        background-color: #106ebe;
        border-color: #106ebe;
    }}

    QPushButton[buttonType="success"] {{
        background-color: {COLORS['success']};
        color: white;
        border-color: {COLORS['success']};
    }}

    QPushButton[buttonType="success"]:hover {{
        background-color: #0e6e0e;
        border-color: #0e6e0e;
    }}

    QPushButton[buttonType="danger"] {{
        background-color: {COLORS['accent']};
        color: white;
        border-color: {COLORS['accent']};
    }}

    QPushButton[buttonType="danger"]:hover {{
        background-color: #b52d32;
        border-color: #b52d32;
    }}
    """

def get_input_style():
    """获取输入框样式"""
    return f"""
    QLineEdit, QTextEdit, QPlainTextEdit {{
        border: 1px solid {COLORS['border']};
        border-radius: 3px;
        padding: 6px 8px;
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        font-size: 9pt;
        selection-background-color: {COLORS['selected']};
    }}

    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
        border-color: {COLORS['secondary']};
        outline: none;
    }}

    QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {{
        border-color: #999999;
    }}

    QTextEdit {{
        line-height: 1.4;
    }}
    """

def get_combo_box_style():
    """获取下拉框样式"""
    return f"""
    QComboBox {{
        border: 2px solid {COLORS['border']};
        border-radius: 6px;
        padding: 6px 12px;
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        font-size: 9pt;
        min-height: 20px;
    }}
    
    QComboBox:hover {{
        border-color: {COLORS['hover']};
    }}
    
    QComboBox:focus {{
        border-color: {COLORS['secondary']};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 20px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {COLORS['text_secondary']};
        margin-right: 5px;
    }}
    
    QComboBox QAbstractItemView {{
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
        background-color: {COLORS['surface']};
        selection-background-color: {COLORS['secondary']};
        selection-color: white;
        padding: 4px;
    }}
    """

def get_checkbox_style():
    """获取复选框样式"""
    return f"""
    QCheckBox {{
        color: {COLORS['text_primary']};
        font-size: 9pt;
        spacing: 8px;
    }}
    
    QCheckBox::indicator {{
        width: 16px;
        height: 16px;
        border: 2px solid {COLORS['border']};
        border-radius: 3px;
        background-color: {COLORS['surface']};
    }}
    
    QCheckBox::indicator:hover {{
        border-color: {COLORS['secondary']};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {COLORS['secondary']};
        border-color: {COLORS['secondary']};
        image: none;
    }}
    
    QCheckBox::indicator:checked::after {{
        content: "✓";
        color: white;
        font-weight: bold;
        font-size: 12px;
    }}
    """

def get_tab_widget_style():
    """获取标签页样式"""
    return f"""
    QTabWidget::pane {{
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
        background-color: {COLORS['surface']};
        margin-top: -1px;
    }}
    
    QTabBar::tab {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
        border: 1px solid {COLORS['border']};
        border-bottom: none;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        padding: 8px 16px;
        margin-right: 2px;
        font-weight: bold;
        font-size: 9pt;
    }}
    
    QTabBar::tab:selected {{
        background-color: {COLORS['surface']};
        color: {COLORS['secondary']};
        border-color: {COLORS['secondary']};
    }}
    
    QTabBar::tab:hover:!selected {{
        background-color: {COLORS['hover']};
        color: white;
    }}
    """

def get_splitter_style():
    """获取分割器样式"""
    return f"""
    QSplitter::handle {{
        background-color: {COLORS['border']};
        border-radius: 2px;
    }}
    
    QSplitter::handle:horizontal {{
        width: 3px;
        margin: 2px 0;
    }}
    
    QSplitter::handle:vertical {{
        height: 3px;
        margin: 0 2px;
    }}
    
    QSplitter::handle:hover {{
        background-color: {COLORS['secondary']};
    }}
    """

def get_scroll_area_style():
    """获取滚动区域样式"""
    return f"""
    QScrollArea {{
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
        background-color: {COLORS['surface']};
    }}
    
    QScrollBar:vertical {{
        background-color: {COLORS['background']};
        width: 12px;
        border-radius: 6px;
        margin: 0;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COLORS['border']};
        border-radius: 6px;
        min-height: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {COLORS['secondary']};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    
    QScrollBar:horizontal {{
        background-color: {COLORS['background']};
        height: 12px;
        border-radius: 6px;
        margin: 0;
    }}
    
    QScrollBar::handle:horizontal {{
        background-color: {COLORS['border']};
        border-radius: 6px;
        min-width: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background-color: {COLORS['secondary']};
    }}
    
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
        width: 0px;
    }}
    """

def get_complete_style():
    """获取完整的应用程序样式"""
    return (
        get_main_window_style() +
        get_group_box_style() +
        get_button_style() +
        get_input_style() +
        get_combo_box_style() +
        get_checkbox_style() +
        get_tab_widget_style() +
        get_splitter_style() +
        get_scroll_area_style()
    )
