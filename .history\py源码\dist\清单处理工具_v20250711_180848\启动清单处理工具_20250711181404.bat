@echo off
title List Processing Tool Launcher

echo ========================================
echo List Processing Tool v2025.07.11
echo ========================================
echo.
echo Starting program, please wait...
echo.

REM Check if exe file exists
if not exist "清单处理工具.exe" (
    echo Error: Cannot find exe file!
    echo Please ensure this batch file is in the same directory as the exe.
    pause
    exit /b 1
)

REM Start the program
start "" "清单处理工具.exe"

REM Wait for program to start
timeout /t 3 /nobreak >nul

echo Program started!
echo.
echo If the program did not start properly, please:
echo 1. Check if antivirus software is blocking it
echo 2. Try running as administrator
echo 3. Check system requirements
echo.
echo Press any key to close this window...
pause >nul
