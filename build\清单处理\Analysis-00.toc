(['C:\\Users\\<USER>\\Desktop\\python工具箱\\py源码\\清单处理_Qt.py'],
 ['C:\\Users\\<USER>\\Desktop\\python工具箱\\py源码'],
 ['PyQt5.QtCore',
  'PyQt5.QtGui',
  'PyQt5.QtWidgets',
  'pandas',
  'openpyxl',
  'xlsxwriter',
  'ezdxf',
  'configparser',
  'zipfile',
  'datetime'],
 [('D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.21 (main, Dec 11 2024, 16:35:24) [MSC v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('清单处理_Qt',
   'C:\\Users\\<USER>\\Desktop\\python工具箱\\py源码\\清单处理_Qt.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\Miniconda3\\envs\\Excel\\lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'D:\\Miniconda3\\envs\\Excel\\lib\\contextlib.py', 'PYMODULE'),
  ('pathlib', 'D:\\Miniconda3\\envs\\Excel\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\Miniconda3\\envs\\Excel\\lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Miniconda3\\envs\\Excel\\lib\\fnmatch.py', 'PYMODULE'),
  ('email', 'D:\\Miniconda3\\envs\\Excel\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\Miniconda3\\envs\\Excel\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\Miniconda3\\envs\\Excel\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Miniconda3\\envs\\Excel\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\Miniconda3\\envs\\Excel\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Miniconda3\\envs\\Excel\\lib\\gettext.py', 'PYMODULE'),
  ('struct', 'D:\\Miniconda3\\envs\\Excel\\lib\\struct.py', 'PYMODULE'),
  ('socket', 'D:\\Miniconda3\\envs\\Excel\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Miniconda3\\envs\\Excel\\lib\\selectors.py', 'PYMODULE'),
  ('random', 'D:\\Miniconda3\\envs\\Excel\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\Miniconda3\\envs\\Excel\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\Miniconda3\\envs\\Excel\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Miniconda3\\envs\\Excel\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions', 'D:\\Miniconda3\\envs\\Excel\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Miniconda3\\envs\\Excel\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Miniconda3\\envs\\Excel\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\Miniconda3\\envs\\Excel\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Miniconda3\\envs\\Excel\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Miniconda3\\envs\\Excel\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('threading', 'D:\\Miniconda3\\envs\\Excel\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_threading_local.py',
   'PYMODULE'),
  ('string', 'D:\\Miniconda3\\envs\\Excel\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\Miniconda3\\envs\\Excel\\lib\\bisect.py', 'PYMODULE'),
  ('_strptime', 'D:\\Miniconda3\\envs\\Excel\\lib\\_strptime.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Miniconda3\\envs\\Excel\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Miniconda3\\envs\\Excel\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'D:\\Miniconda3\\envs\\Excel\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\Miniconda3\\envs\\Excel\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\Miniconda3\\envs\\Excel\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'D:\\Miniconda3\\envs\\Excel\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\Miniconda3\\envs\\Excel\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Miniconda3\\envs\\Excel\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('subprocess', 'D:\\Miniconda3\\envs\\Excel\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\Miniconda3\\envs\\Excel\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\Miniconda3\\envs\\Excel\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\Miniconda3\\envs\\Excel\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Miniconda3\\envs\\Excel\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\Miniconda3\\envs\\Excel\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Miniconda3\\envs\\Excel\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\Miniconda3\\envs\\Excel\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Miniconda3\\envs\\Excel\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\Miniconda3\\envs\\Excel\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\Miniconda3\\envs\\Excel\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Miniconda3\\envs\\Excel\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Miniconda3\\envs\\Excel\\lib\\http\\client.py',
   'PYMODULE'),
  ('hmac', 'D:\\Miniconda3\\envs\\Excel\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'D:\\Miniconda3\\envs\\Excel\\lib\\tempfile.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Miniconda3\\envs\\Excel\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Miniconda3\\envs\\Excel\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Miniconda3\\envs\\Excel\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Miniconda3\\envs\\Excel\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Miniconda3\\envs\\Excel\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Miniconda3\\envs\\Excel\\lib\\zipimport.py', 'PYMODULE'),
  ('inspect', 'D:\\Miniconda3\\envs\\Excel\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Miniconda3\\envs\\Excel\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Miniconda3\\envs\\Excel\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Miniconda3\\envs\\Excel\\lib\\ast.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.color',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Miniconda3\\envs\\Excel\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Miniconda3\\envs\\Excel\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Miniconda3\\envs\\Excel\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput', 'D:\\Miniconda3\\envs\\Excel\\lib\\fileinput.py', 'PYMODULE'),
  ('bz2', 'D:\\Miniconda3\\envs\\Excel\\lib\\bz2.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\Miniconda3\\envs\\Excel\\lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'D:\\Miniconda3\\envs\\Excel\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\Miniconda3\\envs\\Excel\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\Miniconda3\\envs\\Excel\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Miniconda3\\envs\\Excel\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'D:\\Miniconda3\\envs\\Excel\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\socketserver.py',
   'PYMODULE'),
  ('html', 'D:\\Miniconda3\\envs\\Excel\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Miniconda3\\envs\\Excel\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Miniconda3\\envs\\Excel\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Miniconda3\\envs\\Excel\\lib\\tty.py', 'PYMODULE'),
  ('code', 'D:\\Miniconda3\\envs\\Excel\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Miniconda3\\envs\\Excel\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\Miniconda3\\envs\\Excel\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Miniconda3\\envs\\Excel\\lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\Miniconda3\\envs\\Excel\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.case',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Miniconda3\\envs\\Excel\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('lzma', 'D:\\Miniconda3\\envs\\Excel\\lib\\lzma.py', 'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('json', 'D:\\Miniconda3\\envs\\Excel\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Miniconda3\\envs\\Excel\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('platform', 'D:\\Miniconda3\\envs\\Excel\\lib\\platform.py', 'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('__future__', 'D:\\Miniconda3\\envs\\Excel\\lib\\__future__.py', 'PYMODULE'),
  ('PIL',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Miniconda3\\envs\\Excel\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\Miniconda3\\envs\\Excel\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('ezdxf',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\__init__.py',
   'PYMODULE'),
  ('ezdxf.lldxf.encoding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\encoding.py',
   'PYMODULE'),
  ('ezdxf.render.arrows',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\arrows.py',
   'PYMODULE'),
  ('ezdxf.render',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\__init__.py',
   'PYMODULE'),
  ('ezdxf.render.point',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\point.py',
   'PYMODULE'),
  ('ezdxf.entities.factory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\factory.py',
   'PYMODULE'),
  ('ezdxf.lldxf.extendedtags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\extendedtags.py',
   'PYMODULE'),
  ('ezdxf.lldxf.tagger',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\tagger.py',
   'PYMODULE'),
  ('ezdxf.tools.codepage',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\codepage.py',
   'PYMODULE'),
  ('ezdxf.lldxf.tags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\tags.py',
   'PYMODULE'),
  ('ezdxf.lldxf.types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\types.py',
   'PYMODULE'),
  ('ezdxf.document',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\document.py',
   'PYMODULE'),
  ('ezdxf.units',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\units.py',
   'PYMODULE'),
  ('ezdxf.tools.handle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\handle.py',
   'PYMODULE'),
  ('ezdxf.tools.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\text.py',
   'PYMODULE'),
  ('ezdxf.fonts.fonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\fonts.py',
   'PYMODULE'),
  ('fontTools.ttLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttCollection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttCollection.py',
   'PYMODULE'),
  ('fontTools.misc.xmlWriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\xmlWriter.py',
   'PYMODULE'),
  ('fontTools.misc.textTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\textTools.py',
   'PYMODULE'),
  ('fontTools.misc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.xmlReader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\xmlReader.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.DefaultTable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\DefaultTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_m_t_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_h_e_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_h_e_a.py',
   'PYMODULE'),
  ('fontTools.misc.fixedTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\fixedTools.py',
   'PYMODULE'),
  ('fontTools.misc.roundTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\roundTools.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._t_r_a_k',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_t_r_a_k.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._s_b_i_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_s_b_i_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixStrike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixStrike.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixGlyph',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixGlyph.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_o_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_o_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otBase',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otBase.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_e_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_e_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_o_s_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_o_s_t.py',
   'PYMODULE'),
  ('fontTools.agl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\agl.py',
   'PYMODULE'),
  ('fontTools.ttLib.standardGlyphOrder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\standardGlyphOrder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._o_p_b_d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_o_p_b_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_e_t_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_e_t_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_a_x_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_a_x_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_t_a_g',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_t_a_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_o_c_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_o_c_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_c_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_c_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._k_e_r_n',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_k_e_r_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_m_t_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_h_e_a',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_e_a_d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_e_a_d.py',
   'PYMODULE'),
  ('fontTools.misc.arrayTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\arrayTools.py',
   'PYMODULE'),
  ('fontTools.misc.vector',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\vector.py',
   'PYMODULE'),
  ('fontTools.misc.timeTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\timeTools.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_d_m_x',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_d_m_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.TupleVariation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\TupleVariation.py',
   'PYMODULE'),
  ('fontTools.varLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttx',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttx.py',
   'PYMODULE'),
  ('fontTools.misc.cliTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\cliTools.py',
   'PYMODULE'),
  ('fontTools.unicode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\unicode.py',
   'PYMODULE'),
  ('fontTools.misc.macCreatorType',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\macCreatorType.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFFToCFF2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\CFFToCFF2.py',
   'PYMODULE'),
  ('fontTools.cffLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.cffLib.transforms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\transforms.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFF2ToCFF',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\CFF2ToCFF.py',
   'PYMODULE'),
  ('fontTools.cffLib.width',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\width.py',
   'PYMODULE'),
  ('fontTools.varLib.cff',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\cff.py',
   'PYMODULE'),
  ('fontTools.pens.t2CharStringPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\t2CharStringPen.py',
   'PYMODULE'),
  ('fontTools.pens',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\__init__.py',
   'PYMODULE'),
  ('fontTools.pens.basePen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\basePen.py',
   'PYMODULE'),
  ('fontTools.pens.reverseContourPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\reverseContourPen.py',
   'PYMODULE'),
  ('fontTools.pens.filterPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\filterPen.py',
   'PYMODULE'),
  ('fontTools.pens.recordingPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\recordingPen.py',
   'PYMODULE'),
  ('fontTools.pens.pointPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\pointPen.py',
   'PYMODULE'),
  ('fontTools.pens.transformPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\transformPen.py',
   'PYMODULE'),
  ('fontTools.misc.transform',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\transform.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Miniconda3\\envs\\Excel\\lib\\dataclasses.py',
   'PYMODULE'),
  ('fontTools.cffLib.specializer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\cffLib\\specializer.py',
   'PYMODULE'),
  ('fontTools.otlLib.builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\builder.py',
   'PYMODULE'),
  ('fontTools.otlLib.error',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\error.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize.gpos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\optimize\\gpos.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\optimize\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.intTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\intTools.py',
   'PYMODULE'),
  ('fontTools.otlLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\otlLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.ast',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\ast.py',
   'PYMODULE'),
  ('fontTools.feaLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.encodingTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\encodingTools.py',
   'PYMODULE'),
  ('fontTools.encodings.codecs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\encodings\\codecs.py',
   'PYMODULE'),
  ('fontTools.encodings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\encodings\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.location',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\location.py',
   'PYMODULE'),
  ('fontTools.feaLib.error',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\error.py',
   'PYMODULE'),
  ('fontTools.varLib.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\errors.py',
   'PYMODULE'),
  ('fontTools.colorLib.unbuilder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\unbuilder.py',
   'PYMODULE'),
  ('fontTools.colorLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.colorLib.table_builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\table_builder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otConverters',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otConverters.py',
   'PYMODULE'),
  ('fontTools.colorLib.builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.geometry',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\geometry.py',
   'PYMODULE'),
  ('fontTools.colorLib.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\colorLib\\errors.py',
   'PYMODULE'),
  ('fontTools.misc.treeTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\treeTools.py',
   'PYMODULE'),
  ('fontTools.varLib.stat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\stat.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\types.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.split',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\split.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.statNames',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\statNames.py',
   'PYMODULE'),
  ('fontTools.designspaceLib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\designspaceLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.varLib.featureVars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\featureVars.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttVisitor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttVisitor.py',
   'PYMODULE'),
  ('fontTools.misc.visitor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\visitor.py',
   'PYMODULE'),
  ('fontTools.misc.dictTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\dictTools.py',
   'PYMODULE'),
  ('fontTools.varLib.mvar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\mvar.py',
   'PYMODULE'),
  ('fontTools.varLib.merger',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\merger.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTraverse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTraverse.py',
   'PYMODULE'),
  ('fontTools.varLib.varStore',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\varStore.py',
   'PYMODULE'),
  ('fontTools.varLib.models',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\models.py',
   'PYMODULE'),
  ('fontTools.varLib.builder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\builder.py',
   'PYMODULE'),
  ('fontTools.misc.lazyTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\lazyTools.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_l_y_f',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_l_y_f.py',
   'PYMODULE'),
  ('fontTools.misc.filenames',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\filenames.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_c_i_d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_c_i_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_a_s_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_a_s_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_p_g_m',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_p_g_m.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.ttProgram',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\ttProgram.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_e_a_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_m_a_p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_m_a_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_i_d_g',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_i_d_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._b_s_l_n',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_b_s_l_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_v_a_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_n_k_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_n_k_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_O_R_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_O_R_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_D_M_X_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_D_M_X_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_A_R_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_A_R_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_T_F_A_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_T_F_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__5.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__3.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__1',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__1.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__0.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_V_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_V_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_S_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_P_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_J_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_J_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_D_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_D_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_B_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.asciiTable',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\asciiTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_l',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_l.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_f',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_V_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_V_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_T_A_T_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_T_A_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_I_N_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_I_N_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.O_S_2f_2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\O_S_2f_2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_E_T_A_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_E_T_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_A_T_H_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_A_T_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.L_T_S_H_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\L_T_S_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.J_S_T_F_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\J_S_T_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.H_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\H_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_o_c',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_o_c.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_a_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_V_A_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_S_U_B_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_S_U_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_O_S_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_O_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_K_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_K_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_M_A_P_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_M_A_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_D_E_F_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_D_E_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F__e_a_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\F__e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.grUtils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\grUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F_F_T_M_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\F_F_T_M_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D__e_b_g',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\D__e_b_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D_S_I_G_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\D_S_I_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._n_a_m_e',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\_n_a_m_e.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_P_A_L_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_P_A_L_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_O_L_R_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_O_L_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F__2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_L_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_L_C_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.BitmapGlyphMetrics',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\BitmapGlyphMetrics.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_D_T_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_D_T_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.B_A_S_E_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\B_A_S_E_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTables.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otData',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\tables\\otData.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttGlyphSet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttGlyphSet.py',
   'PYMODULE'),
  ('fontTools.varLib.multiVarStore',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\multiVarStore.py',
   'PYMODULE'),
  ('fontTools.misc.iterTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\iterTools.py',
   'PYMODULE'),
  ('fontTools.feaLib.lookupDebugInfo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\feaLib\\lookupDebugInfo.py',
   'PYMODULE'),
  ('fontTools.pens.boundsPen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\pens\\boundsPen.py',
   'PYMODULE'),
  ('fontTools.misc.classifyTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\classifyTools.py',
   'PYMODULE'),
  ('fontTools.misc.plistlib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\plistlib\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.etree',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\etree.py',
   'PYMODULE'),
  ('fontTools.misc.psCharStrings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\psCharStrings.py',
   'PYMODULE'),
  ('fontTools.encodings.StandardEncoding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\encodings\\StandardEncoding.py',
   'PYMODULE'),
  ('fontTools.misc.sstruct',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\sstruct.py',
   'PYMODULE'),
  ('fontTools.misc.cython',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\cython.py',
   'PYMODULE'),
  ('fontTools.ttLib.sfnt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\sfnt.py',
   'PYMODULE'),
  ('fontTools.ttLib.woff2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\woff2.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttFont',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\ttFont.py',
   'PYMODULE'),
  ('fontTools.ttLib.reorderGlyphs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\reorderGlyphs.py',
   'PYMODULE'),
  ('fontTools.misc.configTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\configTools.py',
   'PYMODULE'),
  ('fontTools.ttLib.macUtils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\ttLib\\macUtils.py',
   'PYMODULE'),
  ('fontTools.misc.macRes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\macRes.py',
   'PYMODULE'),
  ('fontTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.loggingTools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\loggingTools.py',
   'PYMODULE'),
  ('timeit', 'D:\\Miniconda3\\envs\\Excel\\lib\\timeit.py', 'PYMODULE'),
  ('fontTools.config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\config\\__init__.py',
   'PYMODULE'),
  ('ezdxf.fonts.ttfonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\ttfonts.py',
   'PYMODULE'),
  ('ezdxf.fonts.glyphs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\glyphs.py',
   'PYMODULE'),
  ('ezdxf.npshapes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\npshapes.py',
   'PYMODULE'),
  ('ezdxf.addons.xqt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\xqt.py',
   'PYMODULE'),
  ('ezdxf.addons',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\__init__.py',
   'PYMODULE'),
  ('ezdxf.addons.mtxpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\mtxpl.py',
   'PYMODULE'),
  ('ezdxf.render.abstract_mtext_renderer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\abstract_mtext_renderer.py',
   'PYMODULE'),
  ('ezdxf.entities.mtext',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mtext.py',
   'PYMODULE'),
  ('ezdxf.entities.copy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\copy.py',
   'PYMODULE'),
  ('ezdxf.entities.xdata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\xdata.py',
   'PYMODULE'),
  ('ezdxf.lldxf.repair',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\repair.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfgfx',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfgfx.py',
   'PYMODULE'),
  ('ezdxf.proxygraphic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\proxygraphic.py',
   'PYMODULE'),
  ('ezdxf.tools.binarydata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\binarydata.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfentity',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfentity.py',
   'PYMODULE'),
  ('ezdxf.entities.xdict',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\xdict.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfns',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfns.py',
   'PYMODULE'),
  ('ezdxf.entities.appdata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\appdata.py',
   'PYMODULE'),
  ('uuid', 'D:\\Miniconda3\\envs\\Excel\\lib\\uuid.py', 'PYMODULE'),
  ('ezdxf.math.transformtools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\transformtools.py',
   'PYMODULE'),
  ('ezdxf.lldxf.attributes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\attributes.py',
   'PYMODULE'),
  ('ezdxf.tools.text_layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\text_layout.py',
   'PYMODULE'),
  ('ezdxf.addons.r12writer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\r12writer.py',
   'PYMODULE'),
  ('ezdxf.addons.importer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\importer.py',
   'PYMODULE'),
  ('ezdxf.addons.dimlines',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\dimlines.py',
   'PYMODULE'),
  ('ezdxf.addons.sierpinski_pyramid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\sierpinski_pyramid.py',
   'PYMODULE'),
  ('ezdxf.addons.menger_sponge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\menger_sponge.py',
   'PYMODULE'),
  ('ezdxf.addons.tablepainter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\tablepainter.py',
   'PYMODULE'),
  ('ezdxf.addons.mtextsurrogate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\mtextsurrogate.py',
   'PYMODULE'),
  ('ezdxf.addons.mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\addons\\mixins.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('ezdxf.acc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\__init__.py',
   'PYMODULE'),
  ('ezdxf.path.nesting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\nesting.py',
   'PYMODULE'),
  ('ezdxf.protocols',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\protocols.py',
   'PYMODULE'),
  ('ezdxf.entities.temporary_transform',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\temporary_transform.py',
   'PYMODULE'),
  ('ezdxf.transform',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\transform.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_measurements',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_measurements.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_synonyms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_synonyms.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_manager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_manager.py',
   'PYMODULE'),
  ('ezdxf.fonts.lff',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\lff.py',
   'PYMODULE'),
  ('ezdxf.fonts.shapefile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\shapefile.py',
   'PYMODULE'),
  ('ezdxf.fonts.font_face',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\font_face.py',
   'PYMODULE'),
  ('ezdxf.fonts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\fonts\\__init__.py',
   'PYMODULE'),
  ('ezdxf.tools.juliandate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\juliandate.py',
   'PYMODULE'),
  ('ezdxf.sections.tables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\tables.py',
   'PYMODULE'),
  ('ezdxf.sections',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\__init__.py',
   'PYMODULE'),
  ('ezdxf.sections.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\table.py',
   'PYMODULE'),
  ('ezdxf.entities.table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\table.py',
   'PYMODULE'),
  ('ezdxf.sections.objects',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\objects.py',
   'PYMODULE'),
  ('ezdxf.entities.image',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\image.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfobj',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfobj.py',
   'PYMODULE'),
  ('ezdxf.entities.dictionary',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dictionary.py',
   'PYMODULE'),
  ('ezdxf.sections.header',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\header.py',
   'PYMODULE'),
  ('ezdxf.sections.headervars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\headervars.py',
   'PYMODULE'),
  ('ezdxf.lldxf.hdrvars',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\hdrvars.py',
   'PYMODULE'),
  ('ezdxf.sections.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\entities.py',
   'PYMODULE'),
  ('ezdxf.sections.classes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\classes.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfclass',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfclass.py',
   'PYMODULE'),
  ('ezdxf.sections.acdsdata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\acdsdata.py',
   'PYMODULE'),
  ('ezdxf.render.dimension',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dimension.py',
   'PYMODULE'),
  ('ezdxf.render.dim_radius',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_radius.py',
   'PYMODULE'),
  ('ezdxf.render.dim_ordinate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_ordinate.py',
   'PYMODULE'),
  ('ezdxf.render.dim_linear',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_linear.py',
   'PYMODULE'),
  ('ezdxf.render.dim_diameter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_diameter.py',
   'PYMODULE'),
  ('ezdxf.render.dim_curved',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_curved.py',
   'PYMODULE'),
  ('ezdxf.render.dim_base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\dim_base.py',
   'PYMODULE'),
  ('ezdxf.entities.dimstyleoverride',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dimstyleoverride.py',
   'PYMODULE'),
  ('ezdxf.query',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\query.py',
   'PYMODULE'),
  ('ezdxf.queryparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\queryparser.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('ezdxf.lldxf.tagwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\tagwriter.py',
   'PYMODULE'),
  ('ezdxf.lldxf.loader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\loader.py',
   'PYMODULE'),
  ('ezdxf.layouts.layouts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\layouts.py',
   'PYMODULE'),
  ('ezdxf.layouts.layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\layout.py',
   'PYMODULE'),
  ('ezdxf.layouts.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\base.py',
   'PYMODULE'),
  ('ezdxf.graphicsfactory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\graphicsfactory.py',
   'PYMODULE'),
  ('ezdxf.entities.mtext_columns',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mtext_columns.py',
   'PYMODULE'),
  ('ezdxf.groupby',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\groupby.py',
   'PYMODULE'),
  ('ezdxf.entitydb',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entitydb.py',
   'PYMODULE'),
  ('ezdxf.entities.mline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mline.py',
   'PYMODULE'),
  ('ezdxf.explode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\explode.py',
   'PYMODULE'),
  ('ezdxf.entities.polygon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\polygon.py',
   'PYMODULE'),
  ('ezdxf.entities.pattern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\pattern.py',
   'PYMODULE'),
  ('ezdxf.entities.gradient',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\gradient.py',
   'PYMODULE'),
  ('ezdxf.entities.boundary_paths',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\boundary_paths.py',
   'PYMODULE'),
  ('ezdxf.render.mline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\mline.py',
   'PYMODULE'),
  ('ezdxf.entities.objectcollection',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\objectcollection.py',
   'PYMODULE'),
  ('ezdxf.entities.mleader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mleader.py',
   'PYMODULE'),
  ('ezdxf.entities.material',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\material.py',
   'PYMODULE'),
  ('ezdxf.entities.dxfgroups',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dxfgroups.py',
   'PYMODULE'),
  ('ezdxf.audit',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\audit.py',
   'PYMODULE'),
  ('ezdxf.render.mleader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\mleader.py',
   'PYMODULE'),
  ('ezdxf.tools.text_size',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\text_size.py',
   'PYMODULE'),
  ('ezdxf.render.hatching',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\hatching.py',
   'PYMODULE'),
  ('ezdxf.render.trace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\trace.py',
   'PYMODULE'),
  ('ezdxf.render.mesh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\mesh.py',
   'PYMODULE'),
  ('ezdxf.math.triangulation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\triangulation.py',
   'PYMODULE'),
  ('ezdxf.math._mapbox_earcut',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_mapbox_earcut.py',
   'PYMODULE'),
  ('ezdxf.acis.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\api.py',
   'PYMODULE'),
  ('ezdxf.acis.cache',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\cache.py',
   'PYMODULE'),
  ('ezdxf.acis.type_hints',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\type_hints.py',
   'PYMODULE'),
  ('ezdxf.acis.dxf',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\dxf.py',
   'PYMODULE'),
  ('ezdxf.acis.dbg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\dbg.py',
   'PYMODULE'),
  ('ezdxf.acis.sab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\sab.py',
   'PYMODULE'),
  ('ezdxf.acis.abstract',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\abstract.py',
   'PYMODULE'),
  ('ezdxf.acis.hdr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\hdr.py',
   'PYMODULE'),
  ('ezdxf.acis.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\entities.py',
   'PYMODULE'),
  ('ezdxf.acis.sat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\sat.py',
   'PYMODULE'),
  ('ezdxf.acis.mesh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\mesh.py',
   'PYMODULE'),
  ('ezdxf.acis.const',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\const.py',
   'PYMODULE'),
  ('ezdxf.acis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acis\\__init__.py',
   'PYMODULE'),
  ('ezdxf.render.curves',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\curves.py',
   'PYMODULE'),
  ('ezdxf.math.perlin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\perlin.py',
   'PYMODULE'),
  ('ezdxf.render.r12spline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\r12spline.py',
   'PYMODULE'),
  ('ezdxf.layouts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\__init__.py',
   'PYMODULE'),
  ('ezdxf.layouts.blocklayout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\layouts\\blocklayout.py',
   'PYMODULE'),
  ('ezdxf.eztypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\eztypes.py',
   'PYMODULE'),
  ('ezdxf.sections.blocks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\sections\\blocks.py',
   'PYMODULE'),
  ('ezdxf.entities',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\__init__.py',
   'PYMODULE'),
  ('ezdxf.entities.geodata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\geodata.py',
   'PYMODULE'),
  ('ezdxf.lldxf.packedtags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\packedtags.py',
   'PYMODULE'),
  ('ezdxf.tools.indexing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\indexing.py',
   'PYMODULE'),
  ('ezdxf.entities.acad_table',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\acad_table.py',
   'PYMODULE'),
  ('ezdxf.entities.light',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\light.py',
   'PYMODULE'),
  ('ezdxf.entities.acis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\acis.py',
   'PYMODULE'),
  ('ezdxf.tools.crypt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\crypt.py',
   'PYMODULE'),
  ('ezdxf.entities.helix',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\helix.py',
   'PYMODULE'),
  ('ezdxf.entities.tolerance',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\tolerance.py',
   'PYMODULE'),
  ('ezdxf.entities.leader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\leader.py',
   'PYMODULE'),
  ('ezdxf.render.leader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\leader.py',
   'PYMODULE'),
  ('ezdxf.entities.underlay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\underlay.py',
   'PYMODULE'),
  ('ezdxf.entities.mpolygon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mpolygon.py',
   'PYMODULE'),
  ('ezdxf.entities.hatch',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\hatch.py',
   'PYMODULE'),
  ('ezdxf.entities.mesh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\mesh.py',
   'PYMODULE'),
  ('ezdxf.entities.spline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\spline.py',
   'PYMODULE'),
  ('ezdxf.entities.xline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\xline.py',
   'PYMODULE'),
  ('ezdxf.entities.ellipse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\ellipse.py',
   'PYMODULE'),
  ('ezdxf.math.ellipse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\ellipse.py',
   'PYMODULE'),
  ('ezdxf.entities.lwpolyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\lwpolyline.py',
   'PYMODULE'),
  ('ezdxf.render.polyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\polyline.py',
   'PYMODULE'),
  ('ezdxf.entities.viewport',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\viewport.py',
   'PYMODULE'),
  ('ezdxf.entities.dimension',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dimension.py',
   'PYMODULE'),
  ('ezdxf.entities.attrib',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\attrib.py',
   'PYMODULE'),
  ('ezdxf.entities.polyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\polyline.py',
   'PYMODULE'),
  ('ezdxf.entities.block',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\block.py',
   'PYMODULE'),
  ('ezdxf.entities.insert',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\insert.py',
   'PYMODULE'),
  ('ezdxf.entities.subentity',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\subentity.py',
   'PYMODULE'),
  ('ezdxf.entities.text',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\text.py',
   'PYMODULE'),
  ('ezdxf.entities.solid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\solid.py',
   'PYMODULE'),
  ('ezdxf.entities.shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\shape.py',
   'PYMODULE'),
  ('ezdxf.entities.arc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\arc.py',
   'PYMODULE'),
  ('ezdxf.entities.circle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\circle.py',
   'PYMODULE'),
  ('ezdxf.entities.point',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\point.py',
   'PYMODULE'),
  ('ezdxf.entities.line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\line.py',
   'PYMODULE'),
  ('ezdxf.entities.visualstyle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\visualstyle.py',
   'PYMODULE'),
  ('ezdxf.entities.spatial_filter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\spatial_filter.py',
   'PYMODULE'),
  ('ezdxf.entities.oleframe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\oleframe.py',
   'PYMODULE'),
  ('ezdxf.entities.sun',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\sun.py',
   'PYMODULE'),
  ('ezdxf.entities.idbuffer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\idbuffer.py',
   'PYMODULE'),
  ('ezdxf.entities.layout',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\layout.py',
   'PYMODULE'),
  ('ezdxf.entities.acad_proxy_entity',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\acad_proxy_entity.py',
   'PYMODULE'),
  ('ezdxf.entities.blockrecord',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\blockrecord.py',
   'PYMODULE'),
  ('ezdxf.entities.appid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\appid.py',
   'PYMODULE'),
  ('ezdxf.entities.ucs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\ucs.py',
   'PYMODULE'),
  ('ezdxf.entities.vport',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\vport.py',
   'PYMODULE'),
  ('ezdxf.entities.view',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\view.py',
   'PYMODULE'),
  ('ezdxf.entities.dimstyle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\dimstyle.py',
   'PYMODULE'),
  ('ezdxf.entities.textstyle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\textstyle.py',
   'PYMODULE'),
  ('ezdxf.entities.layer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\layer.py',
   'PYMODULE'),
  ('ezdxf.entities.ltype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\entities\\ltype.py',
   'PYMODULE'),
  ('ezdxf.tools.complex_ltype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\complex_ltype.py',
   'PYMODULE'),
  ('ezdxf.render.forms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\render\\forms.py',
   'PYMODULE'),
  ('ezdxf.math',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\__init__.py',
   'PYMODULE'),
  ('ezdxf.math.polyline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\polyline.py',
   'PYMODULE'),
  ('ezdxf.math.curvetools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\curvetools.py',
   'PYMODULE'),
  ('ezdxf.math.linalg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\linalg.py',
   'PYMODULE'),
  ('ezdxf.math.offset2d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\offset2d.py',
   'PYMODULE'),
  ('ezdxf.math.bbox',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bbox.py',
   'PYMODULE'),
  ('ezdxf.math.shape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\shape.py',
   'PYMODULE'),
  ('ezdxf.math.box',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\box.py',
   'PYMODULE'),
  ('ezdxf.math.circle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\circle.py',
   'PYMODULE'),
  ('ezdxf.math.line',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\line.py',
   'PYMODULE'),
  ('ezdxf.math.arc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\arc.py',
   'PYMODULE'),
  ('ezdxf.math.bulge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bulge.py',
   'PYMODULE'),
  ('ezdxf.math.ucs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\ucs.py',
   'PYMODULE'),
  ('ezdxf.math.eulerspiral',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\eulerspiral.py',
   'PYMODULE'),
  ('ezdxf.math.bezier_interpolation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bezier_interpolation.py',
   'PYMODULE'),
  ('ezdxf.math.bezier',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bezier.py',
   'PYMODULE'),
  ('ezdxf.math.bspline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\bspline.py',
   'PYMODULE'),
  ('ezdxf.math.parametrize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\parametrize.py',
   'PYMODULE'),
  ('ezdxf.math.construct3d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\construct3d.py',
   'PYMODULE'),
  ('ezdxf.math.construct2d',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\construct2d.py',
   'PYMODULE'),
  ('ezdxf.math._ctypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_ctypes.py',
   'PYMODULE'),
  ('ezdxf.math._construct',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_construct.py',
   'PYMODULE'),
  ('ezdxf.math._bspline',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_bspline.py',
   'PYMODULE'),
  ('ezdxf.math._bezier3p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_bezier3p.py',
   'PYMODULE'),
  ('ezdxf.math._bezier4p',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_bezier4p.py',
   'PYMODULE'),
  ('ezdxf.math._matrix44',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_matrix44.py',
   'PYMODULE'),
  ('ezdxf.math._vector',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\math\\_vector.py',
   'PYMODULE'),
  ('ezdxf.tools.pattern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\pattern.py',
   'PYMODULE'),
  ('ezdxf.tools._iso_pattern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\_iso_pattern.py',
   'PYMODULE'),
  ('ezdxf.tools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\__init__.py',
   'PYMODULE'),
  ('ezdxf.tools.standards',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\standards.py',
   'PYMODULE'),
  ('ezdxf.filemanagement',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\filemanagement.py',
   'PYMODULE'),
  ('ezdxf.tools.zipmanager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\tools\\zipmanager.py',
   'PYMODULE'),
  ('ezdxf.lldxf.validator',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\validator.py',
   'PYMODULE'),
  ('ezdxf.reorder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\reorder.py',
   'PYMODULE'),
  ('ezdxf.msgtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\msgtypes.py',
   'PYMODULE'),
  ('ezdxf.messenger',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\messenger.py',
   'PYMODULE'),
  ('ezdxf.bbox',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\bbox.py',
   'PYMODULE'),
  ('ezdxf.disassemble',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\disassemble.py',
   'PYMODULE'),
  ('ezdxf.path',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\__init__.py',
   'PYMODULE'),
  ('ezdxf.path.shapes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\shapes.py',
   'PYMODULE'),
  ('ezdxf.path.tools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\tools.py',
   'PYMODULE'),
  ('ezdxf.path.converter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\converter.py',
   'PYMODULE'),
  ('ezdxf.path.path',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\path.py',
   'PYMODULE'),
  ('ezdxf.path.commands',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\path\\commands.py',
   'PYMODULE'),
  ('ezdxf.xref',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\xref.py',
   'PYMODULE'),
  ('ezdxf.lldxf.const',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\const.py',
   'PYMODULE'),
  ('ezdxf.lldxf',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\lldxf\\__init__.py',
   'PYMODULE'),
  ('ezdxf.enums',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\enums.py',
   'PYMODULE'),
  ('ezdxf.colors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\colors.py',
   'PYMODULE'),
  ('ezdxf._options',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\_options.py',
   'PYMODULE'),
  ('ezdxf.version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\version.py',
   'PYMODULE'),
  ('datetime', 'D:\\Miniconda3\\envs\\Excel\\lib\\datetime.py', 'PYMODULE'),
  ('zipfile', 'D:\\Miniconda3\\envs\\Excel\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\Miniconda3\\envs\\Excel\\lib\\py_compile.py', 'PYMODULE'),
  ('configparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\configparser.py',
   'PYMODULE'),
  ('pandas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Miniconda3\\envs\\Excel\\lib\\tarfile.py', 'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('six',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pickletools',
   'D:\\Miniconda3\\envs\\Excel\\lib\\pickletools.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('xlrd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\__init__.py',
   'PYMODULE'),
  ('xlrd.xldate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE'),
  ('xlrd.info',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\info.py',
   'PYMODULE'),
  ('xlrd.formula',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE'),
  ('xlrd.book',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\book.py',
   'PYMODULE'),
  ('xlrd.sheet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\sheet.py',
   'PYMODULE'),
  ('xlrd.formatting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE'),
  ('xlrd.compdoc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE'),
  ('xlrd.biffh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\biffh.py',
   'PYMODULE'),
  ('xlrd.timemachine',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pytz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\Miniconda3\\envs\\Excel\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('shutil', 'D:\\Miniconda3\\envs\\Excel\\lib\\shutil.py', 'PYMODULE'),
  ('glob', 'D:\\Miniconda3\\envs\\Excel\\lib\\glob.py', 'PYMODULE')],
 [('python39.dll', 'D:\\Miniconda3\\envs\\Excel\\python39.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Miniconda3\\envs\\Excel\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Miniconda3\\envs\\Excel\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\Miniconda3\\envs\\Excel\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_webp.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_imagingtk.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_imagingcms.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_imagingmath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Miniconda3\\envs\\Excel\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_philox.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\random\\_common.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\Miniconda3\\envs\\Excel\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PIL\\_imaging.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('fontTools\\varLib\\iup.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\varLib\\iup.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('fontTools\\misc\\bezierTools.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\fontTools\\misc\\bezierTools.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Miniconda3\\envs\\Excel\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('PyQt5\\sip.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\sip.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\np_support.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\np_support.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\vector.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\vector.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\mapbox_earcut.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\mapbox_earcut.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\construct.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\construct.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\bspline.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\bspline.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\bezier3p.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\bezier3p.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\bezier4p.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\bezier4p.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('ezdxf\\acc\\matrix44.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\ezdxf\\acc\\matrix44.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\writers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Miniconda3\\envs\\Excel\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\tslib.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\testing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\sparse.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\sas.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\reshape.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\properties.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\parsers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\pandas_parser.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\ops.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\missing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\lib.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\json.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\join.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\interval.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\internals.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\indexing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\index.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\hashtable.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\hashing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\groupby.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\byteswap.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\arrays.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp39-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\_libs\\algos.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Miniconda3\\envs\\Excel\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Miniconda3\\envs\\Excel\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\Miniconda3\\envs\\Excel\\MSVCP140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Miniconda3\\envs\\Excel\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Miniconda3\\envs\\Excel\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\Miniconda3\\envs\\Excel\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libffi-7.dll', 'D:\\Miniconda3\\envs\\Excel\\DLLs\\libffi-7.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes39.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pywin32_system32\\pywintypes39.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Miniconda3\\envs\\Excel\\python3.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\Miniconda3\\envs\\Excel\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\Miniconda3\\envs\\Excel\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\Excel\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('numpy.libs\\.load-order-numpy-2.0.2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\numpy.libs\\.load-order-numpy-2.0.2',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\Miniconda3\\envs\\Excel\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\python工具箱\\build\\清单处理\\base_library.zip',
   'DATA')],
 [('_collections_abc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('abc', 'D:\\Miniconda3\\envs\\Excel\\lib\\abc.py', 'PYMODULE'),
  ('linecache', 'D:\\Miniconda3\\envs\\Excel\\lib\\linecache.py', 'PYMODULE'),
  ('locale', 'D:\\Miniconda3\\envs\\Excel\\lib\\locale.py', 'PYMODULE'),
  ('reprlib', 'D:\\Miniconda3\\envs\\Excel\\lib\\reprlib.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Miniconda3\\envs\\Excel\\lib\\sre_parse.py', 'PYMODULE'),
  ('codecs', 'D:\\Miniconda3\\envs\\Excel\\lib\\codecs.py', 'PYMODULE'),
  ('posixpath', 'D:\\Miniconda3\\envs\\Excel\\lib\\posixpath.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sre_compile.py',
   'PYMODULE'),
  ('stat', 'D:\\Miniconda3\\envs\\Excel\\lib\\stat.py', 'PYMODULE'),
  ('traceback', 'D:\\Miniconda3\\envs\\Excel\\lib\\traceback.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\Miniconda3\\envs\\Excel\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Miniconda3\\envs\\Excel\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('types', 'D:\\Miniconda3\\envs\\Excel\\lib\\types.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Miniconda3\\envs\\Excel\\lib\\sre_constants.py',
   'PYMODULE'),
  ('_bootlocale',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_bootlocale.py',
   'PYMODULE'),
  ('genericpath',
   'D:\\Miniconda3\\envs\\Excel\\lib\\genericpath.py',
   'PYMODULE'),
  ('heapq', 'D:\\Miniconda3\\envs\\Excel\\lib\\heapq.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Miniconda3\\envs\\Excel\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('ntpath', 'D:\\Miniconda3\\envs\\Excel\\lib\\ntpath.py', 'PYMODULE'),
  ('io', 'D:\\Miniconda3\\envs\\Excel\\lib\\io.py', 'PYMODULE'),
  ('weakref', 'D:\\Miniconda3\\envs\\Excel\\lib\\weakref.py', 'PYMODULE'),
  ('enum', 'D:\\Miniconda3\\envs\\Excel\\lib\\enum.py', 'PYMODULE'),
  ('keyword', 'D:\\Miniconda3\\envs\\Excel\\lib\\keyword.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\Miniconda3\\envs\\Excel\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('copyreg', 'D:\\Miniconda3\\envs\\Excel\\lib\\copyreg.py', 'PYMODULE'),
  ('operator', 'D:\\Miniconda3\\envs\\Excel\\lib\\operator.py', 'PYMODULE'),
  ('functools', 'D:\\Miniconda3\\envs\\Excel\\lib\\functools.py', 'PYMODULE'),
  ('warnings', 'D:\\Miniconda3\\envs\\Excel\\lib\\warnings.py', 'PYMODULE'),
  ('re', 'D:\\Miniconda3\\envs\\Excel\\lib\\re.py', 'PYMODULE'),
  ('os', 'D:\\Miniconda3\\envs\\Excel\\lib\\os.py', 'PYMODULE')])
