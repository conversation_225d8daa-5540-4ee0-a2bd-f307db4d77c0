[CrashReport]
path = C:/优效日历\CrashReport.exe

[InstallSystemTimeSyncService]
path = C:/优效日历\InstallSystemTimeSyncService.bat

[screensaver-install]
path = C:/优效日历\screensaver-install.bat

[SystemClockHookHost]
path = C:/优效日历\SystemClockHookHost.exe

[SystemClockHookHost_x64]
path = C:/优效日历\SystemClockHookHost_x64.exe

[SystemTimeSyncService]
path = C:/优效日历\SystemTimeSyncService.exe

[uninst]
path = C:/优效日历\uninst.exe

[UninstallSystemTimeSyncService]
path = C:/优效日历\UninstallSystemTimeSyncService.bat

[YXCalendar]
path = C:/优效日历\YXCalendar.exe

[YXUpdate]
path = C:/优效日历\YXUpdate.exe

[Memo]
text = 请输入备忘录内容

[Settings]
software_directory = C:/优效日历
startup_interval = 1

[StartupOrder]
order = YXUpdate,SystemClockHookHost

[Configurations]
names = 默认配置,配置1,配置2,配置3,配置4,配置5,配置6,配置7,配置8,配置9,配置10,配置11,配置12,配置13,配置14,配置15,配置16,配置17,配置18,配置19,配置20,配置21,配置22
current = 配置20

[默认配置]
startup_order = YXUpdate,SystemClockHookHost

[配置1]
startup_order = YXUpdate,SystemClockHookHost

[配置2]
startup_order = YXUpdate,SystemClockHookHost

[配置3]
startup_order = YXUpdate,SystemClockHookHost

[配置4]
startup_order = YXUpdate,SystemClockHookHost

[配置5]
startup_order = YXUpdate,SystemClockHookHost

[配置6]
startup_order = YXUpdate,SystemClockHookHost

[配置7]
startup_order = YXUpdate,SystemClockHookHost

[配置8]
startup_order = YXUpdate,SystemClockHookHost

[配置9]
startup_order = YXUpdate,SystemClockHookHost

[配置10]
startup_order = YXUpdate,SystemClockHookHost

[配置11]
startup_order = YXUpdate,SystemClockHookHost

[配置12]
startup_order = YXUpdate,SystemClockHookHost

[配置13]
startup_order = YXUpdate,SystemClockHookHost

[配置14]
startup_order = YXUpdate,SystemClockHookHost

[配置15]
startup_order = YXUpdate,SystemClockHookHost

[配置16]
startup_order = YXUpdate,SystemClockHookHost

[配置17]
startup_order = YXUpdate,SystemClockHookHost

[配置18]
startup_order = YXUpdate,SystemClockHookHost

[配置19]
startup_order = YXUpdate,SystemClockHookHost

[配置20]
startup_order = YXUpdate,SystemClockHookHost

[配置21]
startup_order = YXUpdate,SystemClockHookHost

[配置22]
startup_order = YXUpdate,SystemClockHookHost

[配置23]
startup_order = YXUpdate,SystemClockHookHost

[配置24]
startup_order = YXUpdate,SystemClockHookHost

[配置25]
startup_order = YXUpdate,SystemClockHookHost

[配置26]
startup_order = YXUpdate,SystemClockHookHost

[配置27]
startup_order = YXUpdate,SystemClockHookHost

[配置28]
startup_order = YXUpdate,SystemClockHookHost

[配置29]
startup_order = YXUpdate,SystemClockHookHost,SystemClockHookHost_x64

