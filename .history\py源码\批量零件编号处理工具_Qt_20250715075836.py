#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量零件编号处理工具 - Qt版
功能：
1. 批量输入需要更改的零件名称（如B45A、C38B等）
2. 自动去掉输入名称的最后一个字符作为匹配目标（B45A → B45，C38B → C38）
3. 在指定Excel表格中精确匹配并更新零件编号
4. 同步更新对应的DXF文件名称
5. 将处理后的文件复制到指定输出路径
"""

import sys
import os
import glob
import datetime
import configparser
import shutil
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QFormLayout, QGroupBox, QLabel, 
                             QLineEdit, QPushButton, QTextEdit, QProgressBar,
                             QFileDialog, QMessageBox, QCheckBox, QComboBox,
                             QSplitter, QTabWidget)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import pandas as pd
import openpyxl
from openpyxl import load_workbook


class ProcessingThread(QThread):
    """处理线程，避免界面卡顿"""
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(bool, str, dict)  # 完成信号(成功/失败, 消息, 统计信息)

    def __init__(self, part_names, input_path, output_path, sheet_name, column_name):
        super().__init__()
        self.part_names = part_names
        self.input_path = input_path
        self.output_path = output_path
        self.sheet_name = sheet_name
        self.column_name = column_name
        
        # 统计信息
        self.stats = {
            'total_parts': 0,
            'excel_matched': 0,
            'excel_updated': 0,
            'dxf_found': 0,
            'dxf_renamed': 0,
            'files_copied': 0,
            'errors': []
        }
        self.log_messages = []
        self.backup_data = {}  # 备份原始数据
        
    def run(self):
        try:
            self.progress.emit("开始处理批量零件编号...")
            
            # 第一步：解析零件名称
            self.progress.emit("正在解析零件名称...")
            processed_parts = self.parse_part_names()
            
            if not processed_parts:
                self.finished.emit(False, "没有有效的零件名称需要处理", self.stats)
                return
            
            self.stats['total_parts'] = len(processed_parts)
            self.progress.emit(f"解析完成，共 {len(processed_parts)} 个零件需要处理")
            
            # 第二步：处理Excel文件
            self.progress.emit("正在处理Excel文件...")
            excel_success = self.process_excel_files(processed_parts)
            
            if not excel_success:
                self.finished.emit(False, "Excel文件处理失败", self.stats)
                return
            
            # 第三步：处理DXF文件
            self.progress.emit("正在处理DXF文件...")
            self.process_dxf_files(processed_parts)
            
            # 第四步：复制文件到输出路径
            self.progress.emit("正在复制文件到输出路径...")
            self.copy_files_to_output()
            
            # 第五步：生成处理日志
            self.progress.emit("正在生成处理日志...")
            self.create_log_file()
            
            # 完成处理
            success_msg = f"处理完成！Excel匹配: {self.stats['excel_matched']}, DXF处理: {self.stats['dxf_renamed']}, 文件复制: {self.stats['files_copied']}"
            self.finished.emit(True, success_msg, self.stats)
            
        except Exception as e:
            self.stats['errors'].append(f"处理过程中出现错误: {str(e)}")
            self.finished.emit(False, f"处理过程中出现错误: {str(e)}", self.stats)
    
    def parse_part_names(self):
        """解析零件名称，去掉最后一个字符作为匹配目标"""
        processed_parts = []
        
        for part_name in self.part_names:
            part_name = part_name.strip()
            if not part_name:
                continue
                
            if len(part_name) <= 1:
                self.log_messages.append(f"警告: 零件名称太短，跳过: {part_name}")
                self.stats['errors'].append(f"零件名称太短: {part_name}")
                continue
            
            # 去掉最后一个字符作为匹配目标
            match_target = part_name[:-1]
            processed_parts.append({
                'original_name': part_name,
                'match_target': match_target
            })
            
            self.log_messages.append(f"零件处理: {part_name} -> 匹配目标: {match_target}")
        
        return processed_parts
    
    def process_excel_files(self, processed_parts):
        """处理输入路径下的所有Excel文件，查找并修改匹配的条目"""
        try:
            # 查找输入路径下的所有Excel文件
            excel_patterns = ['*.xlsx', '*.xls', '*.xlsm']
            excel_files = []

            for pattern in excel_patterns:
                found_files = glob.glob(os.path.join(self.input_path, pattern))
                # 过滤掉备份文件和临时文件
                filtered_files = [f for f in found_files if not f.endswith('.backup') and
                                not os.path.basename(f).startswith('~') and
                                not os.path.basename(f).startswith('写入测试') and
                                not os.path.basename(f).startswith('openpyxl')]
                excel_files.extend(filtered_files)

            if not excel_files:
                self.stats['errors'].append(f"输入路径中没有找到有效的Excel文件: {self.input_path}")
                return False

            self.progress.emit(f"找到 {len(excel_files)} 个Excel文件")
            self.log_messages.append(f"找到Excel文件: {[os.path.basename(f) for f in excel_files]}")

            # 处理每个Excel文件
            processed_count = 0
            for excel_file in excel_files:
                self.progress.emit(f"处理Excel文件: {os.path.basename(excel_file)}")
                success = self.process_single_excel_file(excel_file, processed_parts)
                if success:
                    processed_count += 1
                else:
                    self.log_messages.append(f"警告: Excel文件处理失败: {os.path.basename(excel_file)}")

            self.log_messages.append(f"成功处理 {processed_count}/{len(excel_files)} 个Excel文件")
            return processed_count > 0

        except Exception as e:
            self.stats['errors'].append(f"Excel文件批量处理错误: {str(e)}")
            return False

    def process_single_excel_file(self, excel_file, processed_parts):
        """处理单个Excel文件"""
        try:
            # 读取Excel文件
            try:
                excel_file_obj = pd.ExcelFile(excel_file)
                available_sheets = excel_file_obj.sheet_names

                # 检查工作表是否存在
                if self.sheet_name not in available_sheets:
                    self.log_messages.append(f"跳过文件 {os.path.basename(excel_file)}: 工作表 '{self.sheet_name}' 不存在")
                    self.log_messages.append(f"  可用工作表: {', '.join(available_sheets)}")
                    return False

                # 读取指定工作表
                df = pd.read_excel(excel_file, sheet_name=self.sheet_name)

            except Exception as e:
                self.log_messages.append(f"跳过文件 {os.path.basename(excel_file)}: 读取失败 - {str(e)}")
                return False

            # 检查目标列是否存在
            if self.column_name not in df.columns:
                self.log_messages.append(f"跳过文件 {os.path.basename(excel_file)}: 未找到列 '{self.column_name}'")
                self.log_messages.append(f"  可用列名: {', '.join(df.columns)}")
                return False

            # 备份原始Excel文件
            backup_excel = excel_file + '.backup'
            shutil.copy2(excel_file, backup_excel)
            self.log_messages.append(f"Excel文件已备份: {os.path.basename(backup_excel)}")

            file_has_changes = False

            # 遍历零件进行匹配和修改
            for part_info in processed_parts:
                original_name = part_info['original_name']
                match_target = part_info['match_target']

                # 使用新的灵活匹配逻辑
                matched_indices = self.find_flexible_matches(df, match_target)

                if matched_indices:
                    self.stats['excel_matched'] += 1
                    file_has_changes = True

                    # 修改匹配的条目
                    for idx in matched_indices:
                        old_value = df.loc[idx, self.column_name]

                        # 根据匹配类型生成新的Excel值
                        new_value = self.generate_new_excel_value(str(old_value), match_target, original_name)

                        # 备份原始值
                        backup_key = f"{os.path.basename(excel_file)}:{match_target}"
                        if backup_key not in self.backup_data:
                            self.backup_data[backup_key] = []
                        self.backup_data[backup_key].append({
                            'file': os.path.basename(excel_file),
                            'row': idx + 2,  # Excel行号从1开始，加上表头
                            'old_value': old_value,
                            'new_value': new_value,
                            'match_type': self.get_excel_match_type(str(old_value), match_target)
                        })

                        df.loc[idx, self.column_name] = new_value
                        self.stats['excel_updated'] += 1

                        # 记录匹配类型
                        match_type = self.get_excel_match_type(str(old_value), match_target)
                        self.log_messages.append(f"✓ {os.path.basename(excel_file)} 第{idx+2}行 ({match_type}): '{old_value}' -> '{new_value}'")

            # 只有在有更改时才保存文件
            if file_has_changes:
                with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name=self.sheet_name, index=False)
                self.progress.emit(f"✓ 保存Excel文件: {os.path.basename(excel_file)}")
            else:
                self.log_messages.append(f"文件无更改，跳过保存: {os.path.basename(excel_file)}")

            return True

        except Exception as e:
            self.stats['errors'].append(f"处理Excel文件 {os.path.basename(excel_file)} 错误: {str(e)}")
            return False

    def find_flexible_matches(self, df, match_target):
        """
        使用灵活匹配规则查找匹配项
        匹配规则：
        1. 精确匹配：完全相等
        2. 后缀匹配：目标编号作为完整后缀出现，前面可以有连字符分隔符
        3. 避免部分匹配：后面不能有额外字符

        例如：查找"LP3"时
        - 匹配："LP3", "2-LP3", "A-LP3"
        - 不匹配："LP30", "LP3A", "ALP3"
        """
        matched_indices = []

        for idx, cell_value in df[self.column_name].items():
            cell_str = str(cell_value).strip()

            if not cell_str or cell_str == 'nan':
                continue

            # 规则1: 精确匹配
            if cell_str == match_target:
                matched_indices.append(idx)
                continue

            # 规则2: 后缀匹配（带连字符前缀）
            if self.is_valid_suffix_match(cell_str, match_target):
                matched_indices.append(idx)

        return matched_indices

    def is_valid_suffix_match(self, cell_value, target):
        """
        检查是否为有效的后缀匹配
        - cell_value: Excel单元格的值
        - target: 要匹配的目标字符串
        """
        # 必须以目标字符串结尾
        if not cell_value.endswith(target):
            return False

        # 如果完全相等，已经在精确匹配中处理了
        if cell_value == target:
            return False

        # 获取前缀部分
        prefix = cell_value[:-len(target)]

        # 前缀必须以连字符结尾
        if not prefix.endswith('-'):
            return False

        # 检查前缀部分（去掉连字符）是否合理
        prefix_without_dash = prefix[:-1]

        # 前缀不能为空
        if not prefix_without_dash:
            return False

        # 前缀应该是字母数字组合，不包含特殊字符（除了可能的连字符）
        if not prefix_without_dash.replace('-', '').isalnum():
            return False

        return True
    
    def process_dxf_files(self, processed_parts):
        """处理DXF文件，使用灵活匹配逻辑查找并重命名"""
        try:
            # 在输入路径中查找DXF文件
            dxf_pattern = os.path.join(self.input_path, "*.dxf")
            all_dxf_files = glob.glob(dxf_pattern)

            if not all_dxf_files:
                self.log_messages.append("警告: 输入路径中没有找到DXF文件")
                return

            self.progress.emit(f"找到 {len(all_dxf_files)} 个DXF文件")

            # 记录重命名的DXF文件，用于后续复制
            self.renamed_dxf_files = []

            # 遍历零件进行DXF文件处理
            for part_info in processed_parts:
                original_name = part_info['original_name']
                match_target = part_info['match_target']

                self.progress.emit(f"DXF处理: {match_target}")

                # 使用灵活匹配逻辑查找DXF文件
                matched_dxf_files = self.find_matching_dxf_files(match_target, all_dxf_files)

                if matched_dxf_files:
                    self.stats['dxf_found'] += len(matched_dxf_files)

                    # 处理所有匹配的DXF文件
                    for matched_dxf_path in matched_dxf_files:
                        old_dxf_name = os.path.basename(matched_dxf_path)
                        old_filename_without_ext = old_dxf_name[:-4]  # 去掉.dxf后缀

                        # 根据匹配类型生成新的文件名
                        new_filename = self.generate_new_dxf_filename(old_filename_without_ext, match_target, original_name)
                        new_dxf_path = os.path.join(self.input_path, f"{new_filename}.dxf")

                        try:
                            # 备份原始文件名信息
                            backup_key = f"dxf:{match_target}"
                            if backup_key not in self.backup_data:
                                self.backup_data[backup_key] = []
                            self.backup_data[backup_key].append({
                                'type': 'dxf',
                                'old_path': matched_dxf_path,
                                'new_path': new_dxf_path,
                                'old_name': old_dxf_name,
                                'new_name': f"{new_filename}.dxf",
                                'match_type': self.get_match_type(old_filename_without_ext, match_target)
                            })

                            os.rename(matched_dxf_path, new_dxf_path)
                            self.stats['dxf_renamed'] += 1
                            self.renamed_dxf_files.append(new_dxf_path)  # 记录重命名后的文件路径

                            # 记录匹配类型
                            match_type = self.get_match_type(old_filename_without_ext, match_target)
                            self.log_messages.append(f"✓ DXF重命名 ({match_type}): {old_dxf_name} -> {new_filename}.dxf")

                        except Exception as e:
                            self.stats['errors'].append(f"DXF重命名失败 {old_dxf_name}: {str(e)}")
                            self.log_messages.append(f"✗ DXF重命名失败: {old_dxf_name} ({str(e)})")
                else:
                    self.log_messages.append(f"✗ DXF文件未找到匹配项: {match_target}")

        except Exception as e:
            self.stats['errors'].append(f"DXF处理错误: {str(e)}")

    def find_matching_dxf_files(self, match_target, all_dxf_files):
        """
        使用灵活匹配规则查找匹配的DXF文件
        匹配规则与Excel相同：
        1. 精确匹配：完全相等
        2. 后缀匹配：目标编号作为完整后缀出现，前面可以有连字符分隔符
        3. 避免部分匹配：后面不能有额外字符
        """
        matched_files = []

        for dxf_file_path in all_dxf_files:
            # 获取文件名（不包含路径和.dxf扩展名）
            dxf_filename = os.path.splitext(os.path.basename(dxf_file_path))[0]

            # 规则1: 精确匹配
            if dxf_filename == match_target:
                matched_files.append(dxf_file_path)
                continue

            # 规则2: 后缀匹配（带连字符前缀）
            if self.is_valid_suffix_match(dxf_filename, match_target):
                matched_files.append(dxf_file_path)

        return matched_files

    def get_match_type(self, dxf_filename, match_target):
        """获取匹配类型描述"""
        if dxf_filename == match_target:
            return "精确匹配"
        elif self.is_valid_suffix_match(dxf_filename, match_target):
            prefix = dxf_filename[:-len(match_target)-1]  # 去掉连字符和目标字符串
            return f"前缀匹配: {prefix}-{match_target}"
        else:
            return "未知匹配"

    def get_excel_match_type(self, excel_value, match_target):
        """获取Excel匹配类型描述"""
        if excel_value == match_target:
            return "精确匹配"
        elif self.is_valid_suffix_match(excel_value, match_target):
            prefix = excel_value[:-len(match_target)-1]  # 去掉连字符和目标字符串
            return f"前缀匹配: {prefix}-{match_target}"
        else:
            return "未知匹配"

    def generate_new_excel_value(self, old_excel_value, match_target, original_name):
        """
        根据匹配类型生成新的Excel值
        保持原始前缀部分不变，只在末尾添加新的后缀字符

        参数:
        - old_excel_value: 原始Excel单元格值（如"2-LP22"）
        - match_target: 匹配目标（如"LP22"）
        - original_name: 完整的新名称（如"LP22A"）

        返回:
        - 新的Excel值（如"2-LP22A"）
        """
        # 情况1：精确匹配（如LP22匹配LP22）
        if old_excel_value == match_target:
            return original_name

        # 情况2：前缀匹配（如2-LP22匹配LP22）
        elif self.is_valid_suffix_match(old_excel_value, match_target):
            # 获取前缀部分（包含连字符）
            prefix_with_dash = old_excel_value[:-len(match_target)]  # 例如："2-LP22" -> "2-"

            # 生成新Excel值：前缀 + 完整的新名称
            new_excel_value = prefix_with_dash + original_name
            return new_excel_value

        # 情况3：未知匹配类型（理论上不应该发生）
        else:
            # 默认返回原始名称，记录警告
            self.log_messages.append(f"警告: Excel值未知匹配类型，使用默认更新策略: {old_excel_value}")
            return original_name

    def generate_new_dxf_filename(self, old_filename, match_target, original_name):
        """
        根据匹配类型生成新的DXF文件名
        保持原始前缀部分不变，只在末尾添加新的后缀字符

        参数:
        - old_filename: 原始文件名（不含.dxf扩展名）
        - match_target: 匹配目标（如LP20）
        - original_name: 完整的新名称（如LP20A）

        返回:
        - 新的文件名（不含.dxf扩展名）
        """
        # 情况1：精确匹配（如LP20.dxf匹配LP20）
        if old_filename == match_target:
            return original_name

        # 情况2：前缀匹配（如2-LP20.dxf匹配LP20）
        elif self.is_valid_suffix_match(old_filename, match_target):
            # 获取前缀部分（包含连字符）
            prefix_with_dash = old_filename[:-len(match_target)]  # 例如："2-LP20" -> "2-"

            # 生成新文件名：前缀 + 完整的新名称
            new_filename = prefix_with_dash + original_name
            return new_filename

        # 情况3：未知匹配类型（理论上不应该发生）
        else:
            # 默认返回原始名称，记录警告
            self.log_messages.append(f"警告: 未知匹配类型，使用默认重命名策略: {old_filename}")
            return original_name
    
    def copy_files_to_output(self):
        """将处理后的文件另存到输出路径，保持原有文件不变"""
        try:
            if not os.path.exists(self.output_path):
                os.makedirs(self.output_path)
                self.log_messages.append(f"创建输出目录: {self.output_path}")

            # 统计实际被处理的文件数量
            processed_excel_count = 0
            processed_dxf_count = 0

            # 复制实际被处理过的Excel文件
            excel_patterns = ['*.xlsx', '*.xls', '*.xlsm']
            excel_files = []

            for pattern in excel_patterns:
                excel_files.extend(glob.glob(os.path.join(self.input_path, pattern)))

            for excel_file in excel_files:
                # 检查是否有对应的备份文件，如果有说明文件被处理过
                backup_file = excel_file + '.backup'
                if os.path.exists(backup_file):
                    # 检查文件是否真的有内容变更
                    if self.has_excel_changes(excel_file):
                        excel_filename = os.path.basename(excel_file)
                        output_excel = os.path.join(self.output_path, excel_filename)
                        shutil.copy2(excel_file, output_excel)
                        self.stats['files_copied'] += 1
                        processed_excel_count += 1
                        self.log_messages.append(f"✓ 另存处理过的Excel文件: {excel_filename}")
                    else:
                        self.log_messages.append(f"跳过无变更的Excel文件: {os.path.basename(excel_file)}")
                else:
                    self.log_messages.append(f"跳过未处理的Excel文件: {os.path.basename(excel_file)}")

            # 只另存重命名过的DXF文件
            if hasattr(self, 'renamed_dxf_files') and self.renamed_dxf_files:
                for dxf_file in self.renamed_dxf_files:
                    if os.path.exists(dxf_file):
                        dxf_filename = os.path.basename(dxf_file)
                        output_dxf = os.path.join(self.output_path, dxf_filename)
                        shutil.copy2(dxf_file, output_dxf)
                        self.stats['files_copied'] += 1
                        processed_dxf_count += 1
                        self.log_messages.append(f"✓ 另存重命名的DXF文件: {dxf_filename}")
                    else:
                        self.log_messages.append(f"警告: 重命名的DXF文件不存在: {os.path.basename(dxf_file)}")
            else:
                self.log_messages.append("没有DXF文件被重命名，跳过DXF文件复制")

            # 统计未处理的文件
            all_dxf_files = glob.glob(os.path.join(self.input_path, "*.dxf"))
            renamed_dxf_names = [os.path.basename(f) for f in getattr(self, 'renamed_dxf_files', [])]

            unprocessed_dxf_count = 0
            for dxf_file in all_dxf_files:
                dxf_filename = os.path.basename(dxf_file)
                if dxf_filename not in renamed_dxf_names:
                    unprocessed_dxf_count += 1
                    self.log_messages.append(f"保持原状的DXF文件: {dxf_filename}")

            unprocessed_excel_count = len(excel_files) - processed_excel_count

            # 记录处理统计
            self.log_messages.append(f"文件处理统计:")
            self.log_messages.append(f"  Excel文件: {processed_excel_count} 个被处理并另存, {unprocessed_excel_count} 个保持原状")
            self.log_messages.append(f"  DXF文件: {processed_dxf_count} 个被重命名并另存, {unprocessed_dxf_count} 个保持原状")

        except Exception as e:
            self.stats['errors'].append(f"文件复制错误: {str(e)}")

    def has_excel_changes(self, excel_file):
        """检查Excel文件是否真的有内容变更"""
        try:
            backup_file = excel_file + '.backup'
            if not os.path.exists(backup_file):
                return False

            # 检查是否有更新统计记录
            if self.stats['excel_updated'] > 0:
                # 如果有更新记录，进一步检查是否是这个文件
                excel_basename = os.path.basename(excel_file)

                # 检查备份数据中是否有这个文件的记录
                for backup_key, backups in self.backup_data.items():
                    if excel_basename in backup_key:
                        if backups:  # 如果有备份记录，说明文件被修改了
                            return True

            # 如果没有备份记录，使用文件属性检查
            # 简单检查：如果备份文件存在且大小不同，认为有变更
            original_size = os.path.getsize(backup_file)
            current_size = os.path.getsize(excel_file)

            # 如果文件大小有变化，认为有内容变更
            if original_size != current_size:
                return True

            # 更精确的检查：比较修改时间
            original_mtime = os.path.getmtime(backup_file)
            current_mtime = os.path.getmtime(excel_file)

            return current_mtime > original_mtime

        except Exception as e:
            self.log_messages.append(f"检查Excel变更失败 {os.path.basename(excel_file)}: {str(e)}")
            return True  # 出错时默认认为有变更
    
    def create_log_file(self):
        """创建处理日志文件"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            log_filename = f"批量零件编号处理日志_{timestamp}.txt"
            log_path = os.path.join(self.output_path, log_filename)
            
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write("批量零件编号处理日志\n")
                f.write("=" * 50 + "\n")
                f.write(f"处理时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"输入路径: {self.input_path}\n")
                f.write(f"输出路径: {self.output_path}\n")
                f.write(f"工作表: {self.sheet_name}\n")
                f.write(f"目标列: {self.column_name}\n")
                f.write("-" * 50 + "\n\n")
                
                f.write("处理统计:\n")
                f.write(f"总零件数: {self.stats['total_parts']}\n")
                f.write(f"Excel匹配数: {self.stats['excel_matched']}\n")
                f.write(f"Excel更新数: {self.stats['excel_updated']}\n")
                f.write(f"DXF找到数: {self.stats['dxf_found']}\n")
                f.write(f"DXF重命名数: {self.stats['dxf_renamed']}\n")
                f.write(f"文件复制数: {self.stats['files_copied']}\n")
                f.write(f"错误数: {len(self.stats['errors'])}\n")
                f.write("-" * 50 + "\n\n")
                
                if self.backup_data:
                    f.write("备份数据记录:\n")
                    for match_target, backups in self.backup_data.items():
                        f.write(f"匹配目标: {match_target}\n")
                        for backup in backups:
                            if backup.get('type') == 'dxf':
                                f.write(f"  DXF: {backup['old_path']} -> {backup['new_path']}\n")
                            else:
                                f.write(f"  Excel第{backup['row']}行: {backup['old_value']} -> {backup['new_value']}\n")
                        f.write("\n")
                    f.write("-" * 50 + "\n\n")
                
                f.write("详细处理记录:\n")
                for message in self.log_messages:
                    f.write(f"{message}\n")
                
                if self.stats['errors']:
                    f.write("\n错误记录:\n")
                    for error in self.stats['errors']:
                        f.write(f"ERROR: {error}\n")
            
            self.progress.emit(f"✓ 处理日志已保存: {log_filename}")
            
        except Exception as e:
            self.stats['errors'].append(f"保存日志失败: {str(e)}")


class BatchPartProcessorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.processing_thread = None
        self.config = configparser.ConfigParser()
        self.config_file = "batch_part_processor_config.ini"
        self.load_config()

    def init_ui(self):
        self.setWindowTitle("批量零件编号处理工具 - Qt版")
        self.setGeometry(400, 200, 900, 800)
        self.setMinimumSize(900, 700)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 说明信息
        info_group = QGroupBox("功能说明")
        info_layout = QVBoxLayout()
        info_text = QLabel("""
        <b>批量零件编号处理工具：</b><br>
        1. 批量输入需要更改的零件名称（如B45A、C38B等），每行一个<br>
        2. 自动去掉输入名称的最后一个字符作为匹配目标（B45A → B45，C38B → C38）<br>
        3. 在指定Excel表格中精确匹配并更新零件编号<br>
        4. 同步更新对应的DXF文件名称（B45.dxf → B45A.dxf）<br>
        5. 将处理后的文件复制到指定输出路径<br>
        <b>严格匹配规则：</b> 确保B45只匹配B45，不会误匹配B450、B451等<br>
        <b>备份保护：</b> 自动备份原始文件，记录所有更改前的原始数值
        """)
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)

        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)

        # 输入设置选项卡
        input_tab = QWidget()
        tab_widget.addTab(input_tab, "输入设置")
        self.setup_input_tab(input_tab)

        # 路径设置选项卡
        path_tab = QWidget()
        tab_widget.addTab(path_tab, "路径设置")
        self.setup_path_tab(path_tab)

        # 处理按钮
        button_layout = QHBoxLayout()
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 10px; font-weight: bold; font-size: 14px; }")
        button_layout.addWidget(self.process_btn)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # 日志显示区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(250)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 状态栏
        try:
            self.statusBar().showMessage("就绪")
        except:
            pass  # 忽略状态栏错误

    def setup_input_tab(self, tab):
        """设置输入选项卡"""
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 零件名称输入区域
        parts_group = QGroupBox("零件名称输入")
        parts_layout = QVBoxLayout()

        parts_label = QLabel("请输入需要处理的零件名称（每行一个）：")
        parts_layout.addWidget(parts_label)

        self.parts_input = QTextEdit()
        self.parts_input.setPlaceholderText("例如：\nB45A\nC38B\nD12C\n\n程序会自动去掉最后一个字符进行匹配\nB45A → 匹配 B45\nC38B → 匹配 C38")
        self.parts_input.setMaximumHeight(200)
        parts_layout.addWidget(self.parts_input)

        parts_group.setLayout(parts_layout)
        layout.addWidget(parts_group)

        # Excel设置区域
        excel_group = QGroupBox("Excel设置")
        excel_layout = QFormLayout()

        # 工作表名称
        self.sheet_name_edit = QLineEdit()
        self.sheet_name_edit.setText("Sheet1")
        excel_layout.addRow("工作表名称:", self.sheet_name_edit)

        # 目标列名
        self.column_name_edit = QLineEdit()
        self.column_name_edit.setText("零件编号")
        excel_layout.addRow("目标列名:", self.column_name_edit)

        excel_group.setLayout(excel_layout)
        layout.addWidget(excel_group)

        layout.addStretch()

    def setup_path_tab(self, tab):
        """设置路径选项卡"""
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 文件路径设置区域
        path_group = QGroupBox("文件路径设置")
        path_layout = QFormLayout()

        # 输入路径（包含DXF文件和Excel文件的路径）
        input_row = QHBoxLayout()
        self.input_path_edit = QLineEdit()
        self.input_browse_btn = QPushButton("浏览")
        self.input_browse_btn.clicked.connect(self.browse_input_path)
        input_row.addWidget(self.input_path_edit)
        input_row.addWidget(self.input_browse_btn)
        path_layout.addRow("输入路径（包含DXF和Excel文件）:", input_row)

        # 输出路径
        output_row = QHBoxLayout()
        self.output_path_edit = QLineEdit()
        self.output_browse_btn = QPushButton("浏览")
        self.output_browse_btn.clicked.connect(self.browse_output_path)
        output_row.addWidget(self.output_path_edit)
        output_row.addWidget(self.output_browse_btn)
        path_layout.addRow("输出路径:", output_row)

        path_group.setLayout(path_layout)
        layout.addWidget(path_group)

        # 添加说明信息
        info_group = QGroupBox("路径说明")
        info_layout = QVBoxLayout()
        info_text = QLabel("""
        <b>输入路径要求：</b><br>
        • 包含需要处理的DXF文件（*.dxf）<br>
        • 包含需要处理的Excel文件（*.xlsx, *.xls, *.xlsm）<br>
        • 程序会自动扫描并处理路径下的所有相关文件<br><br>
        <b>输出路径：</b><br>
        • 存放处理后的文件<br>
        • 只复制重命名过的DXF文件和处理过的Excel文件
        """)
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        layout.addStretch()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')

                # 加载路径设置
                if 'paths' in self.config:
                    self.input_path_edit.setText(self.config.get('paths', 'input_path', fallback=''))
                    self.output_path_edit.setText(self.config.get('paths', 'output_path', fallback=''))

                # 加载Excel设置
                if 'excel' in self.config:
                    self.sheet_name_edit.setText(self.config.get('excel', 'sheet_name', fallback='Sheet1'))
                    self.column_name_edit.setText(self.config.get('excel', 'column_name', fallback='零件编号'))

                # 加载零件名称
                if 'parts' in self.config:
                    parts_text = self.config.get('parts', 'part_names', fallback='')
                    self.parts_input.setPlainText(parts_text)

        except Exception as e:
            self.log_text.append(f"加载配置文件失败: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            if 'paths' not in self.config:
                self.config.add_section('paths')
            if 'excel' not in self.config:
                self.config.add_section('excel')
            if 'parts' not in self.config:
                self.config.add_section('parts')

            # 保存路径设置
            self.config.set('paths', 'input_path', self.input_path_edit.text())
            self.config.set('paths', 'output_path', self.output_path_edit.text())

            # 保存Excel设置
            self.config.set('excel', 'sheet_name', self.sheet_name_edit.text())
            self.config.set('excel', 'column_name', self.column_name_edit.text())

            # 保存零件名称
            self.config.set('parts', 'part_names', self.parts_input.toPlainText())

            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)

        except Exception as e:
            self.log_text.append(f"保存配置文件失败: {str(e)}")

    def browse_input_path(self):
        """浏览输入路径"""
        folder = QFileDialog.getExistingDirectory(self, "选择输入路径（包含DXF和Excel文件）")
        if folder:
            self.input_path_edit.setText(folder)

            # 检查文件夹中的文件
            dxf_files = glob.glob(os.path.join(folder, "*.dxf"))
            excel_patterns = ['*.xlsx', '*.xls', '*.xlsm']
            excel_files = []
            for pattern in excel_patterns:
                excel_files.extend(glob.glob(os.path.join(folder, pattern)))

            status_msg = f"✓ 输入路径设置成功"
            if dxf_files:
                status_msg += f"，找到 {len(dxf_files)} 个DXF文件"
            if excel_files:
                status_msg += f"，找到 {len(excel_files)} 个Excel文件"

            self.log_text.append(status_msg)

            if not dxf_files and not excel_files:
                QMessageBox.warning(self, "警告", "选择的文件夹中没有找到DXF文件或Excel文件")

    def browse_output_path(self):
        """浏览输出路径"""
        folder = QFileDialog.getExistingDirectory(self, "选择输出路径")
        if folder:
            self.output_path_edit.setText(folder)
            self.log_text.append(f"✓ 输出路径设置成功: {folder}")

    def validate_inputs(self):
        """验证输入参数"""
        # 获取零件名称
        parts_text = self.parts_input.toPlainText().strip()
        if not parts_text:
            QMessageBox.critical(self, "错误", "请输入需要处理的零件名称")
            return False

        part_names = [line.strip() for line in parts_text.split('\n') if line.strip()]
        if not part_names:
            QMessageBox.critical(self, "错误", "没有有效的零件名称")
            return False

        # 验证路径
        input_path = self.input_path_edit.text().strip()
        output_path = self.output_path_edit.text().strip()

        if not input_path:
            QMessageBox.critical(self, "错误", "请选择输入路径")
            return False

        if not os.path.exists(input_path):
            QMessageBox.critical(self, "错误", "输入路径不存在")
            return False

        if not output_path:
            QMessageBox.critical(self, "错误", "请选择输出路径")
            return False

        # 检查输入路径中是否有相关文件
        dxf_files = glob.glob(os.path.join(input_path, "*.dxf"))
        excel_patterns = ['*.xlsx', '*.xls', '*.xlsm']
        excel_files = []
        for pattern in excel_patterns:
            excel_files.extend(glob.glob(os.path.join(input_path, pattern)))

        if not dxf_files and not excel_files:
            QMessageBox.critical(self, "错误", "输入路径中没有找到DXF文件或Excel文件")
            return False

        # 验证Excel设置
        sheet_name = self.sheet_name_edit.text().strip()
        column_name = self.column_name_edit.text().strip()

        if not sheet_name:
            QMessageBox.critical(self, "错误", "请输入工作表名称")
            return False

        if not column_name:
            QMessageBox.critical(self, "错误", "请输入目标列名")
            return False

        return True

    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return

        # 获取参数
        parts_text = self.parts_input.toPlainText().strip()
        part_names = [line.strip() for line in parts_text.split('\n') if line.strip()]
        input_path = self.input_path_edit.text().strip()
        output_path = self.output_path_edit.text().strip()
        sheet_name = self.sheet_name_edit.text().strip()
        column_name = self.column_name_edit.text().strip()

        # 统计输入路径中的文件
        dxf_files = glob.glob(os.path.join(input_path, "*.dxf"))
        excel_patterns = ['*.xlsx', '*.xls', '*.xlsm']
        excel_files = []
        for pattern in excel_patterns:
            excel_files.extend(glob.glob(os.path.join(input_path, pattern)))

        # 显示确认对话框
        reply = QMessageBox.question(
            self, "确认处理",
            f"即将处理以下内容：\n\n"
            f"零件数量: {len(part_names)}\n"
            f"输入路径: {input_path}\n"
            f"DXF文件数: {len(dxf_files)}\n"
            f"Excel文件数: {len(excel_files)}\n"
            f"工作表: {sheet_name}\n"
            f"目标列: {column_name}\n"
            f"输出路径: {output_path}\n\n"
            f"确认开始处理吗？\n"
            f"注意：程序会自动备份原始文件",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 保存配置
        self.save_config()

        # 清空日志
        self.log_text.clear()

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 禁用按钮
        self.process_btn.setEnabled(False)
        self.process_btn.setText("处理中...")

        # 启动处理线程
        self.processing_thread = ProcessingThread(
            part_names, input_path, output_path, sheet_name, column_name
        )
        self.processing_thread.progress.connect(self.on_progress)
        self.processing_thread.finished.connect(self.on_finished)
        self.processing_thread.start()

        try:
            self.statusBar().showMessage("正在处理...")
        except:
            pass  # 忽略状态栏错误

    def on_progress(self, message):
        """更新进度信息"""
        self.log_text.append(message)
        try:
            self.statusBar().showMessage(message)
        except:
            pass  # 忽略状态栏错误
        # 自动滚动到底部
        try:
            self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        except:
            pass  # 忽略滚动条错误

    def on_finished(self, success, message, stats):
        """处理完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮
        self.process_btn.setEnabled(True)
        self.process_btn.setText("开始处理")

        # 显示结果
        if success:
            self.log_text.append(f"✓ {message}")
            try:
                self.statusBar().showMessage("处理完成", 3000)
            except:
                pass  # 忽略状态栏错误

            # 显示详细统计信息
            stats_msg = (
                f"处理完成！\n\n"
                f"统计信息：\n"
                f"总零件数: {stats['total_parts']}\n"
                f"Excel匹配数: {stats['excel_matched']}\n"
                f"Excel更新数: {stats['excel_updated']}\n"
                f"DXF找到数: {stats['dxf_found']}\n"
                f"DXF重命名数: {stats['dxf_renamed']}\n"
                f"文件复制数: {stats['files_copied']}\n"
                f"错误数: {len(stats['errors'])}"
            )

            if stats['errors']:
                stats_msg += f"\n\n错误详情:\n" + "\n".join(stats['errors'][:5])
                if len(stats['errors']) > 5:
                    stats_msg += f"\n... 还有 {len(stats['errors']) - 5} 个错误，详见日志文件"

            QMessageBox.information(self, "处理完成", stats_msg)
        else:
            self.log_text.append(f"✗ {message}")
            try:
                self.statusBar().showMessage("处理失败", 3000)
            except:
                pass  # 忽略状态栏错误
            QMessageBox.critical(self, "处理失败", message)

    def closeEvent(self, event):
        """关闭事件"""
        # 保存配置
        self.save_config()

        # 如果有处理线程在运行，询问是否确认关闭
        if self.processing_thread and self.processing_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认关闭",
                "处理正在进行中，确认关闭程序吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.processing_thread.terminate()
                self.processing_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    app = QApplication(sys.argv)

    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = BatchPartProcessorApp()
    window.show()

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
