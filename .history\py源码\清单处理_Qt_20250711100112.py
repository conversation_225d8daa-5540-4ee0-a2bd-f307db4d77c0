import sys
import os
import glob
import shutil
import pandas as pd
import configparser
import zipfile
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QTextEdit, QGroupBox, QMessageBox, QFileDialog,
                             QFormLayout, QProgressBar, QCheckBox, QFrame,
                             QScrollArea, QSplitter, QDialog, QDialogButtonBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QTextCursor


class ReminderDialog(QDialog):
    """醒目的提醒对话框"""
    def __init__(self, parent=None, show_pattern_reminder=False):
        super().__init__(parent)
        self.setWindowTitle("⚠️ 重要提醒 ⚠️")
        self.setFixedSize(500, 350)  # 进一步增大对话框尺寸
        self.setModal(True)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #ffeb3b;
                border: 3px solid #ff5722;
            }
            QLabel {
                color: #d32f2f;
                font-weight: bold;
                font-size: 18px;
            }
            QPushButton {
                background-color: #4caf50;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 12px;
                border: none;
                border-radius: 4px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        layout = QVBoxLayout()

        # 提醒内容
        reminder_label = QLabel("⚠️ 重要提醒 ⚠️")
        reminder_label.setAlignment(Qt.AlignCenter)
        reminder_label.setStyleSheet("font-size: 24px; margin: 15px; font-weight: bold;")
        layout.addWidget(reminder_label)

        # 根据是否需要显示花纹板提醒来设置内容
        if show_pattern_reminder:
            content_text = "核对花纹板！\n\n改筋板！\n\n改插孔板！"
        else:
            content_text = "改筋板！\n\n改插孔板！"

        content_label = QLabel(content_text)
        content_label.setAlignment(Qt.AlignCenter)
        content_label.setStyleSheet("font-size: 18px; margin: 20px; font-weight: bold; padding: 10px;")
        content_label.setWordWrap(True)  # 允许文本换行
        content_label.setMinimumHeight(150)  # 设置最小高度确保所有内容显示
        layout.addWidget(content_label)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)

        self.setLayout(layout)


class PLPrefixDialog(QDialog):
    """询问是否添加PL前缀的对话框"""
    def __init__(self, parent=None, data_examples=None):
        super().__init__(parent)
        self.setWindowTitle("⚠️ 数据格式确认 ⚠️")
        self.setFixedSize(600, 400)
        self.setModal(True)
        self.result = False  # 默认不添加PL前缀

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #fff3cd;
                border: 3px solid #ffc107;
            }
            QLabel {
                color: #856404;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton {
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border: none;
                border-radius: 4px;
                min-height: 35px;
                min-width: 100px;
            }
            QPushButton#yes_btn {
                background-color: #28a745;
                color: white;
            }
            QPushButton#yes_btn:hover {
                background-color: #218838;
            }
            QPushButton#no_btn {
                background-color: #dc3545;
                color: white;
            }
            QPushButton#no_btn:hover {
                background-color: #c82333;
            }
        """)

        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("⚠️ 发现特殊格式的截面型材数据 ⚠️")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; margin: 15px; font-weight: bold;")
        layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel("检测到以下格式的数据：")
        info_label.setStyleSheet("font-size: 14px; margin: 10px;")
        layout.addWidget(info_label)

        # 数据示例
        if data_examples:
            examples_text = "\n".join([f"• {example}" for example in data_examples[:10]])  # 最多显示10个示例
            if len(data_examples) > 10:
                examples_text += f"\n... 还有 {len(data_examples) - 10} 个类似数据"
        else:
            examples_text = "• 10*177\n• 8*177\n• 6*1709"

        examples_label = QLabel(examples_text)
        examples_label.setStyleSheet("font-size: 12px; margin: 10px; background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;")
        examples_label.setWordWrap(True)
        layout.addWidget(examples_label)

        # 询问文字
        question_label = QLabel("这些数据是否需要在前面加上 'PL' 前缀？\n（例如：10*177 → PL10*177）")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("font-size: 14px; margin: 15px; font-weight: bold;")
        layout.addWidget(question_label)

        # 按钮
        button_layout = QHBoxLayout()

        yes_btn = QPushButton("是，添加PL前缀")
        yes_btn.setObjectName("yes_btn")
        yes_btn.clicked.connect(self.accept_with_pl)

        no_btn = QPushButton("否，保持原样")
        no_btn.setObjectName("no_btn")
        no_btn.clicked.connect(self.reject_pl)

        button_layout.addWidget(yes_btn)
        button_layout.addWidget(no_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def accept_with_pl(self):
        self.result = True
        self.accept()

    def reject_pl(self):
        self.result = False
        self.accept()


class MultiplicationFormatDialog(QDialog):
    """乘法格式提醒对话框"""
    def __init__(self, parent=None, data_examples=None):
        super().__init__(parent)
        self.setWindowTitle("⚠️ 乘法格式提醒 ⚠️")
        self.setFixedSize(550, 350)
        self.setModal(True)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #d1ecf1;
                border: 3px solid #17a2b8;
            }
            QLabel {
                color: #0c5460;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton {
                background-color: #17a2b8;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border: none;
                border-radius: 4px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("⚠️ 发现乘法格式的截面型材数据 ⚠️")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; margin: 15px; font-weight: bold;")
        layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel("检测到以下使用 'x' 作为乘号的数据：")
        info_label.setStyleSheet("font-size: 14px; margin: 10px;")
        layout.addWidget(info_label)

        # 数据示例
        if data_examples:
            examples_text = "\n".join([f"• {example}" for example in data_examples[:10]])  # 最多显示10个示例
            if len(data_examples) > 10:
                examples_text += f"\n... 还有 {len(data_examples) - 10} 个类似数据"
        else:
            examples_text = "• 10x177\n• 8x200\n• 6x1709"

        examples_label = QLabel(examples_text)
        examples_label.setStyleSheet("font-size: 12px; margin: 10px; background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;")
        examples_label.setWordWrap(True)
        layout.addWidget(examples_label)

        # 提醒文字
        reminder_label = QLabel("请注意：系统将自动把 'x' 替换为 '*' 进行处理\n请确认数据格式是否正确！")
        reminder_label.setAlignment(Qt.AlignCenter)
        reminder_label.setStyleSheet("font-size: 14px; margin: 15px; font-weight: bold; color: #721c24;")
        layout.addWidget(reminder_label)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)

        self.setLayout(layout)


class ZFDuplicateDialog(QDialog):
    """直发配件重复警告对话框"""
    def __init__(self, parent=None, duplicate_parts=None):
        super().__init__(parent)
        self.setWindowTitle("⚠️ 直发配件重复警告 ⚠️")
        self.setFixedSize(600, 400)
        self.setModal(True)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8d7da;
                border: 3px solid #dc3545;
            }
            QLabel {
                color: #721c24;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton {
                background-color: #dc3545;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border: none;
                border-radius: 4px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)

        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("⚠️ 发现直发配件与跨零件清单重复 ⚠️")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; margin: 15px; font-weight: bold;")
        layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel("以下零件同时存在于直发配件清单和跨零件清单中，这可能导致重复加工或材料浪费：")
        info_label.setStyleSheet("font-size: 14px; margin: 10px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 重复零件列表
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        if duplicate_parts:
            for info in duplicate_parts:
                part_label = QLabel(f"• {info}")
                part_label.setWordWrap(True)
                part_label.setStyleSheet("font-size: 12px; margin: 5px;")
                scroll_layout.addWidget(part_label)
        else:
            part_label = QLabel("• 未找到具体重复信息")
            part_label.setStyleSheet("font-size: 12px; margin: 5px;")
            scroll_layout.addWidget(part_label)

        scroll_content.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        # 提示
        warning_label = QLabel("请检查这些零件是否应该从直发配件清单中移除，或从跨零件清单中移除，以避免重复加工。")
        warning_label.setStyleSheet("font-size: 14px; margin: 10px; font-weight: bold;")
        warning_label.setWordWrap(True)
        layout.addWidget(warning_label)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)

        self.setLayout(layout)


class ProcessingThread(QThread):
    """处理线程，避免界面卡顿"""
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(bool, str)  # 完成信号(成功/失败, 消息)
    pl_prefix_request = pyqtSignal(list)  # 请求PL前缀确认的信号
    multiplication_format_warning = pyqtSignal(list)  # 乘法格式警告信号

    def __init__(self, input_path, create_zip=False):
        super().__init__()
        self.input_path = input_path
        self.create_zip = create_zip  # 是否创建ZIP压缩包
        self.missing_dxf_files = []  # 记录缺失的DXF文件
        self.duplicate_files = []  # 记录重复的文件名
        self.case_duplicate_parts = []  # 记录大小写重复的零件名
        self.all_part_names = []  # 记录所有零件名用于检查重复
        self.fl_warnings = []  # 记录FL开头的警告
        self.chinese_warnings = []  # 记录包含汉字的警告
        self.no_hi_warnings = []  # 记录没有HI型材的警告
        self.pattern_keywords_found = []  # 记录发现的花纹板相关关键词
        self.pl_prefix_data = []  # 记录需要PL前缀确认的数据
        self.multiplication_format_data = []  # 记录乘法格式的数据
        self.add_pl_prefix = False  # 是否添加PL前缀的用户选择
        self.processed_dxf_files = []  # 记录所有处理过的DXF文件
        self.zf_duplicate_parts = []  # 记录直发配件与其他清单重复的零件
    
    def run(self):
        try:
            self.progress.emit("开始处理清单文件...")
            
            excel_path = f'{self.input_path}\\清单'
            dxf_path = f'{self.input_path}\\NC_dxf'
            
            # 检查路径是否存在
            if not os.path.exists(excel_path):
                self.finished.emit(False, f"清单文件夹不存在: {excel_path}")
                return
            
            if not os.path.exists(dxf_path):
                self.finished.emit(False, f"NC_dxf文件夹不存在: {dxf_path}")
                return
            
            # 第一步：重命名DXF文件
            self.progress.emit("正在重命名DXF文件...")
            self.rename_dxf_files(dxf_path)

            # 在重命名完成后立即创建DXF压缩包（如果启用）
            zip_created = False
            if self.create_zip:
                self.progress.emit("正在创建DXF文件压缩包...")
                zip_created = self.create_dxf_zip(dxf_path)
                if zip_created:
                    self.progress.emit("✓ DXF文件压缩包创建完成")
            
            # 第二步：处理Excel文件
            self.progress.emit("正在处理Excel文件...")
            zf_parts = pd.DataFrame(columns=['零件编号'])
            df_gjList = None
            
            # 处理报料模板和零件所属构件清单
            for file_name in os.listdir(excel_path):
                if '报料模板' in file_name:
                    self.progress.emit(f"处理报料模板: {file_name}")
                    df_gjList = self.process_template_file(excel_path, file_name)
                
                elif '零件所属构件清单' in file_name:
                    self.progress.emit(f"处理零件所属构件清单: {file_name}")
                    zf_parts = self.process_component_list(excel_path, file_name, dxf_path, zf_parts)
            
            # 处理跨零件清单
            for file_name in os.listdir(excel_path):
                if '跨零件清单' in file_name:
                    self.progress.emit(f"处理跨零件清单: {file_name}")
                    self.process_cross_part_list(excel_path, file_name, dxf_path, zf_parts, df_gjList)
            
            # 检查大小写重复的零件名
            self.check_case_duplicate_parts()

            # 生成缺失DXF文件日志
            if self.missing_dxf_files:
                self.create_missing_dxf_log(dxf_path)

            # 生成重复文件名日志
            if self.duplicate_files:
                self.create_duplicate_files_log(dxf_path)

            # 生成大小写重复零件名日志
            if self.case_duplicate_parts:
                self.create_case_duplicate_log(dxf_path)
            
            # 生成直发配件重复零件日志
            if self.zf_duplicate_parts:
                self.create_zf_duplicate_log(dxf_path)

            # 生成汇总警告信息
            self.create_summary_warnings()

            success_msg = "所有文件处理完成，整理好的清单在NC_dxf文件夹下。"

            # 添加各种警告信息
            if self.missing_dxf_files:
                success_msg += f"\n注意：有 {len(self.missing_dxf_files)} 个DXF文件未找到，详情请查看缺失文件日志。"
            if self.duplicate_files:
                success_msg += f"\n⚠️ 重要警告：发现 {len(self.duplicate_files)} 个重复文件名！零件所属构件清单和跨零件清单不能有相同名字的文件！"
            if self.case_duplicate_parts:
                success_msg += f"\n⚠️ 重点提醒：发现 {len(self.case_duplicate_parts)} 组大小写重复的零件名！例如B1和b1不能同时出现！请仔细检查！"
            if self.zf_duplicate_parts:
                success_msg += f"\n⚠️ 重要警告：发现 {len(self.zf_duplicate_parts)} 个零件同时存在于直发配件清单和跨零件清单中！"
            if self.fl_warnings:
                success_msg += f"\n⚠️ 重要警告：发现 {len(self.fl_warnings)} 个FL开头的规格或截面型材，已自动替换为PL。请仔细检查！"
            if self.chinese_warnings:
                success_msg += f"\n⚠️ 警告：发现 {len(self.chinese_warnings)} 个包含汉字的规格，请检查数据准确性。"
            if self.no_hi_warnings:
                success_msg += f"\n⚠️ 警告：发现 {len(self.no_hi_warnings)} 个表格没有HI型材，请确认是否正常。"
            if self.pattern_keywords_found:
                success_msg += f"\n⚠️ 重要提醒：发现 {len(self.pattern_keywords_found)} 个花纹板相关关键词，请注意核对花纹板！"
            if self.pl_prefix_data:
                success_msg += f"\n⚠️ 提醒：发现 {len(self.pl_prefix_data)} 个可能需要PL前缀的数据格式。"
            if self.multiplication_format_data:
                success_msg += f"\n⚠️ 提醒：发现 {len(self.multiplication_format_data)} 个乘法格式数据，已自动将'x'替换为'*'。"

            # 构建特殊标记
            special_flags = []
            if self.fl_warnings:
                special_flags.append("FL_WARNING")
            if self.pattern_keywords_found:
                special_flags.append("PATTERN_WARNING")
            if self.pl_prefix_data:
                special_flags.append("PL_PREFIX_WARNING")
            if self.multiplication_format_data:
                special_flags.append("MULTIPLICATION_WARNING")
            if self.zf_duplicate_parts:
                special_flags.append("ZF_DUPLICATE_WARNING")

            flag_string = "|" + "|".join(special_flags) if special_flags else ""
            self.finished.emit(True, success_msg + flag_string)

        except Exception as e:
            self.finished.emit(False, f"处理过程中出现错误: {str(e)}")

    def create_missing_dxf_log(self, dxf_path):
        """创建缺失DXF文件日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(dxf_path, f"缺失DXF文件日志_{timestamp}.txt")

            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("缺失DXF文件日志\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"缺失文件数量: {len(self.missing_dxf_files)}\n")
                f.write("-" * 50 + "\n\n")

                for i, missing_info in enumerate(self.missing_dxf_files, 1):
                    f.write(f"{i}. {missing_info}\n")

                f.write("\n" + "=" * 50 + "\n")
                f.write("请检查以上文件是否存在于NC_dxf文件夹中\n")

                # 添加缺失文件的简洁列表
                f.write("\n" + "=" * 50 + "\n")
                f.write("缺失的零件号列表：\n")
                f.write("-" * 30 + "\n")

                missing_files = []
                for missing_info in self.missing_dxf_files:
                    # 提取文件名
                    if "源文件:" in missing_info:
                        file_part = missing_info.split("源文件:")[1].split("→")[0].strip()
                        missing_files.append(file_part)

                for file_name in missing_files:
                    f.write(f"{file_name}\n")

            self.progress.emit(f"缺失DXF文件日志已保存: {log_file}")

        except Exception as e:
            self.progress.emit(f"保存缺失文件日志失败: {str(e)}")

    def check_case_duplicate_parts(self):
        """检查大小写重复的零件名"""
        try:
            # 按小写分组检查重复
            part_groups = {}
            for list_type, part_name, file_name in self.all_part_names:
                lower_name = part_name.lower()
                if lower_name not in part_groups:
                    part_groups[lower_name] = []
                part_groups[lower_name].append((list_type, part_name, file_name))

            # 找出有多个不同大小写版本的零件名
            for lower_name, parts in part_groups.items():
                if len(parts) > 1:
                    # 检查是否真的有大小写差异
                    unique_names = set(part[1] for part in parts)
                    if len(unique_names) > 1:
                        duplicate_info = f"大小写重复零件名: {', '.join(unique_names)} (出现在: {', '.join([f'{p[2]}({p[0]})' for p in parts])})"
                        self.case_duplicate_parts.append(duplicate_info)
                        self.progress.emit(f"⚠️ 重要警告: 发现大小写重复的零件名: {', '.join(unique_names)}")

        except Exception as e:
            self.progress.emit(f"检查大小写重复零件名失败: {str(e)}")

    def create_duplicate_files_log(self, dxf_path):
        """创建重复文件名日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(dxf_path, f"重复文件名日志_{timestamp}.txt")

            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("重复文件名日志\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"重复文件数量: {len(self.duplicate_files)}\n")
                f.write("-" * 50 + "\n\n")

                for i, duplicate_info in enumerate(self.duplicate_files, 1):
                    f.write(f"{i}. {duplicate_info}\n")

                f.write("\n" + "=" * 50 + "\n")
                f.write("注意：零件所属构件清单和跨零件清单不能有相同名字的文件！\n")

            self.progress.emit(f"重复文件名日志已保存: {log_file}")

        except Exception as e:
            self.progress.emit(f"保存重复文件名日志失败: {str(e)}")

    def create_case_duplicate_log(self, dxf_path):
        """创建大小写重复零件名日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(dxf_path, f"大小写重复零件名日志_{timestamp}.txt")

            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("⚠️ 大小写重复零件名日志 ⚠️\n")
                f.write("=" * 60 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"重复零件名组数: {len(self.case_duplicate_parts)}\n")
                f.write("-" * 60 + "\n\n")

                f.write("⚠️ 重要提醒：以下零件名存在大小写差异，可能导致混淆！\n")
                f.write("例如：B1 和 b1 不能在清单中同时出现！\n\n")

                for i, duplicate_info in enumerate(self.case_duplicate_parts, 1):
                    f.write(f"{i}. {duplicate_info}\n\n")

                f.write("=" * 60 + "\n")
                f.write("⚠️ 请仔细检查并统一零件名的大小写格式！\n")

            self.progress.emit(f"大小写重复零件名日志已保存: {log_file}")

        except Exception as e:
            self.progress.emit(f"保存大小写重复零件名日志失败: {str(e)}")

    def check_pattern_keywords(self, df, file_name):
        """检查花纹板相关关键词 - 全局检查所有列"""
        pattern_keywords = ['花', '花纹', '花纹板', 'HWB', 'HW']
        found_in_file = False

        for keyword in pattern_keywords:
            # 检查所有文本列
            for column_name in df.columns:
                if df[column_name].dtype == 'object':  # 只检查文本类型的列
                    try:
                        matching_items = df[df[column_name].str.contains(keyword, na=False, regex=False)]
                        if not matching_items.empty:
                            examples = matching_items[column_name].iloc[:3].tolist()
                            keyword_info = f"文件'{file_name}' - 列'{column_name}' - 发现关键词'{keyword}': {', '.join(map(str, examples))}"
                            self.pattern_keywords_found.append(keyword_info)
                            self.progress.emit(f"⚠️ 发现花纹板关键词: 在'{file_name}'的'{column_name}'列中发现'{keyword}'")
                            found_in_file = True
                            break  # 在当前列找到关键词后跳出列循环
                    except Exception as e:
                        # 如果某列无法进行字符串操作，跳过
                        continue

            if found_in_file:
                break  # 在文件中找到任何关键词后就停止搜索其他关键词

    def check_pl_prefix_data(self, df, column_name, file_name):
        """检查需要PL前缀的数据格式"""
        if column_name not in df.columns:
            return

        # 匹配类似 10*177、8*177、6*1709 的格式（数字*数字，且不以PL开头）
        import re
        pattern = r'^(?!PL)\d+\*\d+$'

        matching_items = df[df[column_name].str.match(pattern, na=False)]
        if not matching_items.empty:
            examples = matching_items[column_name].unique().tolist()
            for example in examples:
                data_info = f"文件'{file_name}' - {column_name}: {example}"
                if data_info not in self.pl_prefix_data:
                    self.pl_prefix_data.append(data_info)
            self.progress.emit(f"⚠️ 发现可能需要PL前缀的数据: 在'{file_name}'中发现 {len(examples)} 个")

    def check_multiplication_format(self, df, column_name, file_name):
        """检查乘法格式的数据（使用x而不是*）"""
        if column_name not in df.columns:
            return

        # 匹配包含x作为乘号的格式
        import re
        pattern = r'\d+x\d+'

        matching_items = df[df[column_name].str.contains(pattern, na=False, regex=True, case=False)]
        if not matching_items.empty:
            examples = matching_items[column_name].unique().tolist()
            for example in examples:
                data_info = f"文件'{file_name}' - {column_name}: {example}"
                if data_info not in self.multiplication_format_data:
                    self.multiplication_format_data.append(data_info)
            self.progress.emit(f"⚠️ 发现乘法格式数据: 在'{file_name}'中发现 {len(examples)} 个使用'x'的数据")

    def process_multiplication_format_data(self, df, column_name):
        """处理乘法格式数据，将x替换为*"""
        if column_name not in df.columns:
            return df

        # 将x替换为*（忽略大小写）
        import re
        df[column_name] = df[column_name].str.replace(r'(\d+)x(\d+)', r'\1*\2', regex=True, case=False)
        return df

    def create_summary_warnings(self):
        """创建汇总警告信息"""
        if self.missing_dxf_files:
            self.progress.emit("\n" + "=" * 50)
            self.progress.emit("缺失的零件号汇总：")
            self.progress.emit("-" * 30)

            missing_files = []
            for missing_info in self.missing_dxf_files:
                if "源文件:" in missing_info:
                    file_part = missing_info.split("源文件:")[1].split("→")[0].strip()
                    missing_files.append(file_part)

            for file_name in missing_files:
                self.progress.emit(f"• {file_name}")
            self.progress.emit("=" * 50)

        # FL开头警告汇总
        if self.fl_warnings:
            self.progress.emit("\n⚠️ FL开头规格汇总（已替换为PL）：")
            for warning in self.fl_warnings:
                self.progress.emit(f"• {warning}")

        # 汉字警告汇总
        if self.chinese_warnings:
            self.progress.emit("\n⚠️ 包含汉字的规格汇总：")
            for warning in self.chinese_warnings:
                self.progress.emit(f"• {warning}")

        # 没有HI型材警告汇总
        if self.no_hi_warnings:
            self.progress.emit("\n⚠️ 没有HI型材的表格汇总：")
            for warning in self.no_hi_warnings:
                self.progress.emit(f"• {warning}")

        # 花纹板关键词汇总
        if self.pattern_keywords_found:
            self.progress.emit("\n⚠️ 花纹板关键词汇总：")
            for warning in self.pattern_keywords_found:
                self.progress.emit(f"• {warning}")

        # PL前缀数据汇总
        if self.pl_prefix_data:
            self.progress.emit("\n⚠️ 可能需要PL前缀的数据汇总：")
            for warning in self.pl_prefix_data:
                self.progress.emit(f"• {warning}")

        # 乘法格式数据汇总
        if self.multiplication_format_data:
            self.progress.emit("\n⚠️ 乘法格式数据汇总（已替换x为*）：")
            for warning in self.multiplication_format_data:
                self.progress.emit(f"• {warning}")

    def create_dxf_zip(self, dxf_path):
        """创建DXF文件的ZIP压缩包"""
        try:
            # 在重命名完成后立即创建压缩包
            if not self.create_zip:
                return False

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            zip_filename = f"处理后的DXF文件_{timestamp}.zip"
            zip_path = os.path.join(self.input_path, zip_filename)

            # self.progress.emit("开始创建DXF文件压缩包...")

            # 获取所有DXF文件
            dxf_files = []
            for file in os.listdir(dxf_path):
                if file.lower().endswith('.dxf'):
                    dxf_files.append(os.path.join(dxf_path, file))

            if not dxf_files:
                self.progress.emit("⚠️ 警告: NC_dxf文件夹中没有找到DXF文件")
                return False

            # 创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for dxf_file in dxf_files:
                    # 只保存文件名，不包含路径
                    arcname = os.path.basename(dxf_file)
                    zipf.write(dxf_file, arcname)
                    # self.progress.emit(f"添加到压缩包: {arcname}")

            file_count = len(dxf_files)
            zip_size = os.path.getsize(zip_path) / (1024 * 1024)  # 转换为MB

            self.progress.emit(f"✓ ZIP压缩包创建成功!")
            # self.progress.emit(f"  文件路径: {zip_path}")
            self.progress.emit(f"  包含文件: {file_count} 个DXF文件")
            # self.progress.emit(f"  压缩包大小: {zip_size:.2f} MB")

            return True

        except Exception as e:
            self.progress.emit(f"✗ 创建ZIP压缩包失败: {str(e)}")
            return False
    
    def rename_dxf_files(self, folder_path):
        """重命名DXF文件"""
        for filename in os.listdir(folder_path):
            old_path = os.path.join(folder_path, filename)
            if os.path.isfile(old_path) and filename.lower().endswith('.dxf'):
                if filename.endswith('A.dxf'):
                    new_name = filename.replace('A.dxf', 'F1.dxf')
                elif filename.endswith('B.dxf'):
                    new_name = filename.replace('B.dxf', 'F2.dxf')
                elif filename.endswith('C.dxf'):
                    new_name = filename.replace('C.dxf', 'W1.dxf')
                else:
                    continue
                
                new_path = os.path.join(folder_path, new_name)
                os.rename(old_path, new_path)
                self.progress.emit(f"重命名: {filename} → {new_name}")
    
    def process_template_file(self, excel_path, file_name):
        """处理报料模板文件"""
        file_path = os.path.join(excel_path, file_name)
        df_gjList = pd.read_excel(file_path)
        df_gjList.columns = df_gjList.columns.str.strip()
        df_gjList.columns = df_gjList.columns.str.replace(' ', '')
        df_gjList = df_gjList.dropna(subset=['板厚(mm)'])
        df_gjList = df_gjList[['构件号', '零件编号', '板厚(mm)', '板宽(mm)', '板长(mm)', '材质']]
        df_gjList = df_gjList.drop_duplicates(subset='零件编号')
        df_gjList['零件编号'] = df_gjList['零件编号'].str.strip()
        return df_gjList
    
    def process_component_list(self, excel_path, file_name, dxf_path, zf_parts):
        """处理零件所属构件清单"""
        file_path = os.path.join(excel_path, file_name)

        # 检查Sheet名称
        try:
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            self.progress.emit(f"Excel文件包含的Sheet: {', '.join(sheet_names)}")

            # 检查是否有正确的Sheet名称
            expected_sheet_patterns = ['零件所属构件清单', '清单', 'Sheet1']
            target_sheet = None

            for sheet in sheet_names:
                if any(pattern in sheet for pattern in expected_sheet_patterns):
                    target_sheet = sheet
                    break

            if target_sheet is None:
                raise ValueError(f"未找到合适的Sheet页面。当前Sheet名称: {', '.join(sheet_names)}。期望包含: {', '.join(expected_sheet_patterns)}")

            self.progress.emit(f"使用Sheet页面: {target_sheet}")

        except Exception as e:
            raise ValueError(f"读取Excel文件Sheet信息失败: {str(e)}")

        # 先读取前几行检查表头
        try:
            # 读取前10行来检查表头位置
            df_check = pd.read_excel(file_path, sheet_name=target_sheet, nrows=10)

            # 检查第5行（索引4）是否是表头
            if len(df_check) < 5:
                raise ValueError(f"Excel文件行数不足，无法读取到第5行。当前行数: {len(df_check)}")

            # 读取第5行作为表头检查
            df_header_check = pd.read_excel(file_path, sheet_name=target_sheet, skiprows=4, nrows=1)
            header_row = df_header_check.columns.tolist()

            # 检查必要的列是否存在
            required_columns = ['所属构件', '零件编号', '规格', '长度', '数量', '材质']
            missing_columns = []

            for col in required_columns:
                if not any(col in str(h) for h in header_row):
                    missing_columns.append(col)

            if missing_columns:
                raise ValueError(f"第5行不是正确的表头行。缺少必要列: {', '.join(missing_columns)}。当前表头: {', '.join(str(h) for h in header_row)}")

            self.progress.emit("✓ 第5行表头验证通过")

        except Exception as e:
            raise ValueError(f"表头验证失败: {str(e)}")

        # 正式读取数据
        df_ljList = pd.read_excel(file_path, sheet_name=target_sheet, skiprows=4)
        df_ljList.columns = df_ljList.columns.str.strip()

        # 全局检查花纹板关键词（检查所有列）
        self.check_pattern_keywords(df_ljList, file_name)

        # 检查规格列中的各种情况
        if '规格' in df_ljList.columns:
            # 检查PL前缀数据
            self.check_pl_prefix_data(df_ljList, '规格', file_name)

            # 检查乘法格式数据
            self.check_multiplication_format(df_ljList, '规格', file_name)

            # 处理乘法格式数据（将x替换为*）
            df_ljList = self.process_multiplication_format_data(df_ljList, '规格')

            # 检查FL开头的
            fl_items = df_ljList[df_ljList['规格'].str.contains('FL', na=False, regex=False)]
            if not fl_items.empty:
                fl_examples = fl_items['规格'].iloc[:3].tolist()
                warning_msg = f"表格'{file_name}' - FL开头规格: {', '.join(map(str, fl_examples))}"
                self.fl_warnings.append(warning_msg)
                self.progress.emit(f"⚠️ 警告: 在'{file_name}'中发现FL开头的规格，已自动替换为PL")

            # 检查规格列中是否包含汉字
            chinese_pattern = r'[\u4e00-\u9fff]'
            chinese_items = df_ljList[df_ljList['规格'].str.contains(chinese_pattern, na=False, regex=True)]
            if not chinese_items.empty:
                chinese_examples = chinese_items['规格'].iloc[:3].tolist()
                warning_msg = f"表格'{file_name}' - 包含汉字规格: {', '.join(map(str, chinese_examples))}"
                self.chinese_warnings.append(warning_msg)
                self.progress.emit(f"⚠️ 警告: 在'{file_name}'中发现包含汉字的规格: {', '.join(map(str, chinese_examples))}")
        
        df_ljList = df_ljList.drop_duplicates(subset=['所属构件'], keep=False)
        df_ljList = df_ljList[df_ljList['零件编号'] != '总计']
        df_ljList = df_ljList[df_ljList['规格'].str.contains('PL|F', na=False)]
        df_ljList = df_ljList.reset_index(drop=True)
        df_ljList['规格'] = df_ljList['规格'].str.replace('PL', '').str.replace('FL', '')
        df_ljList[['厚度', '宽度']] = df_ljList['规格'].str.split('*', expand=True)
        df_ljList = df_ljList[['所属构件', '零件编号', '厚度', '宽度', '长度', '数量', '材质']]

        # 收集零件名用于重复检查
        for _, row in df_ljList.iterrows():
            part_name = str(row['零件编号']).strip()
            if part_name:
                self.all_part_names.append(('零件所属构件清单', part_name, file_name))

        zf_parts = pd.concat([zf_parts, df_ljList[['零件编号']]], ignore_index=True)
        
        # 复制DXF文件
        for i in range(len(df_ljList)):
            lj_name = str(df_ljList.loc[i, '零件编号']).strip()
            gj_name = str(df_ljList.loc[i, '所属构件']).strip()
            dxf_file = os.path.join(dxf_path, f'{lj_name}.dxf')
            new_dxf_file = os.path.join(dxf_path, f'{gj_name}.dxf')
            try:
                if os.path.exists(dxf_file):
                    shutil.copy2(dxf_file, new_dxf_file)
                    # self.progress.emit(f"✓ 复制DXF: {lj_name}.dxf → {gj_name}.dxf")
                else:
                    missing_info = f"源文件: {lj_name}.dxf → 目标文件: {gj_name}.dxf (零件所属构件清单)"
                    self.missing_dxf_files.append(missing_info)
                    self.progress.emit(f"✗ 缺失DXF文件: {lj_name}.dxf")
            except Exception as e:
                missing_info = f"源文件: {lj_name}.dxf → 目标文件: {gj_name}.dxf (复制失败: {str(e)})"
                self.missing_dxf_files.append(missing_info)
                self.progress.emit(f"✗ 复制失败: {lj_name}.dxf → {gj_name}.dxf")
        
        # 保存直发配件清单
        df_ljList = df_ljList.drop(columns=['零件编号'])
        df_ljList.rename(columns={'所属构件': '零件编号'}, inplace=True)
        df_ljList['下料数量'] = None
        df_ljList['领料数量'] = None
        df_ljList['备注'] = None
        df_ljList.to_excel(os.path.join(dxf_path, '直发配件清单.xlsx'), index=False)
        
        # 保存导入文件
        df_ljList.rename(columns={'数量': '最小数量'}, inplace=True)
        df_ljList['材质'] = "0"
        df_ljList['文件夹'] = dxf_path
        df_ljList = df_ljList[['零件编号', '最小数量', '材质', '厚度', '文件夹']]
        df_ljList.to_csv(os.path.join(dxf_path, '直发配件导入.csv'), index=False, encoding='GB18030')
        
        return zf_parts
    
    def process_cross_part_list(self, excel_path, file_name, dxf_path, zf_parts, df_gjList):
        """处理跨零件清单"""
        n_K = file_name[0]  # 获取第一个字符作为跨号
        file_path = os.path.join(excel_path, file_name)
        
        # 初始化df_temp为空DataFrame，避免未赋值错误
        df_temp = pd.DataFrame()
        
        try:
            # 读取Excel文件，从第四行开始（skiprows=3表示跳过前三行）
            df = pd.read_excel(file_path, skiprows=3)
            # 去除df中数据的前后空格
            df.columns = df.columns.str.strip()

            # 将零件编号中的浮点数转为整数（安全转换）
            # 确保零件编号列是字符串格式
            # 在这里加一个判断，如果零件编号列中是b1、b2、b3等，则保持为字符串格式，如果是数字，则转换为整数格式
            if '零件编号' in df.columns and df['零件编号'].dtype == 'float64':  # 检查零件编号列的数据类型
                df['零件编号'] = df['零件编号'].apply(lambda x: str(int(x)) if pd.notnull(x) and x.is_integer() else str(x))  # 保留非数字值为字符串

            # 删除截面型材为空值的行
            df = df.dropna(subset=['截面型材'])

            # 将截面型材中的FL字符改为PL
            df['截面型材'] = df['截面型材'].str.replace('FL', 'PL')
            
            # 全局检查花纹板关键词（检查所有列）
            self.check_pattern_keywords(df, file_name)
            
            # 检查截面型材中的各种情况
            if '截面型材' in df.columns:
                # 检查PL前缀数据
                self.check_pl_prefix_data(df, '截面型材', file_name)
                
                # 检查乘法格式数据
                self.check_multiplication_format(df, '截面型材', file_name)
                
                # 处理乘法格式数据（将x替换为*）
                df = self.process_multiplication_format_data(df, '截面型材')
                
                # 检查FL开头的
                fl_items = df[df['截面型材'].str.contains('FL', na=False, regex=False)]
                if not fl_items.empty:
                    fl_examples = fl_items['截面型材'].iloc[:3].tolist()
                    warning_msg = f"表格'{file_name}' - FL开头截面型材: {', '.join(map(str, fl_examples))}"
                    self.fl_warnings.append(warning_msg)
                    self.progress.emit(f"⚠️ 警告: 在'{file_name}'中发现FL开头的截面型材，已自动替换为PL")
                
                # 检查汉字
                chinese_pattern = r'[\u4e00-\u9fff]'
                chinese_items = df[df['截面型材'].str.contains(chinese_pattern, na=False, regex=True)]
                if not chinese_items.empty:
                    chinese_examples = chinese_items['截面型材'].iloc[:3].tolist()
                    warning_msg = f"表格'{file_name}' - 包含汉字的截面型材: {', '.join(map(str, chinese_examples))}"
                    self.chinese_warnings.append(warning_msg)
                    self.progress.emit(f"⚠️ 警告: 在'{file_name}'中发现包含汉字的截面型材")
                
                # 检查是否有HI型材
                if not df['截面型材'].str.contains('HI', na=False, regex=False).any():
                    warning_msg = f"表格'{file_name}' - 没有发现HI型材"
                    self.no_hi_warnings.append(warning_msg)
                    self.progress.emit(f"⚠️ 警告: 在'{file_name}'中没有发现HI型材")
                
                # 筛选出截面型材包含PL字符的行
                df_temp = df[df['截面型材'].str.contains('PL|FL', na=False)].copy()

                # 重置索引
                df_temp = df_temp.reset_index(drop=True)

                # 删除截面型材列中的"PL"和"FL"
                df_temp['截面型材'] = df_temp['截面型材'].str.replace('PL', '').str.replace('FL', '')

                # 将截面型材使用*分割，列名分别为厚度、宽度
                df_temp[['厚度', '宽度']] = df_temp['截面型材'].str.split('*', expand=True)

                # 整理数据，只保留零件编号、厚度、宽度、长度、数量、材质列
                df_temp = df_temp[['零件编号', '厚度', '宽度', '长度', '数量', '材质']]

                # 处理HI型材
                df_temp2 = df[df['截面型材'].str.contains('HI', na=False)].copy()
                if not df_temp2.empty:
                    self.progress.emit(f"✓ 在'{file_name}'中发现HI型材，开始处理...")
                    # 重置索引
                    df_temp2 = df_temp2.reset_index(drop=True)
                    # 删除截面型材列中不包含"HI"的行
                    df_temp2 = df_temp2[df_temp2['截面型材'].str.contains('HI', na=False)]
                    # 只保留零件编号、数量
                    df_temp2 = df_temp2[['零件编号', '数量']]

                    # 将每行，依据零件编号分成三行，零件编号的值为"零件编号+F1、零件编号+F2、零件编号+W1"，数量列保持不变
                    df_temp3 = pd.DataFrame(columns=['零件编号', '数量'])
                    for i in range(len(df_temp2)):
                        lj_name = df_temp2.loc[i, '零件编号']
                        # 将lj_name转换为字符串格式
                        lj_name = str(lj_name).strip()
                        df_temp3.loc[i*3, '零件编号'] = f'{lj_name}F1'
                        df_temp3.loc[i*3+1, '零件编号'] = f'{lj_name}F2'
                        df_temp3.loc[i*3+2, '零件编号'] = f'{lj_name}W1'
                        df_temp3.loc[i*3:i*3+2, '数量'] = df_temp2.loc[i, '数量']

                    # 根据零件编号，从df_gjList中查找对应的'板厚(mm)', '板宽(mm)', '板长(mm)'的值，并添加到df_temp3中
                    if df_gjList is not None:
                        df_temp3 = df_temp3.merge(
                            df_gjList[['零件编号', '板厚(mm)', '板宽(mm)', '板长(mm)', '材质']],
                            on='零件编号',
                            how='left'
                        )
                        # 整理数据，只保留零件编号、板厚、宽度、长度、数量列
                        df_temp3 = df_temp3[['零件编号', '板厚(mm)', '板宽(mm)', '板长(mm)', '数量', '材质']]
                        # 将'板厚(mm)', '板宽(mm)', '板长(mm)'的列名改为'厚度', '宽度', '长度'
                        df_temp3.rename(columns={'板厚(mm)': '厚度', '板宽(mm)': '宽度', '板长(mm)': '长度'}, inplace=True)
                        # 将df_temp3中的数据追加到df_temp中
                        df_temp3_non_empty = df_temp3.dropna(axis=1, how='all')
                        df_temp = pd.concat([df_temp, df_temp3_non_empty], ignore_index=True)
                        self.progress.emit(f"✓ HI型材处理完成，生成了 {len(df_temp3)} 个子零件")
                    else:
                        self.progress.emit("⚠️ 警告: 未找到报料模板，无法处理HI型材的详细信息")

                # 添加新零件编号列 - 在跨号后添加"-"
                if n_K != '0':
                    df_temp['零件编号'] = df_temp['零件编号'].astype(str)
                    df_temp['新零件编号'] = n_K + "-" + df_temp['零件编号']
                else:
                    df_temp['新零件编号'] = df_temp['零件编号']
                
                # 收集零件名用于重复检查
                for _, row in df_temp.iterrows():
                    part_name = str(row['零件编号']).strip()
                    new_part_name = str(row['新零件编号']).strip()
                    if part_name:
                        self.all_part_names.append((f'{n_K}跨零件清单', part_name, file_name))
                        self.all_part_names.append((f'{n_K}跨零件清单(新)', new_part_name, file_name))

                # 复制DXF文件
                for i in range(len(df_temp)):
                    lj_name = str(df_temp.loc[i, '零件编号']).strip()
                    new_lj_name = str(df_temp.loc[i, '新零件编号']).strip()
                    dxf_file = os.path.join(dxf_path, f'{lj_name}.dxf')
                    new_dxf_file = os.path.join(dxf_path, f'{new_lj_name}.dxf')
                    try:
                        if os.path.exists(dxf_file):
                            shutil.copy2(dxf_file, new_dxf_file)
                            # self.progress.emit(f"✓ 复制DXF: {lj_name}.dxf → {new_lj_name}.dxf")
                        else:
                            missing_info = f"源文件: {lj_name}.dxf → 目标文件: {new_lj_name}.dxf ({n_K}跨零件清单)"
                            self.missing_dxf_files.append(missing_info)
                            self.progress.emit(f"✗ 缺失DXF文件: {lj_name}.dxf")
                    except Exception as e:
                        missing_info = f"源文件: {lj_name}.dxf → 目标文件: {new_lj_name}.dxf (复制失败: {str(e)})"
                        self.missing_dxf_files.append(missing_info)
                        self.progress.emit(f"✗ 复制失败: {lj_name}.dxf → {new_lj_name}.dxf")
            else:
                # 如果没有截面型材列，创建一个空的DataFrame
                df_temp = pd.DataFrame(columns=['零件编号', '厚度', '宽度', '长度', '数量', '材质', '新零件编号'])
                self.progress.emit(f"⚠️ 警告: 在'{file_name}'中未找到'截面型材'列")
            
            # 过滤直发配件
            duplicate_with_zf = []  # 用于存储与直发配件重复的零件

            # 修改：确保正确过滤掉直发配件对应的零件
            if not zf_parts.empty and not df_temp.empty:
                # 将零件编号转换为字符串并去除空格
                df_temp['零件编号'] = df_temp['零件编号'].astype(str).str.strip()
                zf_parts['零件编号'] = zf_parts['零件编号'].astype(str).str.strip()

                # 使用merge找出需要删除的行
                merged = df_temp.merge(zf_parts, on='零件编号', how='left', indicator=True)

                # 找出重复的零件
                duplicates = merged[merged['_merge'] == 'both']
                if not duplicates.empty:
                    duplicate_with_zf = duplicates['零件编号'].tolist()
                    self.progress.emit(f"⚠️ 警告: 发现 {len(duplicate_with_zf)} 个零件同时存在于直发配件清单和跨零件清单中")

                    # 记录重复信息
                    for part in duplicate_with_zf:
                        duplicate_info = f"零件'{part}' 同时存在于直发配件清单和'{file_name}'中"
                        if duplicate_info not in self.zf_duplicate_parts:
                            self.zf_duplicate_parts.append(duplicate_info)

                # 过滤掉直发配件
                df_temp = df_temp[merged['_merge'] == 'left_only'].copy()

                self.progress.emit(f"过滤后的行数: {len(df_temp)}")
            
            # 保存文件
            if not df_temp.empty:
                df_temp = df_temp.drop(columns=['零件编号'])
                df_temp.rename(columns={'新零件编号': '零件编号'}, inplace=True)
                
                # 确保下料数量和领料数量列存在
                if '下料数量' not in df_temp.columns:
                    df_temp['下料数量'] = None
                if '领料数量' not in df_temp.columns:
                    df_temp['领料数量'] = None
                if '备注' not in df_temp.columns:
                    df_temp['备注'] = None
                
                df_temp = df_temp[['零件编号', '厚度', '宽度', '长度', '数量', '材质', '下料数量', '领料数量', '备注']]
                df_temp['材质'] = df_temp['材质'].str.strip()
                df_temp.to_excel(os.path.join(dxf_path, f'{n_K}跨零件清单.xlsx'), index=False)
                
                # 保存导入文件
                df_temp.rename(columns={'数量': '最小数量'}, inplace=True)
                df_temp['材质'] = "0"
                df_temp['文件夹'] = dxf_path
                df_temp = df_temp[['零件编号', '最小数量', '材质', '厚度', '文件夹']]
                df_temp.to_csv(os.path.join(dxf_path, f'{n_K}跨零件导入.csv'), index=False, encoding='GB18030')
                
                self.progress.emit(f"✓ 已保存 {n_K}跨零件清单.xlsx 和 {n_K}跨零件导入.csv")
            else:
                self.progress.emit(f"⚠️ 警告: '{file_name}'中没有符合条件的数据，未生成输出文件")
        
        except Exception as e:
            self.progress.emit(f"✗ 处理'{file_name}'时出错: {str(e)}")
            # 出错时也要返回，避免中断整个处理流程

    def create_zf_duplicate_log(self, dxf_path):
        """创建直发配件重复零件日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(dxf_path, f"直发配件重复零件日志_{timestamp}.txt")

            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("⚠️ 直发配件重复零件日志 ⚠️\n")
                f.write("=" * 60 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"重复零件数量: {len(self.zf_duplicate_parts)}\n")
                f.write("-" * 60 + "\n\n")

                f.write("⚠️ 重要提醒：以下零件同时存在于直发配件清单和跨零件清单中，可能导致重复加工！\n\n")

                for i, duplicate_info in enumerate(self.zf_duplicate_parts, 1):
                    f.write(f"{i}. {duplicate_info}\n\n")

                f.write("=" * 60 + "\n")
                f.write("⚠️ 请检查这些零件是否应该从直发配件清单中移除，或从跨零件清单中移除，以避免重复加工。\n")

            self.progress.emit(f"直发配件重复零件日志已保存: {log_file}")

        except Exception as e:
            self.progress.emit(f"保存直发配件重复零件日志失败: {str(e)}")


class ListProcessorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.processing_thread = None
        self.config = configparser.ConfigParser()
        self.config_file = "list_processor_config.ini"
        self.load_config()
    
    def init_ui(self):
        self.setWindowTitle("清单处理工具 - Qt版")
        self.setGeometry(500, 300, 700, 600)
        self.setMinimumSize(700, 500)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 说明信息
        info_group = QGroupBox("使用说明")
        info_layout = QVBoxLayout()
        info_text = QLabel("""
        <b>清单处理工具说明：</b><br>
        1. 跨零件清单： 跨零件清单（V1.0）<br>
        2. 表格必须有名为：鑫光正金属公司-零件清单（重量）(V3.1)<br>
        3. 直发： 零件所属构件清单(EXCEL)<br>
        4. 分解ABC： 报料模板（V4.1）--带废料是否有孔<br>
        5. 请选择包含"清单"和"NC_dxf"文件夹的项目根目录
        """)
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)
        
        # 路径选择区域
        path_group = QGroupBox("项目路径设置")
        path_layout = QFormLayout()
        
        # 项目路径
        path_row = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("选择包含清单和NC_dxf文件夹的项目根目录")
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_path)
        path_row.addWidget(self.path_edit)
        path_row.addWidget(self.browse_btn)
        path_layout.addRow("项目路径:", path_row)
        
        path_group.setLayout(path_layout)
        main_layout.addWidget(path_group)

        # 设置选项
        options_group = QGroupBox("处理选项")
        options_layout = QVBoxLayout()

        # 第一行选项
        options_row1 = QHBoxLayout()
        self.reminder_check = QCheckBox("处理完成后显示提醒")
        self.reminder_check.setChecked(True)
        self.reminder_check.setToolTip("处理完成后弹出提醒窗口：核对花纹板！改筋板！改插孔板！")
        options_row1.addWidget(self.reminder_check)
        options_row1.addStretch()

        # 第二行选项
        options_row2 = QHBoxLayout()
        self.create_zip_check = QCheckBox("自动创建DXF压缩包")
        self.create_zip_check.setChecked(False)
        self.create_zip_check.setToolTip("处理完成后，如果没有缺失DXF文件，自动将所有DXF文件压缩成ZIP格式保存到项目根目录")
        options_row2.addWidget(self.create_zip_check)
        options_row2.addStretch()

        options_layout.addLayout(options_row1)
        options_layout.addLayout(options_row2)
        options_group.setLayout(options_layout)
        main_layout.addWidget(options_group)

        # 处理按钮
        button_layout = QHBoxLayout()
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setMinimumHeight(40)
        button_layout.addWidget(self.process_btn)
        main_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 日志显示区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def browse_path(self):
        """浏览项目路径"""
        path = QFileDialog.getExistingDirectory(self, "选择项目根目录")
        if path:
            self.path_edit.setText(path)
            # 检查必要的子文件夹
            excel_path = os.path.join(path, "清单")
            dxf_path = os.path.join(path, "NC_dxf")
            
            if not os.path.exists(excel_path):
                QMessageBox.warning(self, "警告", f"未找到清单文件夹: {excel_path}")
            elif not os.path.exists(dxf_path):
                QMessageBox.warning(self, "警告", f"未找到NC_dxf文件夹: {dxf_path}")
            else:
                self.log_text.append(f"✓ 项目路径设置成功: {path}")
                self.log_text.append(f"✓ 找到清单文件夹: {excel_path}")
                self.log_text.append(f"✓ 找到NC_dxf文件夹: {dxf_path}")
    
    def start_processing(self):
        """开始处理"""
        input_path = self.path_edit.text()
        if not input_path:
            QMessageBox.critical(self, "错误", "请先选择项目路径")
            return
        
        if not os.path.exists(input_path):
            QMessageBox.critical(self, "错误", "项目路径不存在")
            return
        
        # 清空日志
        self.log_text.clear()
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 禁用按钮
        self.process_btn.setEnabled(False)
        self.process_btn.setText("处理中...")
        
        # 启动处理线程
        create_zip = self.create_zip_check.isChecked()
        self.processing_thread = ProcessingThread(input_path, create_zip)
        self.processing_thread.progress.connect(self.on_progress)
        self.processing_thread.finished.connect(self.on_finished)
        self.processing_thread.start()
        
        self.statusBar().showMessage("正在处理...")
    
    def on_progress(self, message):
        """更新进度"""
        self.log_text.append(f"[{self.get_current_time()}] {message}")
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)
    
    def on_finished(self, success, message):
        """处理完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮
        self.process_btn.setEnabled(True)
        self.process_btn.setText("开始处理")

        # 解析特殊标记
        special_flags = []
        if "|" in message:
            parts = message.split("|")
            message = parts[0]
            special_flags = parts[1:]

        has_fl_warning = "FL_WARNING" in special_flags
        has_pattern_warning = "PATTERN_WARNING" in special_flags
        has_pl_prefix_warning = "PL_PREFIX_WARNING" in special_flags
        has_multiplication_warning = "MULTIPLICATION_WARNING" in special_flags

        # 显示结果
        if success:
            self.log_text.append(f"[{self.get_current_time()}] ✓ {message}")
            self.statusBar().showMessage("处理完成")
            QMessageBox.information(self, "处理完成", message)

            # 如果有直发配件重复警告，显示特殊对话框
            if "ZF_DUPLICATE_WARNING" in special_flags:
                zf_dialog = ZFDuplicateDialog(self, self.processing_thread.zf_duplicate_parts)
                zf_dialog.exec_()

            # 如果有FL警告，显示特殊提示
            if has_fl_warning:
                fl_msg = QMessageBox(self)
                fl_msg.setWindowTitle("⚠️ FL开头规格警告 ⚠️")
                fl_msg.setIcon(QMessageBox.Warning)
                fl_msg.setText("检测到FL开头的规格或截面型材！")
                fl_msg.setInformativeText("已自动替换为PL，但请仔细检查处理结果是否正确。\n建议核对原始数据和输出文件。")
                fl_msg.setStandardButtons(QMessageBox.Ok)
                fl_msg.setStyleSheet("""
                    QMessageBox {
                        background-color: #fff3cd;
                        border: 2px solid #ffc107;
                    }
                    QMessageBox QLabel {
                        color: #856404;
                        font-weight: bold;
                    }
                """)
                fl_msg.exec_()

            # 如果有PL前缀警告，显示询问对话框
            if has_pl_prefix_warning and hasattr(self.processing_thread, 'pl_prefix_data'):
                pl_dialog = PLPrefixDialog(self, self.processing_thread.pl_prefix_data)
                pl_dialog.exec_()

            # 如果有乘法格式警告，显示提醒对话框
            if has_multiplication_warning and hasattr(self.processing_thread, 'multiplication_format_data'):
                multiplication_dialog = MultiplicationFormatDialog(self, self.processing_thread.multiplication_format_data)
                multiplication_dialog.exec_()

            # 如果启用了提醒，显示提醒对话框
            if self.reminder_check.isChecked():
                # 根据是否有花纹板关键词来决定是否显示花纹板提醒
                show_pattern_reminder = has_pattern_warning
                reminder = ReminderDialog(self, show_pattern_reminder)
                reminder.exec_()
        else:
            self.log_text.append(f"[{self.get_current_time()}] ✗ {message}")
            self.statusBar().showMessage("处理失败")
            QMessageBox.critical(self, "处理失败", message)
    
    def get_current_time(self):
        """获取当前时间字符串"""
        return datetime.now().strftime("%H:%M:%S")

    def load_config(self):
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding='utf-8')

                # 加载项目路径
                if self.config.has_option('Settings', 'project_path'):
                    self.path_edit.setText(self.config.get('Settings', 'project_path'))

                # 加载提醒设置
                if self.config.has_option('Settings', 'show_reminder'):
                    self.reminder_check.setChecked(self.config.getboolean('Settings', 'show_reminder'))

                # 加载ZIP设置
                if self.config.has_option('Settings', 'create_zip'):
                    self.create_zip_check.setChecked(self.config.getboolean('Settings', 'create_zip'))

            except Exception as e:
                self.log_text.append(f"[{self.get_current_time()}] 加载配置失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        try:
            if not self.config.has_section('Settings'):
                self.config.add_section('Settings')

            self.config.set('Settings', 'project_path', self.path_edit.text())
            self.config.set('Settings', 'show_reminder', str(self.reminder_check.isChecked()))
            self.config.set('Settings', 'create_zip', str(self.create_zip_check.isChecked()))

            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)

        except Exception as e:
            print(f"保存配置失败: {str(e)}")

    def closeEvent(self, event):
        """程序关闭时保存配置"""
        self.save_config()
        event.accept()


def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = ListProcessorApp()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()























