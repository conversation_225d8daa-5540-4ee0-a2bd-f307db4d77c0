#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Python全自动打包工具
支持单文件/文件夹打包，图标设置，版本信息，自动依赖处理
基于PyInstaller 5.13.0构建
"""

import sys
import os
import json
import subprocess
import shutil
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QGridLayout,
    QWidget, QLabel, QLineEdit, QPushButton, QFileDialog, QCheckBox,
    QGroupBox, QTextEdit, QProgressBar, QMessageBox, QComboBox,
    QTabWidget, QFormLayout, QSpinBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap

class PackageThread(QThread):
    """打包线程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, config):
        super().__init__()
        self.config = config
    
    def run(self):
        try:
            self.progress.emit("开始检查环境...")
            
            # 检查PyInstaller
            if not self.check_pyinstaller():
                self.progress.emit("正在安装PyInstaller...")
                if not self.install_pyinstaller():
                    self.finished.emit(False, "PyInstaller安装失败")
                    return
            
            # 构建命令
            command = self.build_command()
            self.progress.emit(f"执行命令: {' '.join(command)}")
            
            # 执行打包
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=os.path.dirname(self.config['source_file']) or '.'
            )
            
            if result.returncode == 0:
                self.progress.emit("打包成功！正在整理文件...")
                self.post_process()
                self.finished.emit(True, "打包完成！文件保存在dist目录")
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                self.finished.emit(False, f"打包失败: {error_msg}")
                
        except Exception as e:
            self.finished.emit(False, f"发生错误: {str(e)}")
    
    def check_pyinstaller(self):
        """检查PyInstaller是否安装"""
        try:
            import PyInstaller
            return True
        except ImportError:
            return False
    
    def install_pyinstaller(self):
        """安装PyInstaller"""
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "pyinstaller==5.13.0"
            ])
            return True
        except:
            return False
    
    def build_command(self):
        """构建PyInstaller命令"""
        command = ['pyinstaller', '--clean']
        
        # 基本选项
        if self.config['onefile']:
            command.append('--onefile')
        else:
            command.append('--onedir')
        
        if self.config['noconsole']:
            command.extend(['--noconsole', '--windowed'])
        
        # 图标
        if self.config['icon'] and os.path.exists(self.config['icon']):
            command.extend(['--icon', self.config['icon']])
        
        # 输出名称
        if self.config['name']:
            command.extend(['--name', self.config['name']])
        
        # 版本信息文件
        if self.config['version_info']:
            version_file = self.create_version_file()
            if version_file:
                command.extend(['--version-file', version_file])
        
        # 添加数据文件
        for data_path in self.config.get('add_data', []):
            if os.path.exists(data_path):
                command.extend(['--add-data', f"{data_path};."])
        
        # 隐藏导入
        hidden_imports = [
            'pandas', 'numpy', 'matplotlib', 'requests', 'openpyxl',
            'xlrd', 'xlwt', 'pillow', 'opencv-python', 'scipy'
        ]
        for imp in hidden_imports:
            command.extend(['--hidden-import', imp])
        
        # 源文件
        command.append(self.config['source_file'])
        
        return command
    
    def create_version_file(self):
        """创建版本信息文件"""
        try:
            version_info = self.config['version_info']
            version_content = f'''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({version_info['file_version'].replace('.', ', ')}, 0),
    prodvers=({version_info['product_version'].replace('.', ', ')}, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [
            StringStruct(u'CompanyName', u'{version_info['company']}'),
            StringStruct(u'FileDescription', u'{version_info['description']}'),
            StringStruct(u'FileVersion', u'{version_info['file_version']}'),
            StringStruct(u'InternalName', u'{version_info['internal_name']}'),
            StringStruct(u'LegalCopyright', u'{version_info['copyright']}'),
            StringStruct(u'OriginalFilename', u'{version_info['original_filename']}'),
            StringStruct(u'ProductName', u'{version_info['product_name']}'),
            StringStruct(u'ProductVersion', u'{version_info['product_version']}')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)'''
            
            version_file = 'version_info.txt'
            with open(version_file, 'w', encoding='utf-8') as f:
                f.write(version_content)
            return version_file
        except:
            return None
    
    def post_process(self):
        """后处理：复制文件，清理等"""
        try:
            # 如果有自动运行选项，可以在这里实现
            if self.config.get('auto_run', False):
                self.progress.emit("准备自动运行...")
        except:
            pass

class PythonPackager(QMainWindow):
    """Python打包工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.config = self.load_config()
        self.init_ui()
        self.package_thread = None
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("Python项目打包工具")
        self.setFixedSize(800, 700)
        
        # 设置图标（如果存在）
        if os.path.exists("mylogo.ico"):
            self.setWindowIcon(QIcon("mylogo.ico"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 基本设置选项卡
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "基本设置")
        
        # 高级设置选项卡
        advanced_tab = self.create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级设置")
        
        # 版本信息选项卡
        version_tab = self.create_version_tab()
        tab_widget.addTab(version_tab, "版本信息")
        
        # 进度显示
        progress_group = QGroupBox("打包进度")
        progress_layout = QVBoxLayout()
        
        self.progress_text = QTextEdit()
        self.progress_text.setMaximumHeight(150)
        self.progress_text.setReadOnly(True)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        progress_layout.addWidget(self.progress_text)
        progress_layout.addWidget(self.progress_bar)
        progress_group.setLayout(progress_layout)
        
        main_layout.addWidget(progress_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.package_btn = QPushButton("开始打包")
        self.package_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        self.clear_btn = QPushButton("清空日志")
        self.save_config_btn = QPushButton("保存配置")
        self.load_config_btn = QPushButton("加载配置")
        
        button_layout.addWidget(self.package_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.save_config_btn)
        button_layout.addWidget(self.load_config_btn)
        
        main_layout.addLayout(button_layout)
        
        # 连接信号
        self.package_btn.clicked.connect(self.start_package)
        self.clear_btn.clicked.connect(self.clear_log)
        self.save_config_btn.clicked.connect(self.save_config)
        self.load_config_btn.clicked.connect(self.load_config_dialog)
        
        # 加载配置到界面
        self.load_config_to_ui()

    def create_basic_tab(self):
        """创建基本设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()

        # 主文件选择
        file_group = QGroupBox("主文件")
        file_layout = QGridLayout()

        self.main_file_edit = QLineEdit()
        self.main_file_edit.setPlaceholderText("选择要打包的Python文件(.py)")
        main_file_btn = QPushButton("选择目录")
        main_file_btn.clicked.connect(self.select_main_file)

        file_layout.addWidget(QLabel("主文件:"), 0, 0)
        file_layout.addWidget(self.main_file_edit, 0, 1)
        file_layout.addWidget(main_file_btn, 0, 2)
        file_group.setLayout(file_layout)

        # 图标设置
        icon_group = QGroupBox("图标设置")
        icon_layout = QGridLayout()

        self.icon_edit = QLineEdit()
        self.icon_edit.setPlaceholderText("选择图标文件(.ico)")
        icon_btn = QPushButton("选择图标")
        icon_btn.clicked.connect(self.select_icon)

        icon_layout.addWidget(QLabel("图标文件:"), 0, 0)
        icon_layout.addWidget(self.icon_edit, 0, 1)
        icon_layout.addWidget(icon_btn, 0, 2)
        icon_group.setLayout(icon_layout)

        # 基本选项
        options_group = QGroupBox("打包选项")
        options_layout = QGridLayout()

        self.onefile_check = QCheckBox("打包为单文件 (--onefile)")
        self.onefile_check.setChecked(True)

        self.noconsole_check = QCheckBox("隐藏控制台 (--noconsole)")
        self.noconsole_check.setChecked(True)

        self.auto_run_check = QCheckBox("打包完成后自动运行")

        options_layout.addWidget(self.onefile_check, 0, 0)
        options_layout.addWidget(self.noconsole_check, 0, 1)
        options_layout.addWidget(self.auto_run_check, 1, 0)
        options_group.setLayout(options_layout)

        # 输出设置
        output_group = QGroupBox("输出设置")
        output_layout = QFormLayout()

        self.output_name_edit = QLineEdit()
        self.output_name_edit.setPlaceholderText("输出文件名（不含扩展名）")

        output_layout.addRow("输出文件名:", self.output_name_edit)
        output_group.setLayout(output_layout)

        layout.addWidget(file_group)
        layout.addWidget(icon_group)
        layout.addWidget(options_group)
        layout.addWidget(output_group)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_advanced_tab(self):
        """创建高级设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()

        # 依赖目录
        data_group = QGroupBox("资源/依赖目录")
        data_layout = QVBoxLayout()

        data_info = QLabel("支持添加资源/依赖目录，程序会自动打包相关依赖")
        data_info.setStyleSheet("color: #666; font-style: italic;")

        self.data_paths_edit = QTextEdit()
        self.data_paths_edit.setMaximumHeight(100)
        self.data_paths_edit.setPlaceholderText("每行一个路径，支持文件夹下所有依赖自动打包")

        data_btn_layout = QHBoxLayout()
        add_data_btn = QPushButton("添加目录")
        add_data_btn.clicked.connect(self.add_data_path)
        clear_data_btn = QPushButton("清空")
        clear_data_btn.clicked.connect(lambda: self.data_paths_edit.clear())

        data_btn_layout.addWidget(add_data_btn)
        data_btn_layout.addWidget(clear_data_btn)
        data_btn_layout.addStretch()

        data_layout.addWidget(data_info)
        data_layout.addWidget(self.data_paths_edit)
        data_layout.addLayout(data_btn_layout)
        data_group.setLayout(data_layout)

        # 环境信息
        env_group = QGroupBox("环境信息")
        env_layout = QFormLayout()

        env_info = QLabel("内置 Python 3.9 环境，基于 PyInstaller 5.13.0 构建")
        env_info.setStyleSheet("color: #4CAF50; font-weight: bold;")

        env_layout.addRow("环境:", env_info)
        env_group.setLayout(env_layout)

        layout.addWidget(data_group)
        layout.addWidget(env_group)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_version_tab(self):
        """创建版本信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()

        version_group = QGroupBox("版本信息")
        version_layout = QFormLayout()

        self.company_edit = QLineEdit()
        self.company_edit.setPlaceholderText("公司名称")

        self.product_name_edit = QLineEdit()
        self.product_name_edit.setPlaceholderText("产品名称")

        self.file_version_edit = QLineEdit()
        self.file_version_edit.setPlaceholderText("*******")

        self.product_version_edit = QLineEdit()
        self.product_version_edit.setPlaceholderText("*******")

        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("程序描述")

        self.copyright_edit = QLineEdit()
        self.copyright_edit.setPlaceholderText("版权信息")

        version_layout.addRow("公司名称:", self.company_edit)
        version_layout.addRow("产品名称:", self.product_name_edit)
        version_layout.addRow("文件版本:", self.file_version_edit)
        version_layout.addRow("产品版本:", self.product_version_edit)
        version_layout.addRow("程序描述:", self.description_edit)
        version_layout.addRow("版权信息:", self.copyright_edit)

        version_group.setLayout(version_layout)

        layout.addWidget(version_group)
        layout.addStretch()

        widget.setLayout(layout)
        return widget
