#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
清单处理工具打包脚本
自动化PyInstaller打包过程
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def print_step(step_num, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step_num}: {description}")
    print('='*60)

def run_command(command, description):
    """运行命令并处理结果"""
    print(f"\n执行: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print(f"✓ {description} 成功")
            if result.stdout:
                print("输出:", result.stdout)
            return True
        else:
            print(f"✗ {description} 失败")
            if result.stderr:
                print("错误:", result.stderr)
            return False
    except Exception as e:
        print(f"✗ 执行命令时出错: {str(e)}")
        return False

def check_dependencies():
    """检查必要的依赖"""
    print_step(1, "检查依赖环境")
    
    # 检查Python版本
    python_version = sys.version
    print(f"Python版本: {python_version}")
    
    # 检查必要的包
    required_packages = [
        'PyQt5', 'pandas', 'openpyxl', 'ezdxf', 'numpy'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)

    # 单独检查pyinstaller（通过命令行）
    try:
        result = subprocess.run(['pyinstaller', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ pyinstaller 已安装 (版本: {result.stdout.strip()})")
        else:
            print("✗ pyinstaller 未安装或无法运行")
            missing_packages.append('pyinstaller')
    except FileNotFoundError:
        print("✗ pyinstaller 未安装或无法运行")
        missing_packages.append('pyinstaller')
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        print("请先安装缺少的包，然后重新运行此脚本")
        return False
    
    return True

def clean_build_dirs():
    """清理构建目录"""
    print_step(2, "清理构建目录")

    dirs_to_clean = ['build', 'dist', '__pycache__']
    success = True
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已删除 {dir_name} 目录")
            except Exception as e:
                print(f"✗ 删除 {dir_name} 目录失败: {str(e)}")
                success = False
        else:
            print(f"- {dir_name} 目录不存在，跳过")

    return success

def build_executable():
    """构建可执行文件"""
    print_step(3, "构建可执行文件")
    
    # 检查spec文件是否存在
    spec_file = "清单处理工具.spec"
    if not os.path.exists(spec_file):
        print(f"✗ 找不到 {spec_file} 文件")
        return False
    
    # 使用PyInstaller构建
    command = f"pyinstaller {spec_file}"
    return run_command(command, "PyInstaller构建")

def test_executable():
    """测试生成的可执行文件"""
    print_step(4, "测试可执行文件")
    
    exe_path = os.path.join("dist", "清单处理工具.exe")
    if not os.path.exists(exe_path):
        print(f"✗ 找不到生成的可执行文件: {exe_path}")
        return False
    
    # 获取文件大小
    file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
    print(f"✓ 可执行文件已生成: {exe_path}")
    print(f"✓ 文件大小: {file_size:.2f} MB")
    
    return True

def create_package():
    """创建发布包"""
    print_step(5, "创建发布包")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"清单处理工具_v{timestamp}"
    package_dir = os.path.join("dist", package_name)
    
    try:
        # 创建发布目录
        os.makedirs(package_dir, exist_ok=True)
        
        # 复制可执行文件
        exe_source = os.path.join("dist", "清单处理工具.exe")
        exe_dest = os.path.join(package_dir, "清单处理工具.exe")
        shutil.copy2(exe_source, exe_dest)
        print(f"✓ 已复制可执行文件到 {package_dir}")
        
        # 创建使用说明
        readme_content = """清单处理工具使用说明
========================

版本信息：
- 构建时间：{timestamp}
- Python版本：{python_version}

使用方法：
1. 双击运行"清单处理工具.exe"
2. 选择包含"清单"和"NC_dxf"文件夹的项目根目录
3. 根据需要启用相关功能选项
4. 点击"开始处理"按钮

注意事项：
- 确保项目目录结构正确
- 处理过程中请勿关闭程序
- 处理完成后查看生成的日志文件

技术支持：
如有问题请联系开发人员
""".format(timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
           python_version=sys.version.split()[0])
        
        readme_path = os.path.join(package_dir, "使用说明.txt")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✓ 已创建使用说明文件")
        
        print(f"✓ 发布包已创建: {package_dir}")
        return True
        
    except Exception as e:
        print(f"✗ 创建发布包失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("清单处理工具自动打包脚本")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {script_dir}")
    
    # 执行构建步骤
    steps = [
        check_dependencies,
        clean_build_dirs,
        build_executable,
        test_executable,
        create_package
    ]
    
    for i, step_func in enumerate(steps, 1):
        if not step_func():
            print(f"\n✗ 构建失败，停止在步骤 {i}")
            return False
    
    print(f"\n{'='*60}")
    print("🎉 构建完成！")
    print("✓ 可执行文件已生成并打包")
    print("✓ 可以在 dist 目录中找到发布包")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print('='*60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断了构建过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n构建过程中发生未预期的错误: {str(e)}")
        sys.exit(1)
