# -*- mode: python ; coding: utf-8 -*-
# 清单处理工具 PyInstaller 配置文件
# 优化版本 - 包含所有必要的依赖和配置

import os
import sys

# 获取当前脚本目录
current_dir = os.getcwd()
source_file = os.path.join(current_dir, '清单处理_Qt.py')

a = Analysis(
    [source_file],
    pathex=[current_dir],
    binaries=[],
    datas=[
        # 包含配置文件（如果存在）
        # (os.path.join(current_dir, '*.ini'), '.'),
    ],
    hiddenimports=[
        # PyQt5 相关模块
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.sip',

        # pandas 相关模块
        'pandas',
        'pandas._libs.tslibs.timedeltas',
        'pandas._libs.tslibs.np_datetime',
        'pandas._libs.tslibs.nattype',
        'pandas._libs.properties',
        'pandas.io.formats.style',

        # Excel 处理相关
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.styles',
        'xlrd',
        'xlsxwriter',

        # DXF 处理相关（可选）
        'ezdxf',
        'ezdxf.math',
        'ezdxf.entities',

        # 其他必要模块
        'configparser',
        'zipfile',
        'datetime',
        'glob',
        'shutil',
        're',

        # 数值计算相关
        'numpy',
        'numpy.core',
        'numpy.core._methods',
        'numpy.lib.format',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小文件大小
        'tkinter',
        'matplotlib',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'pytest',
        'setuptools',
        'distutils',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='清单处理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以在这里指定图标文件路径
    version=None,  # 可以在这里指定版本信息文件
)
