from pyautocad import Autocad, APoint


def is_trapezoid(polyline):
    # 检查多边形是否是梯形（简化版本，需要依据实际情况进行改进）
    return len(polyline) == 4  # 假设我们只在四边形中查找梯形


def move_edges(vertices, distance):
    # 简单平移逻辑（假设斜边为最左侧和最右侧的点）
    moved_vertices = []
    for vertex in vertices:
        new_point = APoint(vertex.x - distance, vertex.y)  # 向内平移
        moved_vertices.append(new_point)
    return moved_vertices


def main():
    acad = Autocad()
    # print("当前绘图:", acad.doc.Name)

    for entity in acad.iter_objects("AcDbPolyline"):
        if is_trapezoid(entity):
            # print(f"处理梯形: {entity.Handle}")

            # 获取梯形的点
            points = [APoint(entity.GetPointAt(i)) for i in range(entity.NumberOfVertices)]

            # 找到斜边的顶点（您需要确认逻辑）
            # 这里使用前两个和后两个点作为斜边，实际需根据形状调整
            edges = [points[1], points[2]]  # 假定斜边是第二和第三点，您需调整

            # 处理斜边点向内平移3毫米
            moved_points = move_edges(edges, 3.0)

            # 更新多边形
            acad.model.AddPolyline(moved_points + [points[0], points[3]])  # 确保保持多边形的顺序
            entity.Delete()  # 删除旧的多边形
            print(f"处理梯形: {entity.Handle}")


if __name__ == "__main__":
    main()
