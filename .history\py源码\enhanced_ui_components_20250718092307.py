"""
增强的UI组件模块
提供改进的用户界面和交互体验
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QProgressBar, QTextEdit, QListWidget, QListWidgetItem,
                             QComboBox, QPushButton, QGroupBox, QFormLayout,
                             QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QFrame, QScrollArea,
                             QCheckBox, QSpinBox, QSlider, QLineEdit, QMenu,
                             QAbstractItemView, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QIcon
import shutil
import win32clipboard
from send2trash import send2trash


class SearchHistoryManager:
    """搜索历史管理器"""
    
    def __init__(self, history_file: str = "search_history.json"):
        self.history_file = history_file
        self.max_history = 100
        self.history = self.load_history()
    
    def load_history(self) -> List[Dict]:
        """加载搜索历史"""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return []
        return []
    
    def save_history(self):
        """保存搜索历史"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history[-self.max_history:], f, 
                         ensure_ascii=False, indent=2)
        except IOError:
            pass
    
    def add_search(self, query: str, path: str, results_count: int,
                   search_time: float, extensions: List[str]):
        """添加搜索记录"""
        if not query.strip() or not path.strip():
            return

        record = {
            'query': query.strip(),
            'path': path.strip(),
            'results_count': int(results_count),
            'search_time': float(search_time),
            'extensions': list(extensions) if extensions else [],
            'timestamp': datetime.now().isoformat()
        }

        # 避免重复记录 - 检查最近的几条记录
        for i in range(len(self.history) - 1, max(-1, len(self.history) - 5), -1):
            existing = self.history[i]
            if (existing.get('query', '') == record['query'] and
                existing.get('path', '') == record['path'] and
                existing.get('extensions', []) == record['extensions']):
                # 更新现有记录的时间戳和结果数
                self.history[i] = record
                self.save_history()
                return

        self.history.append(record)

        # 限制历史记录数量
        if len(self.history) > self.max_history:
            self.history = self.history[-self.max_history:]

        self.save_history()
    
    def get_recent_queries(self, limit: int = 10) -> List[str]:
        """获取最近的查询"""
        recent = []
        seen = set()
        
        for record in reversed(self.history):
            query = record['query']
            if query not in seen:
                recent.append(query)
                seen.add(query)
                if len(recent) >= limit:
                    break
        
        return recent
    
    def get_recent_paths(self, limit: int = 10) -> List[str]:
        """获取最近的搜索路径"""
        recent = []
        seen = set()
        
        for record in reversed(self.history):
            path = record['path']
            if path not in seen:
                recent.append(path)
                seen.add(path)
                if len(recent) >= limit:
                    break
        
        return recent
    
    def get_statistics(self) -> Dict:
        """获取搜索统计信息"""
        if not self.history:
            return {}
        
        total_searches = len(self.history)
        total_results = sum(record['results_count'] for record in self.history)
        avg_results = total_results / total_searches if total_searches > 0 else 0
        
        # 最常用的查询
        query_counts = {}
        for record in self.history:
            query = record['query']
            query_counts[query] = query_counts.get(query, 0) + 1
        
        most_common_query = max(query_counts.items(), key=lambda x: x[1]) if query_counts else ("", 0)
        
        return {
            'total_searches': total_searches,
            'total_results': total_results,
            'average_results': avg_results,
            'most_common_query': most_common_query[0],
            'most_common_count': most_common_query[1]
        }


class EnhancedProgressWidget(QWidget):
    """增强的进度显示组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.start_time = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_elapsed_time)
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 主进度条
        self.main_progress = QProgressBar()
        self.main_progress.setVisible(False)
        layout.addWidget(self.main_progress)
        
        # 状态信息
        info_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        info_layout.addWidget(self.status_label)
        
        info_layout.addStretch()
        
        self.time_label = QLabel("")
        self.time_label.setStyleSheet("color: #666; font-size: 12px;")
        info_layout.addWidget(self.time_label)
        
        layout.addLayout(info_layout)
        
        # 详细信息
        self.detail_label = QLabel("")
        self.detail_label.setStyleSheet("color: #888; font-size: 11px;")
        self.detail_label.setWordWrap(True)
        layout.addWidget(self.detail_label)
        
        self.setLayout(layout)
    
    def start_progress(self, message: str = "开始搜索..."):
        """开始进度显示"""
        self.start_time = datetime.now()
        self.main_progress.setVisible(True)
        self.main_progress.setRange(0, 0)  # 不确定进度
        self.status_label.setText(message)
        self.timer.start(100)  # 每100ms更新一次
    
    def update_progress(self, message: str, current: int = 0, total: int = 0):
        """更新进度"""
        self.status_label.setText(message)
        
        if total > 0:
            self.main_progress.setRange(0, total)
            self.main_progress.setValue(current)
            percentage = (current / total) * 100
            self.detail_label.setText(f"进度: {current}/{total} ({percentage:.1f}%)")
        else:
            self.detail_label.setText(message)
    
    def finish_progress(self, message: str = "搜索完成"):
        """完成进度显示"""
        self.timer.stop()
        self.main_progress.setVisible(False)
        self.status_label.setText(message)
        
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            self.time_label.setText(f"耗时: {elapsed.total_seconds():.2f}秒")
    
    def update_elapsed_time(self):
        """更新已用时间"""
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            self.time_label.setText(f"已用时间: {elapsed.total_seconds():.1f}秒")


class SearchHistoryWidget(QWidget):
    """搜索历史组件"""
    
    history_selected = pyqtSignal(str, str, list)  # query, path, extensions
    
    def __init__(self, history_manager: SearchHistoryManager):
        super().__init__()
        self.history_manager = history_manager
        self.init_ui()
        self.refresh_history()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("搜索历史")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 历史列表
        self.history_list = QListWidget()
        self.history_list.itemDoubleClicked.connect(self.on_history_selected)
        layout.addWidget(self.history_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("清空历史")
        self.clear_btn.clicked.connect(self.clear_history)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_history)
        button_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def refresh_history(self):
        """刷新历史列表"""
        self.history_list.clear()

        if not self.history_manager.history:
            # 如果没有历史记录，显示提示
            item = QListWidgetItem("暂无搜索历史")
            item.setData(Qt.UserRole, None)
            self.history_list.addItem(item)
            return

        # 显示最近20条记录，按时间倒序
        recent_history = self.history_manager.history[-20:]
        for record in reversed(recent_history):
            try:
                timestamp = datetime.fromisoformat(record['timestamp'])
                time_str = timestamp.strftime("%m-%d %H:%M")

                # 处理查询文本显示
                query_lines = record['query'].split('\n')
                display_query = query_lines[0][:25]  # 只显示第一行的前25个字符
                if len(query_lines) > 1:
                    display_query += f"... (+{len(query_lines)-1}行)"
                elif len(record['query']) > 25:
                    display_query += "..."

                # 格式化显示文本
                item_text = f"[{time_str}] {display_query}"
                item_text += f" ({record.get('results_count', 0)}个结果)"

                # 添加路径信息（简化显示）
                path = record.get('path', '')
                if path:
                    path_name = os.path.basename(path) or path
                    if len(path_name) > 15:
                        path_name = path_name[:12] + "..."
                    item_text += f" - {path_name}"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, record)

                # 设置工具提示显示完整信息
                tooltip = f"查询: {record['query']}\n路径: {record.get('path', '')}\n"
                tooltip += f"结果: {record.get('results_count', 0)}个\n"
                tooltip += f"耗时: {record.get('search_time', 0):.2f}秒\n"
                tooltip += f"时间: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
                item.setToolTip(tooltip)

                self.history_list.addItem(item)

            except Exception as e:
                # 如果某条记录有问题，跳过它
                continue
    
    def on_history_selected(self, item):
        """历史项被选中"""
        record = item.data(Qt.UserRole)
        if record and isinstance(record, dict):
            # 确保数据完整性
            query = record.get('query', '')
            path = record.get('path', '')
            extensions = record.get('extensions', [])

            if query and path:
                self.history_selected.emit(query, path, extensions)
    
    def clear_history(self):
        """清空历史"""
        self.history_manager.history.clear()
        self.history_manager.save_history()
        self.refresh_history()


class SearchStatisticsWidget(QWidget):
    """搜索统计组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.reset_statistics()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("搜索统计")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 统计表格
        self.stats_table = QTableWidget(0, 2)
        self.stats_table.setHorizontalHeaderLabels(["项目", "值"])
        self.stats_table.horizontalHeader().setStretchLastSection(True)
        self.stats_table.verticalHeader().setVisible(False)
        self.stats_table.setAlternatingRowColors(True)
        layout.addWidget(self.stats_table)
        
        self.setLayout(layout)
    
    def update_statistics(self, stats: Dict):
        """更新统计信息"""
        self.stats_table.setRowCount(0)

        if not stats:
            # 如果没有统计数据，显示提示
            self.stats_table.insertRow(0)
            self.stats_table.setItem(0, 0, QTableWidgetItem("暂无统计数据"))
            self.stats_table.setItem(0, 1, QTableWidgetItem(""))
            return

        # 按特定顺序显示统计信息
        display_order = [
            'total_matches', 'unique_files', 'total_size', 'average_size',
            'search_time', 'pattern_stats'
        ]

        # 先显示有序的统计项
        for key in display_order:
            if key in stats:
                self._add_stat_row(key, stats[key])

        # 再显示其他统计项
        for key, value in stats.items():
            if key not in display_order:
                self._add_stat_row(key, value)

        # 调整列宽
        self.stats_table.resizeColumnsToContents()

    def _add_stat_row(self, key: str, value):
        """添加统计行"""
        row = self.stats_table.rowCount()
        self.stats_table.insertRow(row)

        # 格式化显示名称
        display_name = self._format_stat_name(key)
        self.stats_table.setItem(row, 0, QTableWidgetItem(display_name))

        # 格式化值
        display_value = self._format_stat_value(key, value)
        self.stats_table.setItem(row, 1, QTableWidgetItem(display_value))
    
    def _format_stat_name(self, key: str) -> str:
        """格式化统计项名称"""
        name_map = {
            'total_matches': '匹配文件数',
            'total_size': '文件总大小',
            'average_size': '平均文件大小',
            'unique_files': '唯一文件数',
            'search_time': '搜索耗时',
            'pattern_stats': '模式统计'
        }
        return name_map.get(key, key)
    
    def _format_stat_value(self, key: str, value) -> str:
        """格式化统计值"""
        try:
            if key in ['total_size', 'average_size']:
                return self._format_file_size(int(value) if value else 0)
            elif key == 'search_time':
                return f"{float(value):.2f}秒"
            elif key == 'pattern_stats' and isinstance(value, dict):
                # 显示模式统计的详细信息
                if not value:
                    return "无匹配模式"
                total = sum(value.values())
                return f"{len(value)}个模式, 共{total}个匹配"
            elif isinstance(value, dict):
                return f"{len(value)}项"
            elif isinstance(value, (int, float)):
                if key in ['total_matches', 'unique_files']:
                    return f"{int(value)}个"
                else:
                    return str(value)
            else:
                return str(value) if value is not None else "0"
        except Exception:
            return str(value) if value is not None else "0"
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats_table.setRowCount(0)


class EnhancedResultWidget(QWidget):
    """增强的结果显示组件"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.results_data = []
        self.all_results_data = []  # 存储所有搜索结果（用于筛选）
        self.file_paths = {}  # 存储显示文本到文件路径的映射
        self.color_matching_enabled = True
        self.filtered_keywords = []  # 当前筛选的关键词
        self.dxf_preview_program = ""  # DXF预览程序路径

        # 导入色彩管理器
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from color_manager import ColorMatchingManager
        self.color_manager = ColorMatchingManager()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 结果工具栏
        toolbar_layout = QHBoxLayout()
        
        self.view_mode_combo = QComboBox()
        self.view_mode_combo.addItems(["列表视图", "详细视图", "图标视图"])
        self.view_mode_combo.currentTextChanged.connect(self.change_view_mode)
        toolbar_layout.addWidget(QLabel("视图:"))
        toolbar_layout.addWidget(self.view_mode_combo)
        
        toolbar_layout.addStretch()
        
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["按名称", "按大小", "按修改时间", "按路径"])
        toolbar_layout.addWidget(QLabel("排序:"))
        toolbar_layout.addWidget(self.sort_combo)
        
        layout.addLayout(toolbar_layout)
        
        # 结果显示区域
        self.result_stack = QTabWidget()
        
        # 列表视图
        self.list_widget = QListWidget()
        self.list_widget.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.list_widget.customContextMenuRequested.connect(self.show_context_menu)
        self.list_widget.itemDoubleClicked.connect(self.open_file)
        self.result_stack.addTab(self.list_widget, "列表")

        # 详细视图
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(4)
        self.table_widget.setHorizontalHeaderLabels(["文件名", "大小", "修改时间", "路径"])
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.show_table_context_menu)
        self.table_widget.itemDoubleClicked.connect(self.open_file_from_table)
        self.result_stack.addTab(self.table_widget, "详细")
        
        layout.addWidget(self.result_stack)
        
        self.setLayout(layout)

    def set_color_matching_enabled(self, enabled: bool):
        """设置色彩匹配是否启用"""
        self.color_matching_enabled = enabled
        self.color_manager.set_enabled(enabled)
        # 刷新显示
        if hasattr(self, 'results_data'):
            self.update_results(self.results_data)

    def update_search_keywords(self, keywords: List[str]):
        """更新搜索关键词，用于色彩匹配"""
        if self.color_matching_enabled:
            self.color_manager.update_keywords(keywords)

    def set_dxf_preview_program(self, program_path: str):
        """设置DXF预览程序路径"""
        self.dxf_preview_program = program_path

    def filter_by_keywords(self, keywords: List[str]):
        """根据关键词筛选结果"""
        self.filtered_keywords = keywords

        if not keywords:
            # 显示全部结果
            self.results_data = self.all_results_data.copy()
        else:
            # 筛选匹配指定关键词的结果
            self.results_data = []
            for result in self.all_results_data:
                matched_patterns = result.get('matched_patterns', [])
                # 检查是否匹配任意一个筛选关键词
                if any(keyword in matched_patterns for keyword in keywords):
                    self.results_data.append(result)

        # 刷新显示
        self._update_list_view()
        self._update_table_view()
    
    def change_view_mode(self, mode: str):
        """切换视图模式"""
        if mode == "列表视图":
            self.result_stack.setCurrentIndex(0)
        elif mode == "详细视图":
            self.result_stack.setCurrentIndex(1)
    
    def update_results(self, results: List[Dict]):
        """更新结果显示"""
        self.all_results_data = results.copy()  # 保存所有结果

        # 如果有筛选条件，应用筛选
        if self.filtered_keywords:
            self.filter_by_keywords(self.filtered_keywords)
        else:
            self.results_data = results

        self.file_paths.clear()
        self._update_list_view()
        self._update_table_view()

    def _update_list_view(self):
        """更新列表视图"""
        self.list_widget.clear()

        for result in self.results_data:
            # 创建显示文本
            matched_patterns = result.get('matched_patterns', [])
            pattern_text = f" [关键词: {', '.join(matched_patterns)}]" if matched_patterns else ""
            item_text = f"{result['filename']} (路径: {result['relative_path']}){pattern_text}"

            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, result)

            # 应用色彩匹配
            if self.color_matching_enabled and matched_patterns:
                background_color = self.color_manager.get_background_color(matched_patterns)
                text_color = self.color_manager.get_text_color(matched_patterns)
                item.setBackground(background_color)
                item.setForeground(text_color)

            # 存储文件路径映射
            self.file_paths[item_text] = result['file_path']

            self.list_widget.addItem(item)
    
    def _update_table_view(self):
        """更新表格视图"""
        self.table_widget.setRowCount(len(self.results_data))

        for row, result in enumerate(self.results_data):
            matched_patterns = result.get('matched_patterns', [])

            # 文件名
            filename_item = QTableWidgetItem(result['filename'])
            self.table_widget.setItem(row, 0, filename_item)

            # 文件大小
            size_str = self._format_file_size(result.get('file_size', 0))
            size_item = QTableWidgetItem(size_str)
            self.table_widget.setItem(row, 1, size_item)

            # 修改时间
            if result.get('modified_time'):
                time_str = datetime.fromtimestamp(result['modified_time']).strftime("%Y-%m-%d %H:%M")
                time_item = QTableWidgetItem(time_str)
            else:
                time_item = QTableWidgetItem("")
            self.table_widget.setItem(row, 2, time_item)

            # 路径
            path_item = QTableWidgetItem(result['relative_path'])
            self.table_widget.setItem(row, 3, path_item)

            # 应用色彩匹配到整行
            if self.color_matching_enabled and matched_patterns:
                background_color = self.color_manager.get_background_color(matched_patterns)
                text_color = self.color_manager.get_text_color(matched_patterns)

                for col in range(4):  # 4列
                    item = self.table_widget.item(row, col)
                    if item:
                        item.setBackground(background_color)
                        item.setForeground(text_color)

        # 调整列宽
        self.table_widget.resizeColumnsToContents()
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def show_context_menu(self, position):
        """显示列表视图的右键菜单"""
        item = self.list_widget.itemAt(position)
        if item is None:
            return

        menu = QMenu()

        open_action = menu.addAction("打开文件")
        open_action.triggered.connect(self.open_file)

        open_folder_action = menu.addAction("打开文件夹")
        open_folder_action.triggered.connect(self.open_folder)

        # 检查是否为DXF/DWG文件，添加预览选项
        file_path = self.file_paths.get(item.text(), "")
        if self._is_dxf_file(file_path):
            preview_action = menu.addAction("预览DXF")
            preview_action.triggered.connect(self.preview_dxf_files)

        copy_path_action = menu.addAction("复制路径")
        copy_path_action.triggered.connect(self.copy_path_to_clipboard)

        menu.addSeparator()

        delete_action = menu.addAction("删除文件")
        delete_action.triggered.connect(self.delete_files)

        menu.exec_(self.list_widget.mapToGlobal(position))

    def show_table_context_menu(self, position):
        """显示表格视图的右键菜单"""
        if self.table_widget.itemAt(position) is None:
            return

        menu = QMenu()

        open_action = menu.addAction("打开文件")
        open_action.triggered.connect(self.open_file_from_table)

        open_folder_action = menu.addAction("打开文件夹")
        open_folder_action.triggered.connect(self.open_folder_from_table)

        # 检查是否为DXF/DWG文件，添加预览选项
        current_row = self.table_widget.currentRow()
        if current_row >= 0 and current_row < len(self.results_data):
            file_path = self.results_data[current_row]['file_path']
            if self._is_dxf_file(file_path):
                preview_action = menu.addAction("预览DXF")
                preview_action.triggered.connect(self.preview_dxf_files_from_table)

        copy_path_action = menu.addAction("复制路径")
        copy_path_action.triggered.connect(self.copy_path_from_table)

        menu.addSeparator()

        delete_action = menu.addAction("删除文件")
        delete_action.triggered.connect(self.delete_files_from_table)

        menu.exec_(self.table_widget.mapToGlobal(position))

    def open_file(self):
        """从列表视图打开选中的文件"""
        current_item = self.list_widget.currentItem()
        if current_item:
            file_path = self.file_paths.get(current_item.text())
            if file_path and os.path.exists(file_path):
                os.startfile(file_path)
            else:
                QMessageBox.warning(self, "警告", "文件不存在或路径无效")

    def open_file_from_table(self):
        """从表格视图打开选中的文件"""
        current_row = self.table_widget.currentRow()
        if current_row >= 0 and current_row < len(self.results_data):
            file_path = self.results_data[current_row]['file_path']
            if file_path and os.path.exists(file_path):
                os.startfile(file_path)
            else:
                QMessageBox.warning(self, "警告", "文件不存在或路径无效")

    def open_folder(self):
        """从列表视图打开文件所在文件夹"""
        current_item = self.list_widget.currentItem()
        if current_item:
            file_path = self.file_paths.get(current_item.text())
            if file_path and os.path.exists(file_path):
                folder_path = os.path.dirname(file_path)
                os.startfile(folder_path)
            else:
                QMessageBox.warning(self, "警告", "文件不存在或路径无效")

    def open_folder_from_table(self):
        """从表格视图打开文件所在文件夹"""
        current_row = self.table_widget.currentRow()
        if current_row >= 0 and current_row < len(self.results_data):
            file_path = self.results_data[current_row]['file_path']
            if file_path and os.path.exists(file_path):
                folder_path = os.path.dirname(file_path)
                os.startfile(folder_path)
            else:
                QMessageBox.warning(self, "警告", "文件不存在或路径无效")

    def copy_path_to_clipboard(self):
        """从列表视图复制文件路径到剪贴板"""
        current_item = self.list_widget.currentItem()
        if current_item:
            file_path = self.file_paths.get(current_item.text())
            if file_path:
                try:
                    win32clipboard.OpenClipboard()
                    win32clipboard.EmptyClipboard()
                    win32clipboard.SetClipboardText(file_path)
                    win32clipboard.CloseClipboard()
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"复制路径失败: {str(e)}")

    def copy_path_from_table(self):
        """从表格视图复制文件路径到剪贴板"""
        current_row = self.table_widget.currentRow()
        if current_row >= 0 and current_row < len(self.results_data):
            file_path = self.results_data[current_row]['file_path']
            if file_path:
                try:
                    win32clipboard.OpenClipboard()
                    win32clipboard.EmptyClipboard()
                    win32clipboard.SetClipboardText(file_path)
                    win32clipboard.CloseClipboard()
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"复制路径失败: {str(e)}")

    def delete_files(self):
        """从列表视图删除选中的文件"""
        selected_items = self.list_widget.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return

        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除选中的 {len(selected_items)} 个文件吗？\n文件将被移动到回收站。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            deleted_count = 0
            for item in selected_items:
                file_path = self.file_paths.get(item.text())
                if file_path and os.path.exists(file_path):
                    try:
                        send2trash(file_path)
                        deleted_count += 1
                        # 从列表中移除
                        row = self.list_widget.row(item)
                        self.list_widget.takeItem(row)
                        del self.file_paths[item.text()]
                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"删除文件 {file_path} 失败: {str(e)}")

            if deleted_count > 0:
                QMessageBox.information(self, "删除完成", f"成功删除 {deleted_count} 个文件")

    def delete_files_from_table(self):
        """从表格视图删除选中的文件"""
        selected_rows = set()
        for item in self.table_widget.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return

        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除选中的 {len(selected_rows)} 个文件吗？\n文件将被移动到回收站。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            deleted_count = 0
            # 按行号倒序删除，避免索引问题
            for row in sorted(selected_rows, reverse=True):
                if row < len(self.results_data):
                    file_path = self.results_data[row]['file_path']
                    if file_path and os.path.exists(file_path):
                        try:
                            send2trash(file_path)
                            deleted_count += 1
                            # 从表格和数据中移除
                            self.table_widget.removeRow(row)
                            del self.results_data[row]
                        except Exception as e:
                            QMessageBox.critical(self, "错误", f"删除文件 {file_path} 失败: {str(e)}")

            if deleted_count > 0:
                QMessageBox.information(self, "删除完成", f"成功删除 {deleted_count} 个文件")

    def _is_dxf_file(self, file_path: str) -> bool:
        """检查是否为DXF/DWG文件"""
        if not file_path:
            return False
        _, ext = os.path.splitext(file_path.lower())
        return ext in ['.dxf', '.dwg']

    def preview_dxf_files(self):
        """从列表视图预览选中的DXF文件"""
        selected_items = self.list_widget.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要预览的DXF文件")
            return

        dxf_files = []
        for item in selected_items:
            file_path = self.file_paths.get(item.text())
            if file_path and self._is_dxf_file(file_path):
                dxf_files.append(file_path)

        if not dxf_files:
            QMessageBox.information(self, "提示", "选中的文件中没有DXF/DWG文件")
            return

        self._launch_dxf_preview(dxf_files)

    def preview_dxf_files_from_table(self):
        """从表格视图预览选中的DXF文件"""
        selected_rows = set()
        for item in self.table_widget.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.information(self, "提示", "请先选择要预览的DXF文件")
            return

        dxf_files = []
        for row in selected_rows:
            if row < len(self.results_data):
                file_path = self.results_data[row]['file_path']
                if file_path and self._is_dxf_file(file_path):
                    dxf_files.append(file_path)

        if not dxf_files:
            QMessageBox.information(self, "提示", "选中的文件中没有DXF/DWG文件")
            return

        self._launch_dxf_preview(dxf_files)

    def _launch_dxf_preview(self, file_paths: List[str]):
        """使用拖拽方式预览DXF文件"""
        if not self.dxf_preview_program:
            QMessageBox.critical(self, "错误", "未配置DXF预览程序路径\n请在设置中配置AcmeCAD程序路径")
            return

        if not os.path.exists(self.dxf_preview_program):
            QMessageBox.critical(self, "错误", f"DXF预览程序不存在:\n{self.dxf_preview_program}")
            return

        # 导入拖拽工具
        try:
            import sys
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            from drag_drop_utils import DragDropUtils
        except ImportError as e:
            QMessageBox.critical(self, "错误", f"导入拖拽工具失败:\n{str(e)}\n将使用传统方式启动程序")
            self._launch_dxf_preview_traditional(file_paths)
            return

        # 过滤存在的文件
        valid_files = []
        failed_files = []

        for file_path in file_paths:
            if os.path.exists(file_path):
                valid_files.append(file_path)
            else:
                failed_files.append(f"{os.path.basename(file_path)} - 文件不存在")

        if not valid_files:
            QMessageBox.critical(self, "错误", "没有有效的文件可以预览")
            return

        # 使用拖拽方式预览文件
        try:
            success, message = DragDropUtils.drag_files_to_program(
                self.dxf_preview_program,
                valid_files,
                "Acme CAD"
            )

            if success:
                result_message = f"成功拖拽 {len(valid_files)} 个文件到AcmeCAD程序"
                if failed_files:
                    result_message += f"\n失败: {len(failed_files)} 个文件"
                    if len(failed_files) <= 3:
                        result_message += "\n失败文件:\n" + "\n".join(failed_files)
                    else:
                        result_message += f"\n失败文件:\n" + "\n".join(failed_files[:3]) + f"\n... 还有 {len(failed_files)-3} 个"

                QMessageBox.information(self, "拖拽预览完成", result_message)
            else:
                # 如果拖拽失败，尝试传统方式
                QMessageBox.warning(self, "拖拽失败", f"拖拽方式失败: {message}\n将尝试传统方式启动程序")
                self._launch_dxf_preview_traditional(valid_files)

        except Exception as e:
            QMessageBox.critical(self, "预览失败", f"拖拽预览失败: {str(e)}\n将尝试传统方式启动程序")
            self._launch_dxf_preview_traditional(valid_files)

    def _launch_dxf_preview_traditional(self, file_paths: List[str]):
        """传统方式启动DXF预览程序（备用方案）"""
        success_count = 0
        failed_files = []

        for file_path in file_paths:
            try:
                if not os.path.exists(file_path):
                    failed_files.append(f"{os.path.basename(file_path)} - 文件不存在")
                    continue

                # 启动预览程序
                import subprocess
                subprocess.Popen([self.dxf_preview_program, file_path])
                success_count += 1

            except Exception as e:
                failed_files.append(f"{os.path.basename(file_path)} - {str(e)}")

        # 显示结果
        if success_count > 0:
            message = f"成功启动预览: {success_count} 个文件"
            if failed_files:
                message += f"\n失败: {len(failed_files)} 个文件"
                if len(failed_files) <= 3:
                    message += "\n失败文件:\n" + "\n".join(failed_files)
                else:
                    message += f"\n失败文件:\n" + "\n".join(failed_files[:3]) + f"\n... 还有 {len(failed_files)-3} 个"

            QMessageBox.information(self, "预览启动完成", message)
        else:
            error_message = "所有文件预览启动失败"
            if failed_files:
                error_message += "\n错误详情:\n" + "\n".join(failed_files[:5])
            QMessageBox.critical(self, "预览失败", error_message)


class IntegratedResultWidget(QWidget):
    """集成的搜索结果组件（包含色彩图例）"""

    def __init__(self):
        super().__init__()
        self.init_ui()

        # 创建结果组件
        self.result_widget = EnhancedResultWidget()

        # 创建图例组件
        self.legend_widget = ColorLegendWidget()
        self.legend_widget.set_color_manager(self.result_widget.color_manager)
        self.legend_widget.keyword_filter_changed.connect(self.result_widget.filter_by_keywords)

        # 设置布局
        self.setup_layout()

    def init_ui(self):
        """初始化UI"""
        self.setMinimumSize(800, 600)

    def setup_layout(self):
        """设置布局"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(3, 3, 3, 3)
        main_layout.setSpacing(4)

        # 创建垂直分割器
        splitter = QSplitter()
        splitter.setOrientation(2)  # 垂直分割 (Qt.Vertical = 2)
        splitter.setHandleWidth(3)

        # 上部：色彩图例（更紧凑的显示）
        legend_frame = QFrame()
        legend_frame.setFrameStyle(QFrame.Box)
        legend_frame.setLineWidth(1)
        legend_frame.setMaximumHeight(110)  # 稍微增加高度确保完全显示
        legend_frame.setMinimumHeight(90)   # 增加最小高度
        legend_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #d1d1d1;
                border-radius: 4px;
                margin: 2px;
            }
        """)

        legend_layout = QVBoxLayout()
        legend_layout.setContentsMargins(6, 4, 6, 4)
        legend_layout.setSpacing(2)

        # 图例标题 - 适应白色主题
        legend_title = QLabel("🎨 色彩图例筛选")
        legend_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333333;
                font-size: 10px;
                padding: 2px 0;
                background: transparent;
                border: none;
            }
        """)
        legend_layout.addWidget(legend_title)

        # 图例内容
        legend_layout.addWidget(self.legend_widget)
        legend_frame.setLayout(legend_layout)

        # 下部：搜索结果
        result_frame = QFrame()
        result_frame.setFrameStyle(QFrame.Box)
        result_frame.setLineWidth(1)
        result_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                margin: 2px;
            }
        """)

        result_layout = QVBoxLayout()
        result_layout.setContentsMargins(6, 4, 6, 6)
        result_layout.setSpacing(4)

        # 结果标题 - 更小的字体
        result_title = QLabel("📋 搜索结果")
        result_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #495057;
                font-size: 10px;
                padding: 2px 0;
                background: transparent;
                border: none;
            }
        """)
        result_layout.addWidget(result_title)

        # 结果内容
        result_layout.addWidget(self.result_widget)
        result_frame.setLayout(result_layout)

        # 添加到分割器
        splitter.addWidget(legend_frame)
        splitter.addWidget(result_frame)

        # 优化分割比例（图例占15%，结果占85%）
        splitter.setSizes([90, 510])
        splitter.setStretchFactor(0, 0)  # 图例不拉伸
        splitter.setStretchFactor(1, 1)  # 结果区域可拉伸

        # 设置分割器样式
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #dee2e6;
                border-radius: 2px;
                margin: 1px 2px;
            }
            QSplitter::handle:hover {
                background-color: #3498db;
            }
        """)

        main_layout.addWidget(splitter)
        self.setLayout(main_layout)

    def update_results(self, results):
        """更新搜索结果"""
        self.result_widget.update_results(results)

    def update_search_keywords(self, keywords):
        """更新搜索关键词"""
        self.result_widget.update_search_keywords(keywords)
        self.legend_widget.update_legend(keywords)

    def set_dxf_preview_program(self, program_path):
        """设置DXF预览程序路径"""
        self.result_widget.set_dxf_preview_program(program_path)

    def get_selected_files(self):
        """获取选中的文件"""
        return self.result_widget.get_selected_files()

    def set_color_matching_enabled(self, enabled):
        """设置色彩匹配是否启用"""
        self.result_widget.set_color_matching_enabled(enabled)


class ColorLegendWidget(QWidget):
    """色彩图例组件"""

    # 定义信号
    keyword_filter_changed = pyqtSignal(list)  # 关键词筛选改变信号

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.color_manager = None
        self.selected_keywords = set()  # 选中的关键词
        self.legend_items = {}  # 存储图例项组件

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(3, 3, 3, 3)
        layout.setSpacing(3)

        # 控制栏 - 更紧凑
        control_layout = QHBoxLayout()
        control_layout.setSpacing(6)

        # 状态提示 - 更小的字体
        self.status_label = QLabel("显示全部结果")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 9px;
                font-style: italic;
                background: transparent;
                border: none;
                padding: 0;
            }
        """)
        control_layout.addWidget(self.status_label)

        control_layout.addStretch()

        # 显示全部按钮 - 更小更紧凑
        self.show_all_btn = QPushButton("显示全部")
        self.show_all_btn.setMaximumWidth(50)
        self.show_all_btn.setMaximumHeight(20)
        self.show_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 8px;
                font-weight: bold;
                padding: 2px 6px;
            }
            QPushButton:hover {
                background-color: #495057;
            }
            QPushButton:pressed {
                background-color: #343a40;
            }
        """)
        self.show_all_btn.clicked.connect(self.show_all_results)
        control_layout.addWidget(self.show_all_btn)

        layout.addLayout(control_layout)

        # 图例容器 - 更紧凑的水平滚动布局
        self.legend_scroll = QScrollArea()
        self.legend_scroll.setHorizontalScrollBarPolicy(1)  # Qt.ScrollBarAsNeeded
        self.legend_scroll.setVerticalScrollBarPolicy(0)    # Qt.ScrollBarAlwaysOff
        self.legend_scroll.setMaximumHeight(45)  # 进一步减少高度
        self.legend_scroll.setMinimumHeight(35)  # 设置最小高度
        self.legend_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background-color: #ffffff;
            }
            QScrollBar:horizontal {
                height: 8px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
            QScrollBar::handle:horizontal {
                background-color: #ced4da;
                border-radius: 4px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #adb5bd;
            }
        """)

        self.legend_widget = QWidget()
        self.legend_layout = QHBoxLayout()  # 水平布局
        self.legend_layout.setContentsMargins(4, 2, 4, 2)
        self.legend_layout.setSpacing(6)
        self.legend_widget.setLayout(self.legend_layout)
        self.legend_scroll.setWidget(self.legend_widget)
        self.legend_scroll.setWidgetResizable(True)

        layout.addWidget(self.legend_scroll)
        self.setLayout(layout)

    def set_color_manager(self, color_manager):
        """设置色彩管理器"""
        self.color_manager = color_manager

    def update_legend(self, keywords: List[str] = None):
        """更新色彩图例"""
        # 清空现有图例
        for i in reversed(range(self.legend_layout.count())):
            child = self.legend_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        self.legend_items.clear()

        if not self.color_manager or not self.color_manager.is_enabled():
            # 显示禁用提示
            disabled_label = QLabel("色彩匹配已禁用")
            disabled_label.setStyleSheet("color: #666; font-style: italic;")
            self.legend_layout.addWidget(disabled_label)
            self._update_status_label()
            return

        # 获取色彩图例
        color_legend = self.color_manager.get_color_legend()

        if not color_legend:
            # 显示无数据提示
            no_data_label = QLabel("暂无搜索关键词")
            no_data_label.setStyleSheet("color: #666; font-style: italic;")
            self.legend_layout.addWidget(no_data_label)
            self._update_status_label()
            return

        # 显示每个关键词的颜色
        for keyword, color in color_legend.items():
            legend_item = self.create_legend_item(keyword, color)
            self.legend_layout.addWidget(legend_item)
            self.legend_items[keyword] = legend_item

        self.legend_layout.addStretch()
        self._update_status_label()

    def create_legend_item(self, keyword: str, color: QColor) -> QWidget:
        """创建可点击的图例项"""
        item_widget = ClickableLegendItem(keyword, color)
        item_widget.clicked.connect(self.on_legend_item_clicked)
        return item_widget

    def on_legend_item_clicked(self, keyword: str, ctrl_pressed: bool):
        """处理图例项点击事件"""
        if ctrl_pressed:
            # Ctrl+点击：多选模式
            if keyword in self.selected_keywords:
                self.selected_keywords.remove(keyword)
            else:
                self.selected_keywords.add(keyword)
        else:
            # 普通点击：单选模式
            if len(self.selected_keywords) == 1 and keyword in self.selected_keywords:
                # 如果点击的是唯一选中的项，则取消选择
                self.selected_keywords.clear()
            else:
                self.selected_keywords = {keyword}

        # 更新图例项的视觉状态
        self._update_legend_items_visual_state()

        # 更新状态标签
        self._update_status_label()

        # 发送筛选信号
        self.keyword_filter_changed.emit(list(self.selected_keywords))

    def show_all_results(self):
        """显示全部结果"""
        self.selected_keywords.clear()
        self._update_legend_items_visual_state()
        self._update_status_label()
        self.keyword_filter_changed.emit([])

    def _update_legend_items_visual_state(self):
        """更新图例项的视觉状态"""
        for keyword, item_widget in self.legend_items.items():
            is_selected = keyword in self.selected_keywords
            item_widget.set_selected(is_selected)

    def _update_status_label(self):
        """更新状态标签"""
        if not self.selected_keywords:
            self.status_label.setText("显示全部结果")
        elif len(self.selected_keywords) == 1:
            keyword = list(self.selected_keywords)[0]
            self.status_label.setText(f"筛选显示: {keyword}")
        else:
            self.status_label.setText(f"筛选显示: {len(self.selected_keywords)}个关键词")


class ClickableLegendItem(QWidget):
    """可点击的图例项组件"""

    clicked = pyqtSignal(str, bool)  # keyword, ctrl_pressed

    def __init__(self, keyword: str, color: QColor):
        super().__init__()
        self.keyword = keyword
        self.color = color
        self.selected = False
        self.init_ui()

    def init_ui(self):
        self.setFixedHeight(20)  # 减少高度
        self.setCursor(Qt.PointingHandCursor)

        layout = QHBoxLayout()
        layout.setContentsMargins(3, 1, 3, 1)  # 减少边距
        layout.setSpacing(4)  # 减少间距

        # 颜色方块 - 更小
        self.color_label = QLabel()
        self.color_label.setFixedSize(12, 12)  # 减少尺寸
        layout.addWidget(self.color_label)

        # 关键词文本 - 更小的字体
        self.text_label = QLabel(self.keyword)
        self.text_label.setStyleSheet("""
            QLabel {
                font-size: 9px;
                font-weight: bold;
                color: #495057;
                background: transparent;
                border: none;
                padding: 0;
            }
        """)
        # 限制文本长度，避免过长
        if len(self.keyword) > 12:
            self.text_label.setText(self.keyword[:10] + "...")
            self.text_label.setToolTip(self.keyword)

        layout.addWidget(self.text_label)

        self.setLayout(layout)

        # 初始化样式
        self.update_style()

    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        self.update_style()

    def update_style(self):
        """更新样式"""
        if self.selected:
            # 选中状态：高亮边框和背景
            color_style = f"""
                QLabel {{
                    background-color: {self.color.name()};
                    border: 2px solid #3498db;
                    border-radius: 2px;
                }}
            """
            widget_style = """
                ClickableLegendItem {
                    background-color: rgba(52, 152, 219, 0.15);
                    border: 1px solid #3498db;
                    border-radius: 4px;
                }
            """
        else:
            # 未选中状态：普通边框
            color_style = f"""
                QLabel {{
                    background-color: {self.color.name()};
                    border: 1px solid #dee2e6;
                    border-radius: 2px;
                }}
            """
            widget_style = """
                ClickableLegendItem {
                    background-color: transparent;
                    border: 1px solid transparent;
                    border-radius: 4px;
                }
                ClickableLegendItem:hover {
                    background-color: rgba(108, 117, 125, 0.1);
                    border: 1px solid #6c757d;
                }
            """

        self.color_label.setStyleSheet(color_style)
        self.setStyleSheet(widget_style)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            ctrl_pressed = event.modifiers() & Qt.ControlModifier
            self.clicked.emit(self.keyword, bool(ctrl_pressed))
        super().mousePressEvent(event)

    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.selected:
            self.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc; border-radius: 3px;")
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)
