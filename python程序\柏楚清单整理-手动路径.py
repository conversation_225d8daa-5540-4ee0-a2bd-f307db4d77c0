import os
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows

# 提示用户原有excel必须和dxf图纸在一个文件夹内
input("1.原有excel、此程序、dxf图纸必须在一个文件夹内\n"
      "2.需要处理的excel的数据必须在第一个工作簿\n"
      "3.必须保证原有excel中表头中有---“厚度”、“零件号”或“零件编号”、“数量”列---\n"
      "4.尤其是厚度列必须有值，否则程序会报错。\n"
      "-----------------------------------------\n"
      "---点击回车才能继续运行---\n")
# 获取用户输入的文件夹路径
folder_path = input("请输入包含Excel文件的文件夹路径：")
# 检查文件夹是否存在
if not os.path.isdir(folder_path):
    print("文件夹路径不存在，请检查路径。")
else:
    # 获取文件夹下所有xlsx和xls文件
    files = [f for f in os.listdir(folder_path) if f.endswith(('.xlsx', '.xls'))]

    print(f"当前工作目录: {os.getcwd()}")
    print(f"输入的文件夹路径: {folder_path}")

    for file in files:
        # 构建绝对文件路径
        file_path = os.path.join(folder_path, file)
        print(f"正在读取文件: {file_path}")

        # 读取文件的第一个工作簿和第一行表头
        df = pd.read_excel(file_path, sheet_name=0)
        header = df.columns.tolist()

        # 如果“厚度”不在表头中，跳过该文件
        if '厚度' not in header:
            continue

        # 查找“零件号”或“零件编号”列
        part_column = None
        if '零件号' in header:
            part_column = '零件号'
        elif '零件编号' in header:
            part_column = '零件编号'

        # 如果“零件号”或“零件编号”列不存在，跳过该文件
        if part_column is None:
            continue

        # 如果“数量”列不存在，跳过该文件
        if '数量' not in header:
            continue

        # 获取当前文件所在的目录路径和文件名（不带扩展名）
        file_base_name = os.path.splitext(file)[0]

        # 根据“厚度”列的值进行分组
        grouped = df.groupby('厚度')

        for thickness, group in grouped:
            # 创建一个新的Excel文件
            new_file_name = f"{file_base_name}-T{thickness}.xlsx"
            new_file_path = os.path.join(folder_path, new_file_name)
            print(f"正在创建文件: {new_file_path}")

            # 创建一个新的工作簿
            workbook = Workbook()

            # 删除默认创建的Sheet
            del workbook[workbook.sheetnames[0]]

            # 创建一个新的工作表命名为Sheet1
            extra_sheet = workbook.create_sheet('PartsDefinition')

            # 在ExtraSheet中添加表头
            extra_sheet['A1'] = 'PartName'
            extra_sheet['B1'] = 'Amount'
            extra_sheet['C1'] = 'FilePath'

            # 获取零件号/零件编号和数量列的数据
            part_numbers = group[part_column]
            quantities = group['数量']

            # 填充ExtraSheet中的数据
            for i, (part_number, quantity) in enumerate(zip(part_numbers, quantities), start=2):
                extra_sheet[f'A{i}'] = part_number
                extra_sheet[f'B{i}'] = quantity
                extra_sheet[f'C{i}'] = os.path.join(folder_path, f"{part_number}.dxf")

            # 创建一个新的工作表命名为Sheet2
            sheet1 = workbook.create_sheet('Sheet2')

            # 将分组后的数据写入Sheet1
            for r in dataframe_to_rows(group, index=False, header=True):
                sheet1.append(r)

            # 将ExtraSheet放到Sheet1前面
            workbook.worksheets.insert(0, extra_sheet)

            # 保存并关闭文件
            workbook.save(new_file_path)

        print(f"文件 {file} 处理完毕。")

print("所有文件处理完毕。")
