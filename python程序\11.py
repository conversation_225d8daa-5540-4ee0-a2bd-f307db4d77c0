import os

def check_files(file_names, folder_path):
    # 获取文件夹下的所有文件名，忽略文件后缀
    folder_files = [os.path.splitext(f)[0] for f in os.listdir(folder_path)]
    print(f"文件夹中的文件名（忽略后缀）：{folder_files}")

    # 构建日志文件的完整路径
    log_file_path = os.path.join(folder_path, '未找到的文件名.txt')
    extra_file_path = os.path.join(folder_path, '多余的文件名.txt')

    # 打开日志文件
    with open(log_file_path, 'w', encoding='utf-8') as log_file:
        for name in file_names:
            # 去掉文件后缀
            name_without_ext = os.path.splitext(name)[0]
            print(f"正在检查文件名：{name_without_ext}")
            if name_without_ext not in folder_files:
                # 如果文件未找到，写入日志文件
                print(f"未找到文件名：{name_without_ext}")
                log_file.write(name_without_ext + '\n')

    # 获取多余的文件名
    extra_files = [f for f in folder_files if f not in [os.path.splitext(name)[0] for name in file_names]]
    print(f"多余的文件名：{extra_files}")

    # 将多余的文件名写入单独的TXT文件
    with open(extra_file_path, 'w', encoding='utf-8') as extra_file:
        for name in extra_files:
            print(f"多余的文件名：{name}")
            extra_file.write(name + '\n')

if __name__ == '__main__':
    # 提示用户输入文件夹路径
    folder_path = input("请输入需要检查的文件夹路径：")
    # 检查路径是否存在
    if not os.path.exists(folder_path):
        print("输入的路径不存在，请检查路径是否正确。")
    else:
        print("请输入需要查询的文件名（每行一个文件名），输入完成后按一行单独的回车结束输入：")
        input_names = []
        while True:
            line = input()
            if line.strip() == "":
                break  # 如果输入为空行，结束输入
            input_names.append(line.strip())

        print(f"输入的文件名列表：{input_names}")

        # 检查文件
        check_files(input_names, folder_path)

        print("检查完成，未找到的文件名已输出到 未找到的文件名.txt 文件中。",
              "多余的文件名已输出到 多余的文件名.txt 文件中。")
