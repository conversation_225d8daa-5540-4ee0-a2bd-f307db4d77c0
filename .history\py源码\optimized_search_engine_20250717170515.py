"""
优化的搜索引擎核心模块
提供高性能的文件搜索功能，支持多种搜索模式
"""

import os
import re
import glob
import time
import fnmatch
from typing import List, Dict, Generator, Tuple, Set, Optional
from dataclasses import dataclass
from enum import Enum
import threading
from collections import defaultdict


class SearchMode(Enum):
    """搜索模式枚举"""
    CONTAINS = "contains"          # 包含匹配（默认）
    EXACT = "exact"               # 精确匹配
    REGEX = "regex"               # 正则表达式
    WILDCARD = "wildcard"         # 通配符匹配
    STARTS_WITH = "starts_with"   # 开头匹配
    ENDS_WITH = "ends_with"       # 结尾匹配
    EXCLUDE = "exclude"           # 排除匹配
    WORD_BOUNDARY = "word_boundary"  # 词边界匹配（精准匹配）


@dataclass
class SearchPattern:
    """搜索模式数据类"""
    pattern: str
    mode: SearchMode
    case_sensitive: bool = False
    compiled_regex: Optional[re.Pattern] = None
    
    def __post_init__(self):
        """初始化后处理，预编译正则表达式"""
        if self.mode == SearchMode.REGEX:
            flags = 0 if self.case_sensitive else re.IGNORECASE
            try:
                self.compiled_regex = re.compile(self.pattern, flags)
            except re.error:
                # 如果正则表达式无效，回退到包含匹配
                self.mode = SearchMode.CONTAINS
        elif self.mode == SearchMode.WILDCARD:
            # 将通配符转换为正则表达式
            regex_pattern = fnmatch.translate(self.pattern)
            flags = 0 if self.case_sensitive else re.IGNORECASE
            self.compiled_regex = re.compile(regex_pattern, flags)


@dataclass
class SearchResult:
    """搜索结果数据类"""
    file_path: str
    filename: str
    relative_path: str
    matched_patterns: List[str]
    file_size: int = 0
    modified_time: float = 0.0
    
    def __post_init__(self):
        """获取文件信息"""
        if os.path.exists(self.file_path):
            stat = os.stat(self.file_path)
            self.file_size = stat.st_size
            self.modified_time = stat.st_mtime


class SearchCache:
    """搜索缓存管理"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, Dict] = {}
        self.max_size = max_size
        self.access_times: Dict[str, float] = {}
        self._lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Dict]:
        """获取缓存"""
        with self._lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key]
            return None
    
    def set(self, key: str, value: Dict):
        """设置缓存"""
        with self._lock:
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def _evict_oldest(self):
        """清除最旧的缓存项"""
        if not self.access_times:
            return
        
        oldest_key = min(self.access_times.keys(), 
                        key=lambda k: self.access_times[k])
        del self.cache[oldest_key]
        del self.access_times[oldest_key]
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_times.clear()


class OptimizedSearchEngine:
    """优化的搜索引擎"""
    
    def __init__(self, cache_size: int = 1000):
        self.cache = SearchCache(cache_size)
        self.file_index: Dict[str, Set[str]] = defaultdict(set)
        self.index_built = False
        self._lock = threading.Lock()
    
    def build_file_index(self, search_path: str, extensions: List[str]) -> int:
        """构建文件索引以提高搜索速度"""
        with self._lock:
            self.file_index.clear()
            file_count = 0
            
            for file_path in self._scan_files_generator(search_path, extensions):
                filename = os.path.basename(file_path).lower()
                
                # 为文件名的每个子串建立索引
                for i in range(len(filename)):
                    for j in range(i + 1, len(filename) + 1):
                        substring = filename[i:j]
                        if len(substring) >= 2:  # 只索引长度>=2的子串
                            self.file_index[substring].add(file_path)
                
                file_count += 1
            
            self.index_built = True
            return file_count
    
    def _scan_files_generator(self, search_path: str, extensions: List[str]) -> Generator[str, None, None]:
        """使用生成器扫描文件，减少内存占用"""
        if not os.path.exists(search_path):
            return
        
        # 预处理扩展名
        ext_set = {ext.lower() for ext in extensions}
        
        for root, dirs, files in os.walk(search_path):
            # 过滤隐藏目录
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.startswith('.'):  # 跳过隐藏文件
                    continue
                
                # 检查扩展名
                _, ext = os.path.splitext(file)
                if ext.lower() in ext_set:
                    yield os.path.join(root, file)
    
    def _match_pattern(self, filename: str, pattern: SearchPattern) -> bool:
        """匹配单个模式"""
        try:
            target = filename if pattern.case_sensitive else filename.lower()
            search_text = pattern.pattern if pattern.case_sensitive else pattern.pattern.lower()

            if pattern.mode == SearchMode.CONTAINS:
                return search_text in target
            elif pattern.mode == SearchMode.EXACT:
                # 对于精确匹配，可以匹配文件名（不含扩展名）或完整文件名
                name_without_ext = os.path.splitext(target)[0]
                return target == search_text or name_without_ext == search_text
            elif pattern.mode == SearchMode.REGEX:
                if pattern.compiled_regex:
                    return bool(pattern.compiled_regex.search(filename))
                else:
                    # 如果正则表达式编译失败，回退到包含匹配
                    return search_text in target
            elif pattern.mode == SearchMode.WILDCARD:
                if pattern.compiled_regex:
                    return bool(pattern.compiled_regex.match(filename))
                else:
                    # 如果通配符编译失败，使用简单的通配符匹配
                    import fnmatch
                    return fnmatch.fnmatch(filename.lower() if not pattern.case_sensitive else filename,
                                         pattern.pattern.lower() if not pattern.case_sensitive else pattern.pattern)
            elif pattern.mode == SearchMode.STARTS_WITH:
                return target.startswith(search_text)
            elif pattern.mode == SearchMode.ENDS_WITH:
                return target.endswith(search_text)
            elif pattern.mode == SearchMode.EXCLUDE:
                return search_text not in target

            return False
        except Exception:
            # 如果匹配过程中出现任何错误，返回False
            return False
    
    def search_optimized(self, search_path: str, patterns: List[SearchPattern],
                        extensions: List[str], progress_callback=None) -> Dict[str, List[SearchResult]]:
        """优化的搜索方法"""
        # 生成缓存键
        cache_key = self._generate_cache_key(search_path, patterns, extensions)

        # 检查缓存
        cached_result = self.cache.get(cache_key)
        if cached_result:
            if progress_callback:
                progress_callback("使用缓存结果")
            return cached_result

        results = {pattern.pattern: [] for pattern in patterns}
        processed_files = 0
        total_files = 0

        # 先统计总文件数用于进度显示
        try:
            total_files = sum(1 for _ in self._scan_files_generator(search_path, extensions))
        except:
            total_files = 0

        if progress_callback:
            progress_callback(f"找到 {total_files} 个文件，开始搜索...")

        # 使用生成器逐个处理文件
        for file_path in self._scan_files_generator(search_path, extensions):
            try:
                filename = os.path.basename(file_path)
                relative_path = os.path.relpath(os.path.dirname(file_path), search_path)

                matched_patterns = []

                # 一次遍历检查所有模式
                for pattern in patterns:
                    try:
                        if self._match_pattern(filename, pattern):
                            matched_patterns.append(pattern.pattern)
                    except Exception as e:
                        # 如果某个模式匹配失败，继续处理其他模式
                        if progress_callback:
                            progress_callback(f"模式匹配警告: {str(e)}")
                        continue

                # 如果有匹配的模式，创建搜索结果
                if matched_patterns:
                    search_result = SearchResult(
                        file_path=file_path,
                        filename=filename,
                        relative_path=relative_path,
                        matched_patterns=matched_patterns
                    )

                    # 将结果添加到对应的模式结果中
                    for pattern_text in matched_patterns:
                        results[pattern_text].append(search_result)

                processed_files += 1

                # 更新进度
                if progress_callback and processed_files % 50 == 0:
                    if total_files > 0:
                        progress = int((processed_files / total_files) * 100)
                        progress_callback(f"已处理 {processed_files}/{total_files} 个文件 ({progress}%)")
                    else:
                        progress_callback(f"已处理 {processed_files} 个文件")

            except Exception as e:
                # 处理单个文件时出错，继续处理下一个
                if progress_callback:
                    progress_callback(f"处理文件出错: {os.path.basename(file_path)}")
                continue

        # 缓存结果
        try:
            self.cache.set(cache_key, results)
        except:
            pass  # 缓存失败不影响搜索结果

        if progress_callback:
            total_matches = sum(len(matches) for matches in results.values())
            progress_callback(f"搜索完成，共找到 {total_matches} 个匹配文件")

        return results
    
    def _generate_cache_key(self, search_path: str, patterns: List[SearchPattern], 
                           extensions: List[str]) -> str:
        """生成缓存键"""
        pattern_strs = [f"{p.pattern}:{p.mode.value}:{p.case_sensitive}" for p in patterns]
        return f"{search_path}:{':'.join(sorted(pattern_strs))}:{':'.join(sorted(extensions))}"
    
    def get_search_statistics(self, results: Dict[str, List[SearchResult]]) -> Dict:
        """获取搜索统计信息"""
        total_matches = sum(len(matches) for matches in results.values())
        pattern_stats = {pattern: len(matches) for pattern, matches in results.items()}
        
        # 文件大小统计
        all_results = []
        for matches in results.values():
            all_results.extend(matches)
        
        if all_results:
            total_size = sum(result.file_size for result in all_results)
            avg_size = total_size / len(all_results)
        else:
            total_size = avg_size = 0
        
        return {
            'total_matches': total_matches,
            'pattern_stats': pattern_stats,
            'total_size': total_size,
            'average_size': avg_size,
            'unique_files': len(set(result.file_path for matches in results.values() for result in matches))
        }
    
    def clear_cache(self):
        """清空搜索缓存"""
        self.cache.clear()
