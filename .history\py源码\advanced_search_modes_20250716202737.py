"""
高级搜索模式模块
支持多种搜索方式和模式组合
"""

import re
import os
import fnmatch
from typing import List, Dict, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import unicodedata


class SearchOperator(Enum):
    """搜索操作符"""
    AND = "AND"
    OR = "OR"
    NOT = "NOT"


@dataclass
class SearchExpression:
    """搜索表达式"""
    pattern: str
    mode: str
    operator: SearchOperator = SearchOperator.OR
    case_sensitive: bool = False
    weight: float = 1.0  # 搜索权重


class AdvancedSearchParser:
    """高级搜索解析器"""
    
    def __init__(self):
        self.operators = ['AND', 'OR', 'NOT', '&', '|', '!', '+', '-']
        self.mode_prefixes = {
            'regex:': 'regex',
            'r:': 'regex',
            'exact:': 'exact',
            'e:': 'exact',
            'wild:': 'wildcard',
            'w:': 'wildcard',
            'start:': 'starts_with',
            's:': 'starts_with',
            'end:': 'ends_with',
            'exclude:': 'exclude',
            'x:': 'exclude'
        }
    
    def parse_search_query(self, query: str) -> List[SearchExpression]:
        """解析搜索查询字符串"""
        expressions = []
        
        # 分割查询字符串
        tokens = self._tokenize_query(query)
        
        current_operator = SearchOperator.OR
        
        for token in tokens:
            if token.upper() in ['AND', '&']:
                current_operator = SearchOperator.AND
                continue
            elif token.upper() in ['OR', '|']:
                current_operator = SearchOperator.OR
                continue
            elif token.upper() in ['NOT', '!', '-']:
                current_operator = SearchOperator.NOT
                continue
            
            # 解析搜索表达式
            expression = self._parse_expression(token, current_operator)
            if expression:
                expressions.append(expression)
        
        return expressions
    
    def _tokenize_query(self, query: str) -> List[str]:
        """分词处理"""
        # 处理引号内的内容
        tokens = []
        current_token = ""
        in_quotes = False
        quote_char = None
        
        i = 0
        while i < len(query):
            char = query[i]
            
            if char in ['"', "'"] and not in_quotes:
                in_quotes = True
                quote_char = char
                i += 1
                continue
            elif char == quote_char and in_quotes:
                in_quotes = False
                if current_token:
                    tokens.append(current_token)
                    current_token = ""
                quote_char = None
                i += 1
                continue
            
            if in_quotes:
                current_token += char
            elif char.isspace():
                if current_token:
                    tokens.append(current_token)
                    current_token = ""
            else:
                current_token += char
            
            i += 1
        
        if current_token:
            tokens.append(current_token)
        
        return tokens
    
    def _parse_expression(self, token: str, operator: SearchOperator) -> Optional[SearchExpression]:
        """解析单个表达式"""
        if not token.strip():
            return None
        
        # 检查模式前缀
        mode = 'contains'
        pattern = token
        case_sensitive = False
        
        # 检查大小写敏感标记
        if token.endswith('/i'):
            case_sensitive = False
            token = token[:-2]
        elif token.endswith('/c'):
            case_sensitive = True
            token = token[:-2]
        
        # 检查模式前缀
        for prefix, mode_name in self.mode_prefixes.items():
            if token.lower().startswith(prefix):
                mode = mode_name
                pattern = token[len(prefix):]
                break
        
        return SearchExpression(
            pattern=pattern,
            mode=mode,
            operator=operator,
            case_sensitive=case_sensitive
        )


class AdvancedMatcher:
    """高级匹配器"""
    
    def __init__(self):
        self.compiled_patterns = {}
    
    def match_expressions(self, filename: str, expressions: List[SearchExpression]) -> Tuple[bool, List[str]]:
        """匹配多个表达式"""
        if not expressions:
            return False, []
        
        matched_patterns = []
        results = []
        
        for expr in expressions:
            match_result = self._match_single_expression(filename, expr)
            results.append((expr.operator, match_result, expr.pattern))
            
            if match_result:
                matched_patterns.append(expr.pattern)
        
        # 计算最终结果
        final_result = self._evaluate_expression_results(results)
        
        return final_result, matched_patterns if final_result else []
    
    def _match_single_expression(self, filename: str, expr: SearchExpression) -> bool:
        """匹配单个表达式"""
        target = filename if expr.case_sensitive else filename.lower()
        pattern = expr.pattern if expr.case_sensitive else expr.pattern.lower()
        
        if expr.mode == 'contains':
            return pattern in target
        elif expr.mode == 'exact':
            return target == pattern
        elif expr.mode == 'regex':
            return self._match_regex(filename, expr)
        elif expr.mode == 'wildcard':
            return self._match_wildcard(filename, expr)
        elif expr.mode == 'starts_with':
            return target.startswith(pattern)
        elif expr.mode == 'ends_with':
            return target.endswith(pattern)
        elif expr.mode == 'exclude':
            return pattern not in target
        
        return False
    
    def _match_regex(self, filename: str, expr: SearchExpression) -> bool:
        """正则表达式匹配"""
        cache_key = f"{expr.pattern}:{expr.case_sensitive}"
        
        if cache_key not in self.compiled_patterns:
            try:
                flags = 0 if expr.case_sensitive else re.IGNORECASE
                self.compiled_patterns[cache_key] = re.compile(expr.pattern, flags)
            except re.error:
                return False
        
        pattern = self.compiled_patterns[cache_key]
        return bool(pattern.search(filename))
    
    def _match_wildcard(self, filename: str, expr: SearchExpression) -> bool:
        """通配符匹配"""
        cache_key = f"wild:{expr.pattern}:{expr.case_sensitive}"
        
        if cache_key not in self.compiled_patterns:
            try:
                regex_pattern = fnmatch.translate(expr.pattern)
                flags = 0 if expr.case_sensitive else re.IGNORECASE
                self.compiled_patterns[cache_key] = re.compile(regex_pattern, flags)
            except re.error:
                return False
        
        pattern = self.compiled_patterns[cache_key]
        return bool(pattern.match(filename))
    
    def _evaluate_expression_results(self, results: List[Tuple]) -> bool:
        """评估表达式结果"""
        if not results:
            return False
        
        # 简化的逻辑评估
        final_result = False
        current_result = False
        
        for i, (operator, match_result, pattern) in enumerate(results):
            if i == 0:
                current_result = match_result
                final_result = match_result
            else:
                if operator == SearchOperator.AND:
                    current_result = current_result and match_result
                elif operator == SearchOperator.OR:
                    current_result = current_result or match_result
                elif operator == SearchOperator.NOT:
                    current_result = current_result and not match_result
                
                final_result = current_result
        
        return final_result


class FuzzyMatcher:
    """模糊匹配器"""
    
    def __init__(self, threshold: float = 0.6):
        self.threshold = threshold
    
    def fuzzy_match(self, filename: str, pattern: str, case_sensitive: bool = False) -> Tuple[bool, float]:
        """模糊匹配"""
        if not case_sensitive:
            filename = filename.lower()
            pattern = pattern.lower()
        
        # 计算相似度
        similarity = self._calculate_similarity(filename, pattern)
        
        return similarity >= self.threshold, similarity
    
    def _calculate_similarity(self, s1: str, s2: str) -> float:
        """计算字符串相似度（简化版编辑距离）"""
        if not s1 or not s2:
            return 0.0
        
        if s1 == s2:
            return 1.0
        
        # 检查包含关系
        if s2 in s1 or s1 in s2:
            shorter = min(len(s1), len(s2))
            longer = max(len(s1), len(s2))
            return shorter / longer
        
        # 简化的编辑距离计算
        distance = self._levenshtein_distance(s1, s2)
        max_len = max(len(s1), len(s2))
        
        return 1.0 - (distance / max_len)
    
    def _levenshtein_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self._levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]


class PinyinMatcher:
    """拼音匹配器（简化版）"""
    
    def __init__(self):
        # 简化的拼音映射表
        self.pinyin_map = {
            'a': ['a', 'ai', 'an', 'ang', 'ao'],
            'b': ['ba', 'bai', 'ban', 'bang', 'bao', 'bei', 'ben', 'beng', 'bi', 'bian', 'biao', 'bie', 'bin', 'bing', 'bo', 'bu'],
            'c': ['ca', 'cai', 'can', 'cang', 'cao', 'ce', 'cen', 'ceng', 'cha', 'chai', 'chan', 'chang', 'chao', 'che', 'chen', 'cheng', 'chi', 'chong', 'chou', 'chu', 'chuai', 'chuan', 'chuang', 'chui', 'chun', 'chuo', 'ci', 'cong', 'cou', 'cu', 'cuan', 'cui', 'cun', 'cuo'],
            # ... 可以扩展更多拼音映射
        }
    
    def match_pinyin(self, filename: str, pinyin_pattern: str) -> bool:
        """拼音匹配（简化实现）"""
        # 这里只是一个简化的示例实现
        # 实际应用中需要更完整的拼音库
        
        # 移除文件扩展名
        name_without_ext = os.path.splitext(filename)[0]
        
        # 简单的拼音首字母匹配
        if len(pinyin_pattern) <= 3:
            return self._match_initials(name_without_ext, pinyin_pattern)
        
        return False
    
    def _match_initials(self, text: str, initials: str) -> bool:
        """匹配拼音首字母"""
        # 简化实现：检查是否包含相应的字母组合
        text_lower = text.lower()
        initials_lower = initials.lower()
        
        # 检查连续字母匹配
        for i in range(len(text_lower) - len(initials_lower) + 1):
            if text_lower[i:i+len(initials_lower)] == initials_lower:
                return True
        
        return False


class SearchModeManager:
    """搜索模式管理器"""
    
    def __init__(self):
        self.parser = AdvancedSearchParser()
        self.matcher = AdvancedMatcher()
        self.fuzzy_matcher = FuzzyMatcher()
        self.pinyin_matcher = PinyinMatcher()
    
    def search_with_advanced_modes(self, filename: str, query: str, 
                                 enable_fuzzy: bool = False,
                                 enable_pinyin: bool = False) -> Tuple[bool, List[str], Dict]:
        """使用高级模式进行搜索"""
        results = {
            'exact_match': False,
            'fuzzy_match': False,
            'pinyin_match': False,
            'matched_patterns': [],
            'similarity_score': 0.0
        }
        
        # 解析查询
        expressions = self.parser.parse_search_query(query)
        
        # 精确匹配
        exact_match, matched_patterns = self.matcher.match_expressions(filename, expressions)
        results['exact_match'] = exact_match
        results['matched_patterns'] = matched_patterns
        
        # 模糊匹配
        if enable_fuzzy and not exact_match:
            for expr in expressions:
                fuzzy_match, similarity = self.fuzzy_matcher.fuzzy_match(
                    filename, expr.pattern, expr.case_sensitive
                )
                if fuzzy_match:
                    results['fuzzy_match'] = True
                    results['similarity_score'] = max(results['similarity_score'], similarity)
                    if expr.pattern not in results['matched_patterns']:
                        results['matched_patterns'].append(expr.pattern)
        
        # 拼音匹配
        if enable_pinyin and not exact_match and not results['fuzzy_match']:
            for expr in expressions:
                if self.pinyin_matcher.match_pinyin(filename, expr.pattern):
                    results['pinyin_match'] = True
                    if expr.pattern not in results['matched_patterns']:
                        results['matched_patterns'].append(expr.pattern)
        
        # 判断最终匹配结果
        final_match = (results['exact_match'] or 
                      results['fuzzy_match'] or 
                      results['pinyin_match'])
        
        return final_match, results['matched_patterns'], results
