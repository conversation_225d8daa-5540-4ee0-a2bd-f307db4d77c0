#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复PyInstaller打包问题的脚本
专门解决conda环境和依赖分析问题
"""

import os
import sys
import subprocess
import tempfile
import shutil

def create_fixed_spec(source_file, output_name, icon_path=None):
    """创建修复版的spec文件"""
    
    # 获取源文件的绝对路径
    source_file = os.path.abspath(source_file)
    source_dir = os.path.dirname(source_file)
    
    # 构建数据文件列表
    datas = []
    if icon_path and os.path.exists(icon_path):
        datas.append(f"('{icon_path}', '.')")
    
    # 检查是否有使用说明文件
    readme_files = ['使用说明.md', 'README.md', 'readme.txt']
    for readme in readme_files:
        readme_path = os.path.join(source_dir, readme)
        if os.path.exists(readme_path):
            datas.append(f"('{readme_path}', '.')")
            break
    
    datas_str = ',\n        '.join(datas) if datas else ''
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# 修复版PyInstaller spec文件

import sys
import os
from pathlib import Path

block_cipher = None

# 分析阶段
a = Analysis(
    [r'{source_file}'],
    pathex=[r'{source_dir}'],
    binaries=[],
    datas=[
        {datas_str}
    ],
    hiddenimports=[
        # PyQt5核心模块
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.sip',
        
        # 标准库模块
        'json',
        'subprocess',
        'pathlib',
        'datetime',
        'shutil',
        'tempfile',
        'threading',
        'queue',
        'configparser',
        
        # 包管理相关
        'pkg_resources',
        'setuptools',
        'distutils',
        'importlib',
        'importlib.util',
        'importlib.metadata',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'IPython',
        'jupyter',
        'notebook',
        'pytest',
        'test',
        'tests',
        '_pytest',
        'PyQt6',  # 排除PyQt6避免冲突
        'PySide2',
        'PySide6',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 打包阶段
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{output_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩，提高兼容性
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon={repr(icon_path) if icon_path and os.path.exists(icon_path) else None},
)
'''
    
    return spec_content

def fix_package_issues(source_file, output_name=None, icon_path=None):
    """修复打包问题并执行打包"""
    
    if not os.path.exists(source_file):
        print(f"❌ 错误: 源文件 {source_file} 不存在")
        return False
    
    if not output_name:
        output_name = os.path.splitext(os.path.basename(source_file))[0]
    
    print(f"🔧 修复打包问题")
    print(f"源文件: {source_file}")
    print(f"输出名称: {output_name}")
    if icon_path:
        print(f"图标文件: {icon_path}")
    
    try:
        # 创建临时spec文件
        spec_content = create_fixed_spec(source_file, output_name, icon_path)
        spec_filename = f"{output_name}_fixed.spec"
        
        with open(spec_filename, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"✅ 已创建修复版spec文件: {spec_filename}")
        
        # 清理之前的构建文件
        build_dir = "build"
        if os.path.exists(build_dir):
            shutil.rmtree(build_dir)
            print("🧹 已清理build目录")
        
        # 执行打包
        print("🚀 开始打包...")
        command = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            '--log-level=INFO',
            spec_filename
        ]
        
        print(f"执行命令: {' '.join(command)}")
        
        # 使用更好的编码处理
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',  # 替换无法解码的字符
            cwd=os.path.dirname(source_file) or '.'
        )
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            
            # 检查输出文件
            dist_dir = "dist"
            exe_file = os.path.join(dist_dir, f"{output_name}.exe")
            if os.path.exists(exe_file):
                file_size = os.path.getsize(exe_file) / (1024 * 1024)  # MB
                print(f"📦 输出文件: {exe_file}")
                print(f"📏 文件大小: {file_size:.1f} MB")
            
            return True
        else:
            print("❌ 打包失败:")
            if result.stdout:
                print("标准输出:")
                print(result.stdout[-1000:])  # 只显示最后1000个字符
            if result.stderr:
                print("错误输出:")
                print(result.stderr[-1000:])  # 只显示最后1000个字符
            return False
            
    except Exception as e:
        print(f"❌ 发生异常: {e}")
        return False
    
    finally:
        # 清理临时文件
        if 'spec_filename' in locals() and os.path.exists(spec_filename):
            try:
                os.remove(spec_filename)
                print(f"🧹 已清理临时文件: {spec_filename}")
            except:
                pass

def main():
    print("=" * 50)
    print("🛠️  PyInstaller打包问题修复工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        source_file = sys.argv[1]
        output_name = sys.argv[2] if len(sys.argv) > 2 else None
        icon_path = sys.argv[3] if len(sys.argv) > 3 else None
    else:
        source_file = input("请输入要打包的Python文件路径: ").strip().strip('"')
        output_name = input("请输入输出名称 (回车使用默认): ").strip() or None
        icon_path = input("请输入图标文件路径 (可选): ").strip().strip('"') or None
    
    success = fix_package_issues(source_file, output_name, icon_path)
    
    if success:
        print("\n🎉 打包完成！请检查dist目录中的文件。")
    else:
        print("\n💥 打包失败，请检查错误信息。")
    
    input("\n按Enter键退出...")

if __name__ == '__main__':
    main()
