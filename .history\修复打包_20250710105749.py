#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复版Python打包工具打包脚本
解决pyimod02_importers模块缺失问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_fixed_spec():
    """创建修复版的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['Python打包工具.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('mylogo.ico', '.'),
        ('使用说明.md', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'json',
        'subprocess',
        'pathlib',
        'datetime',
        'shutil',
        'threading',
        'queue',
        'configparser',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'importlib',
        'importlib.util',
        'importlib.metadata',
        'pkg_resources',
        'pyimod02_importers',
        'pyimod03_importers',
        'pyiboot01_bootstrap',
        'pyi_rth_inspect',
        'pyi_rth_pkgutil',
        'pyi_rth_pyqt5'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Python打包工具_修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩，避免兼容性问题
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='mylogo.ico',
    version='version_info.txt'
)
'''
    
    with open('Python打包工具_修复版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 修复版spec文件已创建")
    return 'Python打包工具_修复版.spec'

def create_version_file():
    """创建版本信息文件"""
    version_content = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [
            StringStruct(u'CompanyName', u'Python工具箱'),
            StringStruct(u'FileDescription', u'Python全自动打包工具'),
            StringStruct(u'FileVersion', u'*******'),
            StringStruct(u'InternalName', u'Python打包工具'),
            StringStruct(u'LegalCopyright', u'Copyright (C) 2024 西昌·水西丁'),
            StringStruct(u'OriginalFilename', u'Python打包工具.exe'),
            StringStruct(u'ProductName', u'Python全自动打包工具'),
            StringStruct(u'ProductVersion', u'*******')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print("✅ 版本信息文件已创建")

def build_fixed_version():
    """使用修复版spec文件进行打包"""
    print("\n开始修复版打包...")
    
    # 创建spec文件和版本文件
    spec_file = create_fixed_spec()
    create_version_file()
    
    # 清理之前的构建
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✅ 清理build目录")
    
    # 使用spec文件打包
    command = ['pyinstaller', '--clean', spec_file]
    
    print(f"执行命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 修复版打包成功！")
            return True
        else:
            print(f"❌ 打包失败:")
            print(f"错误输出: {result.stderr}")
            print(f"标准输出: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def test_exe():
    """测试生成的exe文件"""
    print("\n测试生成的exe文件...")
    
    exe_path = Path("dist/Python打包工具_修复版.exe")
    if not exe_path.exists():
        print("❌ 未找到生成的exe文件")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    print(f"文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
    
    # 可以尝试运行exe文件进行测试
    try:
        print("🧪 尝试启动exe文件进行测试...")
        process = subprocess.Popen([str(exe_path)], shell=True)
        print("✅ exe文件启动成功！")
        print("💡 请手动测试程序功能，然后关闭程序")
        return True
    except Exception as e:
        print(f"⚠️ 启动测试失败: {e}")
        return False

def create_release_package():
    """创建发布包"""
    print("\n创建发布包...")
    
    # 复制必要文件到dist目录
    files_to_copy = [
        ('使用说明.md', 'dist/使用说明.md'),
        ('README.md', 'dist/README.md'),
        ('测试程序.py', 'dist/测试程序.py'),
        ('mylogo.ico', 'dist/mylogo.ico')
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            try:
                shutil.copy2(src, dst)
                print(f"✅ 复制: {src} -> {dst}")
            except Exception as e:
                print(f"⚠️ 复制失败 {src}: {e}")
    
    # 创建发布说明
    release_notes = """Python全自动打包工具 v1.0.0 - 修复版
==========================================

修复内容：
- 解决了pyimod02_importers模块缺失问题
- 优化了打包配置，提高兼容性
- 禁用UPX压缩，避免兼容性问题
- 添加了更多隐藏导入模块

文件说明：
- Python打包工具_修复版.exe - 主程序（修复版）
- 测试程序.py - 测试用例
- 使用说明.md - 详细使用说明
- README.md - 项目说明
- mylogo.ico - 默认图标

使用方法：
1. 双击运行 Python打包工具_修复版.exe
2. 选择要打包的Python文件
3. 配置打包选项
4. 点击"开始打包"

注意事项：
- 确保目标电脑安装了Visual C++运行库
- 首次运行可能需要一些时间加载
- 如遇问题请查看使用说明.md

作者: 西昌·水西丁
版本: v1.0.0 修复版
日期: 2024年7月
"""
    
    with open('dist/发布说明.txt', 'w', encoding='utf-8') as f:
        f.write(release_notes)
    
    print("✅ 创建发布说明文件")

def cleanup():
    """清理临时文件"""
    print("\n清理临时文件...")
    
    files_to_remove = [
        'version_info.txt',
        'Python打包工具_修复版.spec'
    ]
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✅ 删除: {file_name}")
            except:
                print(f"⚠️ 无法删除: {file_name}")

def main():
    """主函数"""
    print("=" * 60)
    print("    Python打包工具 - 修复版打包脚本")
    print("=" * 60)
    
    # 检查源文件
    if not os.path.exists('Python打包工具.py'):
        print("❌ 源文件 Python打包工具.py 不存在")
        return False
    
    # 执行修复版打包
    if not build_fixed_version():
        print("\n❌ 修复版打包失败！")
        return False
    
    # 创建发布包
    create_release_package()
    
    # 测试exe文件
    test_exe()
    
    # 清理临时文件
    cleanup()
    
    print("\n" + "=" * 60)
    print("✅ 修复版打包完成！")
    print("📁 exe文件位置: dist/Python打包工具_修复版.exe")
    print("📖 发布说明: dist/发布说明.txt")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 修复版打包成功！现在应该可以正常运行了")
    else:
        print("\n💥 修复版打包失败")
    
    input("\n按回车键退出...")
