import os
import glob
import ezdxf

class DXFChecker:
    def check_multiple_graphics(self, entities):
        """检查是否包含多个独立的图形 - 基于几何分析"""
        if len(entities) < 2:  # 至少需要2个实体才可能是多图形
            return False
        
        # 获取所有实体的边界框
        bounding_boxes = []
        for entity in entities:
            try:
                # 获取实体的边界框
                bbox = self.get_entity_bounding_box(entity)
                if bbox:
                    bounding_boxes.append(bbox)
            except:
                continue
        
        if len(bounding_boxes) < 2:
            return False
        
        # 使用聚类算法检测分离的图形组
        groups = self.cluster_bounding_boxes(bounding_boxes)
        
        # 如果有多个明显分离的组，则认为是多图形
        return len(groups) > 1
    
    def get_entity_bounding_box(self, entity):
        """获取实体的边界框"""
        try:
            # 不同类型的实体有不同的获取边界框方法
            entity_type = entity.dxftype()
            
            if entity_type == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                return {
                    'min_x': min(start.x, end.x),
                    'max_x': max(start.x, end.x),
                    'min_y': min(start.y, end.y),
                    'max_y': max(start.y, end.y)
                }
            
            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                radius = entity.dxf.radius
                return {
                    'min_x': center.x - radius,
                    'max_x': center.x + radius,
                    'min_y': center.y - radius,
                    'max_y': center.y + radius
                }
            
            elif entity_type == 'ARC':
                center = entity.dxf.center
                radius = entity.dxf.radius
                # 简化处理，使用整个圆的边界框
                return {
                    'min_x': center.x - radius,
                    'max_x': center.x + radius,
                    'min_y': center.y - radius,
                    'max_y': center.y + radius
                }
            
            elif entity_type in ['POLYLINE', 'LWPOLYLINE']:
                # 获取多段线的所有顶点
                points = []
                if hasattr(entity, 'vertices'):
                    for vertex in entity.vertices:
                        if hasattr(vertex, 'dxf') and hasattr(vertex.dxf, 'location'):
                            points.append(vertex.dxf.location)
                elif hasattr(entity, 'get_points'):
                    points = list(entity.get_points())
                
                if points:
                    x_coords = [p[0] if isinstance(p, (list, tuple)) else p.x for p in points]
                    y_coords = [p[1] if isinstance(p, (list, tuple)) else p.y for p in points]
                    return {
                        'min_x': min(x_coords),
                        'max_x': max(x_coords),
                        'min_y': min(y_coords),
                        'max_y': max(y_coords)
                    }
            
        except Exception:
            pass
        
        return None
    
    def cluster_bounding_boxes(self, bounding_boxes):
        """使用简单的聚类算法将边界框分组"""
        if not bounding_boxes:
            return []
        
        groups = []
        used = [False] * len(bounding_boxes)
        
        for i, bbox1 in enumerate(bounding_boxes):
            if used[i]:
                continue
            
            # 创建新组
            group = [i]
            used[i] = True
            
            # 查找与当前边界框相近或重叠的其他边界框
            for j, bbox2 in enumerate(bounding_boxes):
                if used[j] or i == j:
                    continue
                
                if self.are_bounding_boxes_connected(bbox1, bbox2):
                    group.append(j)
                    used[j] = True
            
            groups.append(group)
        
        return groups
    
    def are_bounding_boxes_connected(self, bbox1, bbox2):
        """判断两个边界框是否应该属于同一个图形组"""
        # 计算两个边界框的距离
        distance = self.calculate_bbox_distance(bbox1, bbox2)
        
        # 计算边界框的平均尺寸作为参考
        avg_size1 = ((bbox1['max_x'] - bbox1['min_x']) + (bbox1['max_y'] - bbox1['min_y'])) / 2
        avg_size2 = ((bbox2['max_x'] - bbox2['min_x']) + (bbox2['max_y'] - bbox2['min_y'])) / 2
        avg_size = (avg_size1 + avg_size2) / 2
        
        # 如果距离小于平均尺寸的一定倍数，认为它们属于同一组
        # 这个阈值可以根据实际情况调整
        threshold = max(avg_size * 0.8, 20)  # 调整阈值，使其更敏感
        
        return distance <= threshold
    
    def calculate_bbox_distance(self, bbox1, bbox2):
        """计算两个边界框之间的最短距离"""
        # 如果边界框重叠，距离为0
        if (bbox1['max_x'] >= bbox2['min_x'] and bbox1['min_x'] <= bbox2['max_x'] and
            bbox1['max_y'] >= bbox2['min_y'] and bbox1['min_y'] <= bbox2['max_y']):
            return 0
        
        # 计算中心点之间的距离
        center1_x = (bbox1['min_x'] + bbox1['max_x']) / 2
        center1_y = (bbox1['min_y'] + bbox1['max_y']) / 2
        center2_x = (bbox2['min_x'] + bbox2['max_x']) / 2
        center2_y = (bbox2['min_y'] + bbox2['max_y']) / 2
        
        dx = center2_x - center1_x
        dy = center2_y - center1_y
        
        return (dx * dx + dy * dy) ** 0.5

def test_detection():
    """测试新的检测逻辑"""
    checker = DXFChecker()
    test_folder = "测试新检测"
    
    if not os.path.exists(test_folder):
        print(f"测试文件夹 '{test_folder}' 不存在，请先运行 '测试新检测逻辑.py'")
        return
    
    dxf_files = glob.glob(os.path.join(test_folder, "*.dxf"))
    
    print("测试新的检测逻辑:")
    print("=" * 50)
    
    for dxf_file in dxf_files:
        filename = os.path.basename(dxf_file)
        print(f"\n检查文件: {filename}")
        
        try:
            doc = ezdxf.readfile(dxf_file)
            msp = doc.modelspace()
            
            # 获取图形实体
            graphic_entities = []
            for entity in msp:
                if entity.dxftype() not in ['TEXT', 'MTEXT', 'DIMENSION', 'INSERT', 'ATTRIB', 'ATTDEF']:
                    graphic_entities.append(entity)
            
            print(f"  实体数量: {len(graphic_entities)}")
            
            # 显示实体类型
            entity_types = {}
            for entity in graphic_entities:
                entity_type = entity.dxftype()
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            print(f"  实体类型: {entity_types}")
            
            # 获取边界框信息
            bounding_boxes = []
            for entity in graphic_entities:
                bbox = checker.get_entity_bounding_box(entity)
                if bbox:
                    bounding_boxes.append(bbox)
            
            print(f"  有效边界框数量: {len(bounding_boxes)}")
            
            # 显示边界框信息
            for i, bbox in enumerate(bounding_boxes):
                print(f"    边界框{i+1}: ({bbox['min_x']:.1f},{bbox['min_y']:.1f}) - ({bbox['max_x']:.1f},{bbox['max_y']:.1f})")
            
            # 检测结果
            is_multiple = checker.check_multiple_graphics(graphic_entities)
            
            if is_multiple:
                groups = checker.cluster_bounding_boxes(bounding_boxes)
                print(f"  ⚠️ 检测结果: 多图形 (发现 {len(groups)} 个分离的组)")
                for i, group in enumerate(groups):
                    print(f"    组{i+1}: 包含 {len(group)} 个边界框")
            else:
                print(f"  ✓ 检测结果: 单图形")
            
        except Exception as e:
            print(f"  ✗ 读取文件出错: {str(e)}")

if __name__ == "__main__":
    test_detection()
