清单处理工具 v1.0.0 使用说明
=====================================

## 文件说明
- 清单处理工具.exe - 主程序（无需安装Python环境）
- 使用说明.txt - 本文件
- list_processor_config.ini - 配置文件（首次运行后自动生成）

## 系统要求
- Windows 7/8/10/11 (64位)
- 内存: 建议2GB以上
- 磁盘空间: 至少100MB可用空间
- 无需安装Python或其他依赖

## 使用方法

### 1. 准备工作
确保您的项目目录包含以下结构：
```
项目根目录/
├── 清单/
│   ├── 报料模板（V4.1）.xlsx
│   ├── 零件所属构件清单.xlsx
│   └── 跨零件清单.xlsx
└── NC_dxf/
    ├── A001.dxf
    ├── A002.dxf
    └── ...其他DXF文件
```

### 2. 运行程序
1. 双击运行"清单处理工具.exe"
2. 在"项目路径"中选择包含"清单"和"NC_dxf"文件夹的根目录
3. 根据需要设置处理选项：
   - ☑ 处理完成后显示提醒
   - ☐ 自动创建DXF压缩包
4. 点击"开始处理"按钮

### 3. 交互操作
程序运行过程中可能出现以下对话框：

**PL前缀确认对话框**
- 当检测到类似"10*177"格式的数据时出现
- 询问是否需要添加"PL"前缀
- 选择"是"或"否"根据实际需要

**乘法格式提醒对话框**
- 当检测到使用"x"作为乘号的数据时出现
- 系统会自动将"x"替换为"*"
- 点击"确定"继续

**花纹板提醒对话框**
- 当检测到花纹板相关关键词时出现
- 提醒用户核对花纹板相关项目
- 点击"确定"完成

## 功能特点

### 1. 全局花纹板关键词检测
- 检测关键词：花、花纹、花纹板、HWB、HW
- 在所有Excel列中进行全局搜索
- 只有发现关键词时才显示花纹板提醒

### 2. PL前缀数据格式确认
- 自动检测"数字*数字"格式的数据
- 用户可选择是否添加PL前缀
- 批量应用用户选择

### 3. 乘法格式自动处理
- 自动检测"x"作为乘号的数据
- 自动替换为标准的"*"格式
- 支持大小写"x"和"X"

### 4. DXF文件ZIP压缩
- 可选功能，通过界面开关控制
- 只有在没有缺失DXF文件时才创建
- 压缩包保存到项目根目录
- 文件名包含时间戳，避免重复

### 5. 智能文件处理
- 自动重命名DXF文件（A→F1, B→F2, C→W1）
- 生成直发配件清单和导入文件
- 处理跨零件清单
- 检测重复文件名和大小写问题

## 输出文件

处理完成后，在NC_dxf文件夹中会生成：
- 直发配件清单.xlsx
- 直发配件导入.csv
- X跨零件清单.xlsx（X为文件名首字符）
- X跨零件导入.csv
- 各种日志文件（如有警告或错误）

如果启用了ZIP压缩功能，还会在项目根目录生成：
- 处理后的DXF文件_YYYYMMDD_HHMMSS.zip

## 注意事项

### 重要提醒
- 处理前请备份重要文件
- 确保Excel文件格式正确
- 处理过程中请勿关闭程序
- 如有缺失DXF文件，会生成详细日志

### 常见问题
1. **程序无法启动**
   - 检查是否为64位Windows系统
   - 尝试以管理员身份运行

2. **找不到清单文件夹**
   - 确保选择的是包含"清单"和"NC_dxf"的根目录
   - 检查文件夹名称是否正确

3. **处理失败**
   - 查看程序日志信息
   - 检查Excel文件是否损坏
   - 确保有足够的磁盘空间

4. **DXF文件缺失**
   - 程序会生成缺失文件日志
   - 请根据日志补充缺失的DXF文件

## 配置文件

程序会自动生成配置文件"list_processor_config.ini"，保存用户设置：
- 上次使用的项目路径
- 提醒开关状态
- ZIP压缩开关状态

## 技术支持

如遇问题，请提供以下信息：
- 操作系统版本
- 程序版本号
- 错误截图或日志信息
- 问题重现步骤

## 版本信息
- 版本: 1.0.0
- 更新日期: 2024年12月
- 兼容系统: Windows 7/8/10/11
- 文件大小: 约62MB

## 更新日志
v1.0.0 (2024-12-07)
- 首次发布
- 支持全局花纹板关键词检测
- 支持PL前缀数据确认
- 支持乘法格式自动处理
- 支持DXF文件ZIP压缩
- 完整的用户界面和交互功能
