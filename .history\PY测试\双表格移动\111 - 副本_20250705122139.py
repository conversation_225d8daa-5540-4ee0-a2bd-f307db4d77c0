﻿import sys
import os
import configparser
import subprocess
import time
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QMessageBox, QFileDialog, QTextEdit, QListWidget, QLineEdit, QInputDialog, QDialog, QListWidgetItem, QMenu, QComboBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor
import ctypes


def read_config(config_file):
    """统一的配置文件读取函数，处理编码问题"""
    config = configparser.ConfigParser()
    if not os.path.exists(config_file):
        return config, False
        
    try:
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config.read_file(f)
        except UnicodeDecodeError:
            with open(config_file, 'r', encoding='gbk') as f:
                config.read_file(f)
        return config, True
    except Exception as e:
        print(f"读取配置文件失败: {str(e)}")
        return config, False

def write_config(config, config_file):
    """统一的配置文件写入函数"""
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        return True
    except Exception as e:
        print(f"保存配置文件失败: {str(e)}")
        return False


class SoftwareLauncher(QWidget):
    config_saved = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.software_list = {}
        # 使用当前脚本所在目录的config.ini
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
        self.startup_interval = 1  # 默认启动间隔为1秒
        self.current_drive = os.path.splitdrive(os.path.abspath(__file__))[0]
        self.software_directory = self.get_software_directory()
        self.startup_order = []
        self.initUI()
        self.load_config()
        self.load_software_from_directory()

    def get_software_directory(self):
        config = configparser.ConfigParser()
        try:
            config.read(self.config_file, encoding='utf-8')
        except UnicodeDecodeError:
            config.read(self.config_file, encoding='gbk')
        if 'Settings' in config and 'software_directory' in config['Settings']:
            return config['Settings']['software_directory']
        else:
            return ''

    def initUI(self):
        # 缩小 UI 界面
        # 修复：添加缺失的引号
        self.setWindowTitle('软件启动工具')
        self.setGeometry(800, 400, 400, 100)
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 8px;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QLabel {
                font-size: 14px;
                color: #333;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
            QListWidget {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        # 主布局采用水平布局，左侧放按钮，右侧放编辑窗口
        main_layout = QHBoxLayout()

        # 左侧按钮布局，竖向排列
        left_layout = QVBoxLayout()

        # 软件目录选择部分
        dir_layout = QVBoxLayout()
        dir_label = QLabel('软件目录:')
        dir_text = QLineEdit()
        dir_text.setReadOnly(True)
        dir_text.setText(self.software_directory)
        dir_button = QPushButton('批量导入目录')
        dir_button.clicked.connect(self.select_directory)
        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(dir_text)
        dir_layout.addWidget(dir_button)
        left_layout.addLayout(dir_layout)

        # 功能按钮区域
        self.open_dev_manager_btn = QPushButton('打开 网络适配器')
        self.open_dev_manager_btn.clicked.connect(self.open_device_manager_network)
        self.update_ax88179_driver_btn = QPushButton('更新AX88179网卡驱动')
        self.update_ax88179_driver_btn.clicked.connect(self.update_ax88179_driver)
        self.add_button = QPushButton('添加软件')
        self.add_button.clicked.connect(self.add_custom_software)
        # 添加按钮用于将软件添加到一键启动列表
        self.add_to_startup_btn = QPushButton('添加到一键启动')
        self.add_to_startup_btn.clicked.connect(self.add_to_startup_order)
        left_layout.addWidget(self.open_dev_manager_btn)
        left_layout.addWidget(self.update_ax88179_driver_btn)
        left_layout.addWidget(self.add_button)
        left_layout.addWidget(self.add_to_startup_btn)

        # 时间间隔设置
        interval_layout = QHBoxLayout()  # 改为水平布局，让标签和输入框横向排列
        interval_label = QLabel('启动间隔（秒）:')
        self.interval_input = QLineEdit(str(self.startup_interval))
        self.interval_input.editingFinished.connect(self.set_startup_interval)  # 绑定 editingFinished 信号
        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.interval_input)
        left_layout.addLayout(interval_layout)

        # 一键启动按钮
        start_btn = QPushButton('一键启动')
        start_btn.clicked.connect(self.one_click_start)
        left_layout.addWidget(start_btn)

        # 右侧编辑窗口布局
        right_layout = QVBoxLayout()

        # 备忘录区域
        memo_layout = QVBoxLayout()
        memo_label = QLabel('备忘录:')
        self.memo_text = QTextEdit()
        self.memo_text.setPlainText('请输入备忘录内容')
        # 添加右键菜单
        self.memo_text.setContextMenuPolicy(Qt.CustomContextMenu)
        self.memo_text.customContextMenuRequested.connect(self.show_memo_context_menu)
        memo_layout.addWidget(memo_label)
        memo_layout.addWidget(self.memo_text)
        right_layout.addLayout(memo_layout)

        # 软件列表区域 - 单独启动
        self.software_list_widget = QListWidget()
        self.software_list_widget.setSelectionMode(QListWidget.SingleSelection)
        self.software_list_widget.itemDoubleClicked.connect(self.launch_selected_software)
        # 绑定右键菜单事件
        self.software_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.software_list_widget.customContextMenuRequested.connect(self.show_single_launch_context_menu)
        right_layout.addWidget(self.software_list_widget)

        # 启动顺序设置和一键启动
        order_layout = QVBoxLayout()
        order_label = QLabel('设置启动顺序:')
        self.order_list_widget = QListWidget()
        self.order_list_widget.setSelectionMode(QListWidget.MultiSelection)
        self.order_list_widget.setDragEnabled(True)  # 启用拖拽
        self.order_list_widget.setDragDropMode(QListWidget.InternalMove)  # 设置内部移动模式
        for name in self.startup_order:
            self.order_list_widget.addItem(QListWidgetItem(name))
        # 绑定右键菜单事件
        self.order_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.order_list_widget.customContextMenuRequested.connect(self.show_one_click_launch_context_menu)

        order_layout.addWidget(order_label)
        order_layout.addWidget(self.order_list_widget)

        # 添加保存配置按钮
        save_order_config_btn = QPushButton('保存一键启动配置')
        save_order_config_btn.clicked.connect(self.save_config)
        order_layout.addWidget(save_order_config_btn)

        right_layout.addLayout(order_layout)

        # 将左右布局添加到主布局
        main_layout.addLayout(left_layout)
        main_layout.addLayout(right_layout)

        # 设置左右布局比例：按钮1，编辑框2
        main_layout.setStretch(0, 1)
        main_layout.setStretch(1, 2)

        self.setLayout(main_layout)

    def show_single_launch_context_menu(self, pos):
        item = self.software_list_widget.itemAt(pos)
        if item:
            menu = QMenu(self)
            open_dir_action = menu.addAction("打开目录")
            delete_action = menu.addAction("删除")
            action = menu.exec_(self.software_list_widget.mapToGlobal(pos))
            software_name = item.text()
            if action == open_dir_action:
                path = self.software_list.get(software_name)
                if path and os.path.exists(os.path.dirname(path)):
                    os.startfile(os.path.dirname(path))
                else:
                    QMessageBox.warning(self, "提示", "未找到该软件的目录")
            elif action == delete_action:
                if software_name in self.software_list:
                    del self.software_list[software_name]
                self.software_list_widget.takeItem(self.software_list_widget.row(item))
                self.save_config()

    def show_one_click_launch_context_menu(self, pos):
        item = self.order_list_widget.itemAt(pos)
        menu = QMenu(self)
        open_dir_action = menu.addAction("打开目录")
        delete_action = menu.addAction("删除")
        menu.addSeparator()
        create_config_action = menu.addAction("创建新配置")  # 新增

        action = menu.exec_(self.order_list_widget.mapToGlobal(pos))
        if item:
            software_name = item.text()
            if action == open_dir_action:
                path = self.software_list.get(software_name)
                if path and os.path.exists(os.path.dirname(path)):
                    os.startfile(os.path.dirname(path))
                else:
                    QMessageBox.warning(self, "提示", "未找到该软件的目录")
            elif action == delete_action:
                if software_name in self.startup_order:
                    self.startup_order.remove(software_name)
                self.order_list_widget.takeItem(self.order_list_widget.row(item))
                self.save_config()
        # 新增：创建新配置
        if action == create_config_action:
            self.create_new_config()

    def select_directory(self):
        initial_dir = self.current_drive + '\\'
        self.software_directory = QFileDialog.getExistingDirectory(self, "选择软件目录", initial_dir)
        if self.software_directory:
            self.save_config()
            self.load_software_from_directory()

    def add_custom_software(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择EXE、INF或BAT文件', '', 'Executable Files (*.exe);;INF Files (*.inf);;BAT Files (*.bat)')
        if file_path:
            try:
                software_name = os.path.basename(file_path).split(".")[0]
                # 保存规范化后的路径
                self.software_list[software_name] = os.path.normpath(file_path)
                self.software_list_widget.addItem(software_name)
                self.save_config()
            except Exception as e:
                QMessageBox.critical(self, '错误', f'无法处理该文件：{e}')

    def create_button(self, name, path):
        btn = QPushButton(f'启动: {name}')
        btn.clicked.connect(lambda _, p=path: self.launch_software(p))
        self.layout.addWidget(btn)

    def launch_software(self, path):
        try:
            normalized_path = os.path.normpath(path)
            working_dir = os.path.dirname(normalized_path)
            
            if normalized_path.lower().endswith(('.inf', '.bat')):
                subprocess.Popen(['cmd.exe', '/c', f'"{normalized_path}"'], shell=True)
            elif normalized_path.lower().endswith('.exe'):
                try:
                    # 尝试以管理员身份启动
                    ctypes.windll.shell32.ShellExecuteW(None, "runas", normalized_path, "", working_dir, 1)
                except Exception:
                    # 若失败，以普通方式启动
                    subprocess.Popen([f'"{normalized_path}"'], shell=True, cwd=working_dir)
            else:
                os.startfile(normalized_path)
        except Exception as e:
            QMessageBox.critical(self, '错误', f'无法启动软件: {str(e)}')

    def launch_selected_software(self, item):
        name = item.text()
        path = self.software_list.get(name)
        if path:
            self.launch_software(path)

    def set_startup_interval(self):
        try:
            interval = int(self.interval_input.text())
            self.startup_interval = interval
            QMessageBox.information(self, '提示', '启动间隔设置成功')
            self.save_config()
        except ValueError:
            QMessageBox.critical(self, '错误', '请输入有效的整数')
            self.interval_input.setText(str(self.startup_interval))  # 恢复之前的有效数值

    def one_click_start(self):
        # 获取当前列表中的所有项（按显示顺序）
        startup_order = []
        for i in range(self.order_list_widget.count()):
            startup_order.append(self.order_list_widget.item(i).text())

        if not startup_order:
            QMessageBox.warning(self, '警告', '请先选择要启动的软件')
            return

        for name in startup_order:
            path = self.software_list.get(name)
            if path:
                self.launch_software(path)
                time.sleep(self.startup_interval)

    def add_to_startup_order(self):
        selected_items = self.software_list_widget.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, '警告', '请先选择要添加到一键启动的软件')
            return

        for item in selected_items:
            software_name = item.text()
            if software_name not in self.startup_order:
                self.startup_order.append(software_name)
                self.order_list_widget.addItem(QListWidgetItem(software_name))

        self.save_config()

    def save_config(self):
        # 检查配置文件是否存在
        if not os.path.exists(self.config_file):
            # 创建默认配置文件
            default_config = configparser.ConfigParser()
            default_config['Configurations'] = {
                'names': '默认配置',
                'current': '默认配置'
            }
            default_config['Memo'] = {
                'text': '请输入备忘录内容'
            }
            default_config['Settings'] = {
                'startup_interval': '1'
            }
            default_config['默认配置'] = {
                'startup_order': ''
            }

            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    default_config.write(f)
                print(f"已自动创建默认配置文件: {self.config_file}")
            except Exception as e:
                QMessageBox.critical(self, '错误', f'创建配置文件失败: {str(e)}')
                return

        # 读取现有配置
        config, _ = read_config(self.config_file)

        # 更新软件列表
        for name, path in self.software_list.items():
            if name in config:
                config[name]['path'] = path
            else:
                config[name] = {'path': path}

        # 更新备忘录
        memo_text = self.memo_text.toPlainText()
        if memo_text:
            config['Memo'] = {'text': memo_text}

        # 获取当前列表顺序
        current_order = [self.order_list_widget.item(i).text() for i in range(self.order_list_widget.count())]

        # 更新设置和启动顺序
        config['Settings'] = {'software_directory': self.software_directory, 'startup_interval': str(self.startup_interval)}
        config['StartupOrder'] = {'order': ','.join(current_order)}

        # 获取当前配置名称
        current_config = '默认配置'
        if 'Configurations' in config and 'current' in config['Configurations']:
            current_config = config['Configurations']['current']

        # 确保Configurations节存在
        if 'Configurations' not in config:
            config['Configurations'] = {}
            config['Configurations']['names'] = '默认配置'
            config['Configurations']['current'] = '默认配置'

        # 确保当前配置节存在
        if current_config not in config:
            config[current_config] = {}

        # 更新当前配置的启动顺序
        config[current_config]['startup_order'] = ','.join(current_order)

        # 保存配置
        if write_config(config, self.config_file):
            # QMessageBox.information(self, '成功', f'已保存配置：{current_config}')  # 注释掉或删除这行
            self.config_saved.emit()  # 保存后发出信号
        else:
            QMessageBox.critical(self, '错误', '保存配置时出错')

    def load_config(self):
        config = configparser.ConfigParser()
        try:
            config.read(self.config_file, encoding='utf-8')
        except UnicodeDecodeError:
            config.read(self.config_file, encoding='gbk')

        if 'Memo' in config and 'text' in config['Memo']:
            self.memo_text.setPlainText(config['Memo']['text'])

        if 'Settings' in config and 'startup_interval' in config['Settings']:
            try:
                self.startup_interval = int(config['Settings']['startup_interval'])
                self.interval_input.setText(str(self.startup_interval))
            except ValueError:
                pass

        # 获取当前配置名
        current_config = '默认配置'
        if 'Configurations' in config and 'current' in config['Configurations']:
            current_config = config['Configurations']['current']

        # 优先从当前配置加载启动顺序
        if current_config in config and 'startup_order' in config[current_config]:
            self.startup_order = config[current_config]['startup_order'].split(',')
            self.startup_order = [name for name in self.startup_order if name]
        # 如果当前配置没有启动顺序，则从StartupOrder节加载
        elif 'StartupOrder' in config and 'order' in config['StartupOrder']:
            self.startup_order = config['StartupOrder']['order'].split(',')
            self.startup_order = [name for name in self.startup_order if name]
        else:
            self.startup_order = []

        # 清空现有软件列表
        self.software_list = {}
        self.software_list_widget.clear()
        
        # 加载软件列表
        for section in config.sections():
            # 排除特殊节和配置节
            if section in ['Memo', 'Settings', 'StartupOrder', 'Configurations'] or section.startswith('配置') or section == '默认配置':
                continue
            name = section
            path = config[section].get('path')
            if path and os.path.isfile(path):
                self.software_list[name] = path
                self.software_list_widget.addItem(name)

        # 更新启动顺序列表
        self.order_list_widget.clear()
        for name in self.startup_order:
            if name in self.software_list:
                self.order_list_widget.addItem(QListWidgetItem(name))

    def load_software_from_directory(self):
        if not os.path.exists(self.software_directory):
            return

        self.software_list_widget.clear()
        self.software_list = {}
        for root, dirs, files in os.walk(self.software_directory):
            for file in files:
                if file.lower().endswith(('.exe', '.inf', '.bat')):
                    file_path = os.path.join(root, file)
                    software_name = os.path.basename(file_path).split(".")[0]
                    self.software_list[software_name] = file_path
                    self.software_list_widget.addItem(software_name)
        self.save_config()

    def open_device_manager_network(self):
        try:
            os.system('devmgmt.msc /select @pci.sys,#{4d36e972-e325-11ce-bfc1-08002be10318}')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'无法打开设备管理器: {str(e)}')

    def update_ax88179_driver(self):
        try:
            powershell_script = """
            $device = Get-PnpDevice | Where-Object {$_.Name -like "*ASIX AX88179*"}
            if ($device) {
                $device | Update-PnpDevice -DriverUpdateLocation "C:\\Windows\\System32\\DriverStore\\FileRepository" -Confirm:$false
            } else {
                Write-Output "未找到 ASIX AX88179 网卡设备"
            }
            """
            result = subprocess.run(['powershell', '-Command', powershell_script], capture_output=True, text=True)
            if "未找到 ASIX AX88179 网卡设备" in result.stdout:
                QMessageBox.warning(self, '警告', '未找到 ASIX AX88179 网卡设备')
            else:
                QMessageBox.information(self, '提示', '已尝试更新 ASIX AX88179 网卡驱动')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'更新驱动时出错: {str(e)}')

    def show_memo_context_menu(self, pos):
        menu = QMenu(self)
        copy_action = menu.addAction("复制")
        paste_action = menu.addAction("粘贴")
        clear_action = menu.addAction("清空")
        action = menu.exec_(self.memo_text.mapToGlobal(pos))
        if action == copy_action:
            self.memo_text.copy()
        elif action == paste_action:
            self.memo_text.paste()
        elif action == clear_action:
            self.memo_text.clear()

    def create_new_config(self):
        config_name, ok = QInputDialog.getText(self, '创建新配置', '请输入新配置名称:')
        if not ok or not config_name.strip():
            return
        config_name = config_name.strip()

        config, success = read_config(self.config_file)
        if not success:
            QMessageBox.critical(self, '错误', '读取配置文件失败')
            return

        # 检查Configurations节
        if 'Configurations' not in config:
            config['Configurations'] = {'names': config_name, 'current': config_name}
        else:
            names = [n.strip() for n in config['Configurations'].get('names', '').split(',') if n.strip()]
            if config_name in names:
                QMessageBox.warning(self, '警告', f'配置"{config_name}"已存在')
                return
            names.append(config_name)
            config['Configurations']['names'] = ','.join(names)
            config['Configurations']['current'] = config_name

        # 保存当前顺序到新配置节
        current_order = [self.order_list_widget.item(i).text() for i in range(self.order_list_widget.count())]
        config[config_name] = {'startup_order': ','.join(current_order)}

        if write_config(config, self.config_file):
            QMessageBox.information(self, '成功', f'新配置"{config_name}"已创建')
            self.config_saved.emit()
            # 自动切换到新建配置（可选）
            # 你可以在主窗口的 load_configurations 里加逻辑自动选中 config_name
        else:
            QMessageBox.critical(self, '错误', '保存配置失败')

    def show_error(self, title, message):
        """统一的错误显示函数"""
        QMessageBox.critical(self, title, message)
        print(f"错误: {title} - {message}")

    def show_warning(self, title, message):
        """统一的警告显示函数"""
        QMessageBox.warning(self, title, message)
        print(f"警告: {title} - {message}")

    def show_info(self, title, message):
        """统一的信息显示函数"""
        QMessageBox.information(self, title, message)
        print(f"信息: {title} - {message}")


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.secondary_window = None
        self.config_combo = None
        
        # 使用当前脚本所在目录的config.ini
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
        print(f"配置文件路径: {self.config_file}")
        
        # 初始化UI
        self.initUI()
        
        # 加载配置列表到下拉框
        self.load_configurations()
        
        # 预加载当前配置
        self._preload_current_config()

    def _preload_current_config(self):
        """预加载当前配置"""
        current_config = self.get_current_config()
        if current_config:
            print(f"预加载配置: {current_config}")
            # 创建二级窗口但不显示
            self.secondary_window = SoftwareLauncher()
            self.secondary_window.config_saved.connect(self.load_configurations)
            self.secondary_window.hide()
            # 预加载配置
            self.load_config_for_secondary_window(current_config)
    
    def create_default_config(self):
        """创建默认配置文件，但不添加新配置"""
        print("正在创建基本配置文件...")
        
        # 检查配置文件是否已存在
        if os.path.exists(self.config_file):
            # print(f"配置文件已存在: {self.config_file}")
            return
            
        config = configparser.ConfigParser()
        
        # 确保Configurations节存在
        if not config.has_section('Configurations'):
            config.add_section('Configurations')
            
        # 只有当names字段不存在或为空时，才添加默认配置
        if 'names' not in config['Configurations'] or not config['Configurations'].get('names', '').strip():
            config['Configurations']['names'] = '默认配置'
            
        # 只有当current字段不存在或为空时，才设置当前配置
        if 'current' not in config['Configurations'] or not config['Configurations'].get('current', '').strip():
            config['Configurations']['current'] = '默认配置'
        
        # 确保默认配置节存在
        if '默认配置' not in config:
            config['默认配置'] = {'startup_order': ''}
        
        # 添加基本设置
        if 'Memo' not in config:
            config['Memo'] = {'text': '请输入备忘录内容'}
        if 'Settings' not in config:
            config['Settings'] = {'startup_interval': '1'}
        
        # 保存配置文件
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            print(f"创建了默认配置文件: {self.config_file}")
        except Exception as e:
            print(f"创建默认配置文件失败: {str(e)}")
    
    def get_current_config(self):
        """获取当前配置名称"""
        if not os.path.exists(self.config_file):
            print(f"配置文件不存在: {self.config_file}")
            return None
            
        try:
            config = configparser.ConfigParser()
            # 尝试使用UTF-8编码读取
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config.read_file(f)
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试GBK编码
                with open(self.config_file, 'r', encoding='gbk') as f:
                    config.read_file(f)
                    
            if 'Configurations' in config and 'current' in config['Configurations']:
                return config['Configurations']['current']
        except Exception as e:
            print(f"读取当前配置失败: {str(e)}")
        return None

    def initUI(self):
        self.setWindowTitle('启动')
        self.setGeometry(800, 500, 200, 200)

        layout = QVBoxLayout()

        # 配置选择区域 - 完全重写
        config_layout = QHBoxLayout()
        config_label = QLabel('选择配置:')
        config_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        
        # 创建新的下拉框并设置样式
        self.config_combo = QComboBox(self)
        self.config_combo.setMinimumWidth(150)  # 增加最小宽度
        self.config_combo.setMinimumHeight(30)  # 设置最小高度
        self.config_combo.setEditable(False)    # 不可编辑
        self.config_combo.setFocusPolicy(Qt.StrongFocus)  # 强制焦点策略
        
        # 简化样式，移除可能导致问题的样式设置
        self.config_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #4CAF50;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #4CAF50;
                selection-background-color: #4CAF50;
                selection-color: white;
                background-color: white;
                padding: 5px;
            }
        """)
        
        # 确保信号连接正确
        self.config_combo.currentIndexChanged.connect(self.on_config_changed)
        print("已连接配置选择框的信号到on_config_changed方法")
        
        # 移除加载配置按钮的代码
        
        config_layout.addWidget(config_label)
        config_layout.addWidget(self.config_combo, 1)  # 给下拉框分配更多空间
        layout.addLayout(config_layout)

        # 一键启动按钮
        one_click_start_btn = QPushButton('一键启动')
        one_click_start_btn.clicked.connect(self.one_click_start)
        # 设置按钮样式
        one_click_start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(one_click_start_btn)

        # 二级界面启动按钮
        open_secondary_btn = QPushButton('设置')
        open_secondary_btn.clicked.connect(self.open_secondary_window)
        # 设置按钮样式
        open_secondary_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(open_secondary_btn)

        self.setLayout(layout)

    def load_configurations(self):
        # 确保配置下拉框已初始化
        if not hasattr(self, 'config_combo') or self.config_combo is None:
            print("配置下拉框未初始化")
            return
            
        print("开始加载配置列表...")
        
        # 保存当前选中的配置名称
        current_selected = self.config_combo.currentText() if self.config_combo.currentIndex() >= 0 else ''
        print(f"当前选中的配置: {current_selected}")
        
        # 清空下拉框并阻止信号触发
        self.config_combo.blockSignals(True)
        self.config_combo.clear()
        
        # 读取配置文件
        config, success = read_config(self.config_file)
        
        if not success:
            print("配置文件不存在或读取失败，不自动创建配置")
            self.config_combo.blockSignals(False)
            self.config_combo.setEnabled(True)
            self.config_combo.setVisible(True)
            return
        
        # 检查必要的配置节和字段
        if 'Configurations' not in config or 'names' not in config['Configurations'] or not config['Configurations']['names'].strip():
            print("配置文件格式不正确或配置列表为空")
            self.config_combo.blockSignals(False)
            self.config_combo.setEnabled(True)
            self.config_combo.setVisible(True)
            return
        
        # 加载配置列表到下拉框
        configurations = [name.strip() for name in config['Configurations']['names'].split(',') if name.strip()]
        
        if not configurations:
            print("配置列表为空")
            self.config_combo.blockSignals(False)
            self.config_combo.setEnabled(True)
            self.config_combo.setVisible(True)
            return
        
        # 添加配置到下拉框，确保无重复
        added_configs = set()
        for config_name in configurations:
            if config_name and config_name not in added_configs:
                self.config_combo.addItem(config_name)
                added_configs.add(config_name)
                print(f"添加配置选项: {config_name}")
        
        # 设置当前选中的配置
        current_config = config['Configurations'].get('current', '')
        print(f"配置文件中的当前配置: {current_config}")
        
        # 选择合适的配置项
        self._select_appropriate_config(current_config, current_selected)
        
        # 恢复信号处理
        self.config_combo.blockSignals(False)
        print(f"配置加载完成，共 {self.config_combo.count()} 个配置")
        
        # 确保下拉框可见并可交互
        self.config_combo.setEnabled(True)
        self.config_combo.setVisible(True)

    def _select_appropriate_config(self, current_config, previous_selected):
        """选择合适的配置项"""
        if current_config:
            index = self.config_combo.findText(current_config)
            if index >= 0:
                self.config_combo.setCurrentIndex(index)
                print(f"设置当前选择为: {current_config}")
                return
            else:
                print(f"找不到配置: {current_config}")
        
        # 尝试恢复之前选中的配置
        if previous_selected:
            index = self.config_combo.findText(previous_selected)
            if index >= 0:
                self.config_combo.setCurrentIndex(index)
                print(f"恢复之前的选择: {previous_selected}")
                return
        
        # 默认选择第一项
        if self.config_combo.count() > 0:
            self.config_combo.setCurrentIndex(0)
            print(f"设置为第一个配置: {self.config_combo.itemText(0)}")

    def on_config_changed(self, index):
        # 检查索引是否有效
        if index < 0 or self.config_combo.count() <= 0:
            return
        
        config_name = self.config_combo.currentText()
        print(f"配置选择已更改为: {config_name}")
        
        # 读取配置文件
        config, success = read_config(self.config_file)
        if not success:
            QMessageBox.critical(self, '错误', f'读取配置文件失败')
            return
        
        # 检查必要的配置节和字段
        if 'Configurations' not in config:
            QMessageBox.critical(self, '错误', '配置文件格式错误：缺少Configurations节')
            return
        
        if 'names' not in config['Configurations']:
            QMessageBox.critical(self, '错误', '配置文件格式错误：缺少names字段')
            return
        
        # 获取配置名称列表并确保所选配置存在于列表中
        names = [name.strip() for name in config['Configurations'].get('names', '').split(',') if name.strip()]
        
        if config_name not in names:
            names.append(config_name)
            config['Configurations']['names'] = ','.join(names)
            print(f"已将配置 {config_name} 添加到配置列表")
        
        # 更新当前配置
        config['Configurations']['current'] = config_name
        print(f"已将当前配置更新为: {config_name}")
        
        # 确保该配置节存在
        if config_name not in config:
            config[config_name] = {'startup_order': ''}
            print(f"创建了配置节: {config_name}")
        
        # 保存配置
        if not write_config(config, self.config_file):
            QMessageBox.critical(self, '错误', '保存配置文件失败')
            return
        
        # 自动加载选中的配置
        self.load_selected_config()

    def load_config_for_secondary_window(self, config_name):
        config = configparser.ConfigParser()
        try:
            config.read(self.config_file, encoding='utf-8')
        except UnicodeDecodeError:
            config.read(self.config_file, encoding='gbk')
            
        # 确保二级窗口已加载软件列表
        if not hasattr(self.secondary_window, 'software_list') or not self.secondary_window.software_list:
            self.secondary_window.load_config()
            
        # 清空当前启动顺序列表
        self.secondary_window.order_list_widget.clear()
        self.secondary_window.startup_order = []
            
        if config_name in config and 'startup_order' in config[config_name]:
            # 加载该配置的启动顺序
            startup_order = config[config_name]['startup_order'].split(',')
            self.secondary_window.startup_order = [name for name in startup_order if name]
            
            # 只添加存在于软件列表中的项目
            for name in self.secondary_window.startup_order:
                if name in self.secondary_window.software_list:
                    self.secondary_window.order_list_widget.addItem(QListWidgetItem(name))
        
        # 更新启动间隔显示
        if 'Settings' in config and 'startup_interval' in config['Settings']:
            try:
                interval = int(config['Settings']['startup_interval'])
                self.secondary_window.startup_interval = interval
                self.secondary_window.interval_input.setText(str(interval))
            except ValueError:
                pass
                
        # 更新备忘录内容
        if 'Memo' in config and 'text' in config['Memo']:
            self.secondary_window.memo_text.setPlainText(config['Memo']['text'])

    def one_click_start(self):
        if not self.secondary_window:
            self.secondary_window = SoftwareLauncher()
            self.secondary_window.config_saved.connect(self.load_configurations)  # 连接信号
            self.secondary_window.hide()  # 不显示窗口
        config_name = self.config_combo.currentText()
        self.load_config_for_secondary_window(config_name)
        self.secondary_window.one_click_start()

    def open_secondary_window(self):
        if not self.secondary_window:
            self.secondary_window = SoftwareLauncher()
            self.secondary_window.config_saved.connect(self.load_configurations)  # 连接信号
        self.secondary_window.show()
        
    def load_selected_config(self):
        """加载选定的配置到二级窗口"""
        # 检查是否有可用配置
        if self.config_combo.count() <= 0:
            QMessageBox.warning(self, '警告', '没有可用的配置')
            return
        
        # 检查是否有选中的配置
        if self.config_combo.currentIndex() < 0:
            QMessageBox.warning(self, '警告', '请选择一个配置')
            return
            
        config_name = self.config_combo.currentText()
        print(f"正在加载配置: {config_name}")
            
        # 确保配置存在于配置文件中
        config = configparser.ConfigParser()
        try:
            # 尝试使用UTF-8编码读取
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config.read_file(f)
        except UnicodeDecodeError:
            # 如果UTF-8失败，尝试GBK编码
            try:
                with open(self.config_file, 'r', encoding='gbk') as f:
                    config.read_file(f)
            except Exception as e:
                QMessageBox.critical(self, '错误', f'读取配置文件失败: {str(e)}')
                return
        except FileNotFoundError:
            QMessageBox.critical(self, '错误', f'配置文件不存在: {self.config_file}')
            return
        except Exception as e:
            QMessageBox.critical(self, '错误', f'读取配置文件时发生未知错误: {str(e)}')
            return
        
        # 检查配置是否存在
        if 'Configurations' not in config or 'names' not in config['Configurations']:
            QMessageBox.warning(self, '警告', '配置文件格式错误')
            return
            
        names = config['Configurations'].get('names', '').split(',')
        names = [name.strip() for name in names if name.strip()]
        
        if config_name not in names:
            QMessageBox.warning(self, '警告', f'配置 "{config_name}" 不在配置列表中')
            return
        
        # 创建二级窗口（如果不存在）
        if not self.secondary_window:
            self.secondary_window = SoftwareLauncher()
            self.secondary_window.config_saved.connect(self.load_configurations)  # 连接信号
        
        # 更新配置文件中的当前配置
        config['Configurations']['current'] = config_name
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            print(f"已将当前配置更新为: {config_name}")
        except Exception as e:
            QMessageBox.critical(self, '错误', f'保存配置文件失败: {str(e)}')
            return
        
        # 加载配置到二级窗口
        self.load_config_for_secondary_window(config_name)
        # QMessageBox.information(self, '成功', f'已加载配置: {config_name}')  # 注释掉或删除这行
        
        # 如果二级窗口已打开，则更新其UI
        if self.secondary_window.isVisible():
            self.secondary_window.order_list_widget.update()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())