#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理失败问题诊断和修复工具
系统性诊断批量零件编号处理工具中的Excel处理问题
"""

import os
import sys
import pandas as pd
import shutil
import glob
import traceback
from datetime import datetime
import openpyxl
from openpyxl import load_workbook
import psutil
import stat

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

class ExcelFailureDiagnostic:
    """Excel处理失败诊断类"""

    def __init__(self):
        self.diagnostic_results = {}
        self.error_log = []
        self.fix_recommendations = []

    def run_comprehensive_diagnostic(self, excel_file_path=None, sheet_name=None,
                                   column_name=None, part_names=None):
        """运行全面的Excel处理诊断"""
        print("=" * 80)
        print("Excel文件处理失败问题 - 全面诊断工具")
        print("=" * 80)

        # 如果没有提供参数，创建测试环境
        if not excel_file_path:
            excel_file_path, sheet_name, column_name, part_names = self.create_test_environment()

        print(f"\n诊断目标:")
        print(f"  Excel文件: {excel_file_path}")
        print(f"  工作表: {sheet_name}")
        print(f"  目标列: {column_name}")
        print(f"  零件名称: {part_names}")

        # 执行分步诊断
        self.step1_file_existence_check(excel_file_path)
        self.step2_file_permissions_check(excel_file_path)
        self.step3_file_access_check(excel_file_path)
        self.step4_excel_structure_check(excel_file_path, sheet_name, column_name)
        self.step5_data_format_check(excel_file_path, sheet_name, column_name)
        self.step6_matching_logic_check(excel_file_path, sheet_name, column_name, part_names)
        self.step7_write_operations_check(excel_file_path, sheet_name, column_name)
        self.step8_full_process_simulation(excel_file_path, sheet_name, column_name, part_names)

        # 生成诊断报告
        self.generate_diagnostic_report()

        # 提供修复方案
        self.provide_fix_solutions()

        return self.diagnostic_results

    def create_test_environment(self):
        """创建测试环境"""
        print(f"\n{'='*20} 创建测试环境 {'='*20}")

        test_dir = os.path.join(os.path.dirname(__file__), "test_data", "excel_failure_test")
        os.makedirs(test_dir, exist_ok=True)

        # 创建测试Excel文件
        data = {
            '序号': [1, 2, 3, 4, 5],
            '零件编号': ['LP20', '2-C38', 'D12', 'A-E56', 'F23'],
            '零件名称': ['支撑板', '连接件', '固定块', '支架', '垫片'],
            '材质': ['Q235', 'Q345', 'Q235', 'Q345', 'Q235'],
            '数量': [2, 4, 1, 3, 8],
            '备注': ['', '', '', '', '']
        }

        df = pd.DataFrame(data)
        excel_file = os.path.join(test_dir, "失败诊断测试.xlsx")

        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Sheet1', index=False)

        print(f"✓ 创建测试Excel文件: {excel_file}")

        return excel_file, 'Sheet1', '零件编号', ['LP20A', 'C38B', 'D12C', 'E56D', 'F23E']

    def step1_file_existence_check(self, excel_file_path):
        """步骤1：检查文件存在性"""
        print(f"\n{'='*20} 步骤1：文件存在性检查 {'='*20}")

        try:
            if os.path.exists(excel_file_path):
                print(f"✓ 文件存在: {excel_file_path}")

                # 检查文件大小
                file_size = os.path.getsize(excel_file_path)
                print(f"✓ 文件大小: {file_size} 字节")

                if file_size == 0:
                    self.error_log.append("文件大小为0，可能是空文件")
                    self.fix_recommendations.append("检查文件是否正确创建，重新生成Excel文件")

                # 检查文件扩展名
                file_ext = os.path.splitext(excel_file_path)[1].lower()
                if file_ext not in ['.xlsx', '.xls', '.xlsm']:
                    self.error_log.append(f"文件扩展名不正确: {file_ext}")
                    self.fix_recommendations.append("确保文件扩展名为.xlsx、.xls或.xlsm")
                else:
                    print(f"✓ 文件扩展名正确: {file_ext}")

                self.diagnostic_results['file_exists'] = True

            else:
                print(f"✗ 文件不存在: {excel_file_path}")
                self.error_log.append(f"Excel文件不存在: {excel_file_path}")
                self.fix_recommendations.append("检查文件路径是否正确，确保文件存在")
                self.diagnostic_results['file_exists'] = False

        except Exception as e:
            print(f"✗ 文件存在性检查失败: {str(e)}")
            self.error_log.append(f"文件存在性检查异常: {str(e)}")
            self.diagnostic_results['file_exists'] = False

    def step2_file_permissions_check(self, excel_file_path):
        """步骤2：检查文件权限"""
        print(f"\n{'='*20} 步骤2：文件权限检查 {'='*20}")

        if not self.diagnostic_results.get('file_exists', False):
            print("跳过权限检查（文件不存在）")
            return

        try:
            # 检查读权限
            if os.access(excel_file_path, os.R_OK):
                print(f"✓ 文件可读")
                self.diagnostic_results['file_readable'] = True
            else:
                print(f"✗ 文件不可读")
                self.error_log.append("文件没有读权限")
                self.fix_recommendations.append("检查文件权限，确保程序有读取权限")
                self.diagnostic_results['file_readable'] = False

            # 检查写权限
            if os.access(excel_file_path, os.W_OK):
                print(f"✓ 文件可写")
                self.diagnostic_results['file_writable'] = True
            else:
                print(f"✗ 文件不可写")
                self.error_log.append("文件没有写权限")
                self.fix_recommendations.append("检查文件权限，确保程序有写入权限")
                self.diagnostic_results['file_writable'] = False

            # 检查文件属性
            file_stat = os.stat(excel_file_path)
            file_mode = stat.filemode(file_stat.st_mode)
            print(f"✓ 文件权限: {file_mode}")

            # 检查文件是否被锁定
            if os.name == 'nt':  # Windows系统
                try:
                    # 尝试以独占模式打开文件
                    with open(excel_file_path, 'r+b') as f:
                        pass
                    print(f"✓ 文件未被锁定")
                    self.diagnostic_results['file_locked'] = False
                except IOError:
                    print(f"✗ 文件可能被其他程序锁定")
                    self.error_log.append("文件被其他程序锁定（可能被Excel打开）")
                    self.fix_recommendations.append("关闭所有打开该文件的程序（如Excel）")
                    self.diagnostic_results['file_locked'] = True

        except Exception as e:
            print(f"✗ 权限检查失败: {str(e)}")
            self.error_log.append(f"权限检查异常: {str(e)}")

    def step3_file_access_check(self, excel_file_path):
        """步骤3：检查文件访问状态"""
        print(f"\n{'='*20} 步骤3：文件访问状态检查 {'='*20}")

        if not self.diagnostic_results.get('file_exists', False):
            print("跳过访问检查（文件不存在）")
            return

        try:
            # 检查文件是否被进程占用
            file_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                try:
                    if proc.info['open_files']:
                        for file_info in proc.info['open_files']:
                            if file_info.path == excel_file_path:
                                file_processes.append({
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name']
                                })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if file_processes:
                print(f"✗ 文件被以下进程占用:")
                for proc in file_processes:
                    print(f"    PID: {proc['pid']}, 进程: {proc['name']}")
                self.error_log.append(f"文件被 {len(file_processes)} 个进程占用")
                self.fix_recommendations.append("关闭占用文件的进程，特别是Excel程序")
                self.diagnostic_results['file_in_use'] = True
            else:
                print(f"✓ 文件未被其他进程占用")
                self.diagnostic_results['file_in_use'] = False

            # 检查磁盘空间
            disk_usage = shutil.disk_usage(os.path.dirname(excel_file_path))
            free_space_mb = disk_usage.free / (1024 * 1024)
            print(f"✓ 磁盘剩余空间: {free_space_mb:.1f} MB")

            if free_space_mb < 100:  # 少于100MB
                self.error_log.append(f"磁盘空间不足: {free_space_mb:.1f} MB")
                self.fix_recommendations.append("清理磁盘空间，确保有足够的空间进行文件操作")
                self.diagnostic_results['disk_space_ok'] = False
            else:
                self.diagnostic_results['disk_space_ok'] = True

        except Exception as e:
            print(f"✗ 文件访问检查失败: {str(e)}")
            self.error_log.append(f"文件访问检查异常: {str(e)}")

    def step4_excel_structure_check(self, excel_file_path, sheet_name, column_name):
        """步骤4：检查Excel结构"""
        print(f"\n{'='*20} 步骤4：Excel结构检查 {'='*20}")

        if not self.diagnostic_results.get('file_readable', False):
            print("跳过结构检查（文件不可读）")
            return

        try:
            # 使用pandas读取Excel文件
            print(f"测试pandas读取...")
            try:
                excel_file_obj = pd.ExcelFile(excel_file_path)
                available_sheets = excel_file_obj.sheet_names
                print(f"✓ pandas读取成功")
                print(f"✓ 可用工作表: {available_sheets}")

                # 检查目标工作表是否存在
                if sheet_name in available_sheets:
                    print(f"✓ 目标工作表存在: {sheet_name}")
                    self.diagnostic_results['sheet_exists'] = True
                else:
                    print(f"✗ 目标工作表不存在: {sheet_name}")
                    self.error_log.append(f"工作表 '{sheet_name}' 不存在")
                    self.fix_recommendations.append(f"检查工作表名称，可用工作表: {available_sheets}")
                    self.diagnostic_results['sheet_exists'] = False
                    return

                # 读取工作表数据
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                print(f"✓ 工作表数据读取成功")
                print(f"✓ 数据行数: {len(df)}")
                print(f"✓ 数据列数: {len(df.columns)}")
                print(f"✓ 列名: {list(df.columns)}")

                # 检查目标列是否存在
                if column_name in df.columns:
                    print(f"✓ 目标列存在: {column_name}")
                    self.diagnostic_results['column_exists'] = True

                    # 检查列数据
                    column_data = df[column_name]
                    print(f"✓ 目标列数据: {list(column_data)}")
                    print(f"✓ 目标列数据类型: {column_data.dtype}")

                    # 检查空值
                    null_count = column_data.isnull().sum()
                    if null_count > 0:
                        print(f"⚠ 目标列包含 {null_count} 个空值")
                        self.fix_recommendations.append(f"目标列包含空值，可能影响匹配")

                else:
                    print(f"✗ 目标列不存在: {column_name}")
                    self.error_log.append(f"列 '{column_name}' 不存在")
                    self.fix_recommendations.append(f"检查列名，可用列名: {list(df.columns)}")
                    self.diagnostic_results['column_exists'] = False

            except Exception as e:
                print(f"✗ pandas读取失败: {str(e)}")
                self.error_log.append(f"pandas读取Excel失败: {str(e)}")
                self.diagnostic_results['pandas_read_ok'] = False

            # 使用openpyxl读取Excel文件
            print(f"\n测试openpyxl读取...")
            try:
                wb = load_workbook(excel_file_path)
                print(f"✓ openpyxl读取成功")
                print(f"✓ 工作表列表: {wb.sheetnames}")

                if sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    print(f"✓ 工作表访问成功: {sheet_name}")
                    print(f"✓ 数据范围: {ws.dimensions}")
                    self.diagnostic_results['openpyxl_read_ok'] = True
                else:
                    print(f"✗ openpyxl无法访问工作表: {sheet_name}")
                    self.diagnostic_results['openpyxl_read_ok'] = False

            except Exception as e:
                print(f"✗ openpyxl读取失败: {str(e)}")
                self.error_log.append(f"openpyxl读取Excel失败: {str(e)}")
                self.diagnostic_results['openpyxl_read_ok'] = False

        except Exception as e:
            print(f"✗ Excel结构检查失败: {str(e)}")
            self.error_log.append(f"Excel结构检查异常: {str(e)}")
            traceback.print_exc()