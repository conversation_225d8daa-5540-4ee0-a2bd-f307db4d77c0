#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MAC地址修改自动化工具 - 主启动脚本
提供统一的入口点，用户可以选择使用命令行或图形界面
"""

import os
import sys
import argparse
from typing import Optional

def print_banner():
    """打印工具横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                MAC地址修改自动化工具                          ║
║                                                              ║
║  功能特性:                                                    ║
║  • 自动化控制MAC修改软件                                      ║
║  • 支持随机生成、厂商指定、自定义MAC地址                      ║
║  • 提供命令行和图形界面                                       ║
║  • 完整的历史记录和错误处理                                   ║
║                                                              ║
║  使用方法:                                                    ║
║  python main.py --cli     # 启动命令行界面                   ║
║  python main.py --gui     # 启动图形界面                     ║
║  python main.py --help    # 查看帮助信息                     ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies() -> bool:
    """检查依赖是否已安装"""
    required_modules = ['pyautogui', 'pywinauto']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ 缺少必需的依赖包:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n请运行以下命令安装依赖:")
        print("   python install_dependencies.py")
        print("或者:")
        print("   pip install pyautogui pywinauto Pillow")
        return False
    
    return True

def show_interface_selection():
    """显示界面选择菜单"""
    print("\n请选择要使用的界面:")
    print("1. 命令行界面 (CLI) - 推荐")
    print("2. 图形用户界面 (GUI)")
    print("3. 安装/检查依赖")
    print("4. 运行测试")
    print("5. 查看帮助")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-5): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5']:
                return choice
            else:
                print("❌ 无效选择，请输入0-5之间的数字")
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            return '0'

def launch_cli():
    """启动命令行界面"""
    try:
        print("🚀 启动命令行界面...")
        # 清空命令行参数，避免冲突
        original_argv = sys.argv[:]
        sys.argv = ['mac_cli.py']

        try:
            from mac_cli import MACAutomationCLI
            cli = MACAutomationCLI()
            cli.run()
        finally:
            sys.argv = original_argv

    except ImportError as e:
        print(f"❌ 无法导入命令行界面模块: {e}")
        print("请确保 mac_cli.py 文件存在")
    except Exception as e:
        print(f"❌ 启动命令行界面时发生错误: {e}")

def launch_gui():
    """启动图形用户界面"""
    try:
        print("🚀 启动图形用户界面...")
        from mac_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"❌ 无法导入图形界面模块: {e}")
        print("请确保 mac_gui.py 文件存在")
    except Exception as e:
        print(f"❌ 启动图形界面时发生错误: {e}")

def install_dependencies():
    """安装依赖"""
    try:
        print("🔧 启动依赖安装程序...")
        from install_dependencies import main as install_main
        install_main()
    except ImportError as e:
        print(f"❌ 无法导入依赖安装模块: {e}")
        print("请确保 install_dependencies.py 文件存在")
    except Exception as e:
        print(f"❌ 安装依赖时发生错误: {e}")

def run_tests():
    """运行测试"""
    try:
        print("🧪 启动测试程序...")
        import subprocess
        result = subprocess.run([sys.executable, "test_mac_tool.py"], 
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ 所有测试通过")
        else:
            print("❌ 测试失败")
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
MAC地址修改自动化工具 - 帮助信息

命令行参数:
  --cli                启动命令行界面
  --gui                启动图形用户界面
  --install            安装依赖包
  --test               运行测试
  --help               显示此帮助信息

使用示例:
  python main.py                    # 显示界面选择菜单
  python main.py --cli              # 直接启动命令行界面
  python main.py --gui              # 直接启动图形界面
  python main.py --install          # 安装依赖包

快速开始:
1. 首次使用请先运行: python main.py --install
2. 然后运行: python main.py --cli 或 python main.py --gui
3. 在界面中设置MAC修改软件路径
4. 选择要使用的MAC地址并执行修改

注意事项:
- 需要Windows操作系统
- 建议以管理员权限运行
- 确保MAC修改软件可以正常运行
- 某些网卡可能不支持MAC地址修改

支持的MAC地址格式:
- XX-XX-XX-XX-XX-XX (推荐)
- XX:XX:XX:XX:XX:XX
- XXXXXXXXXXXX

更多信息请查看 README.md 文件
    """
    print(help_text)

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="MAC地址修改自动化工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 显示界面选择菜单
  python main.py --cli              # 启动命令行界面
  python main.py --gui              # 启动图形界面
  python main.py --install          # 安装依赖包
        """
    )
    
    parser.add_argument('--cli', action='store_true',
                       help='启动命令行界面')
    parser.add_argument('--gui', action='store_true',
                       help='启动图形用户界面')
    parser.add_argument('--install', action='store_true',
                       help='安装依赖包')
    parser.add_argument('--test', action='store_true',
                       help='运行测试')

    # 添加对mac_cli.py参数的支持
    parser.add_argument('--mac', help='直接指定要修改的MAC地址')
    parser.add_argument('--random', action='store_true', help='使用随机MAC地址')
    parser.add_argument('--vendor', help='指定厂商生成MAC地址')
    parser.add_argument('--software-path', help='MAC修改软件路径')
    
    args = parser.parse_args()
    
    # 显示横幅
    print_banner()
    
    # 处理命令行参数
    if args.install:
        install_dependencies()
        return

    if args.test:
        run_tests()
        return

    # 检查依赖（除非是安装命令）
    if not check_dependencies():
        return

    # 如果有mac_cli.py的参数，直接传递给mac_cli
    if args.mac or args.random or args.vendor or args.software_path:
        try:
            # 重新构建命令行参数
            cli_args = []
            if args.mac:
                cli_args.extend(['--mac', args.mac])
            if args.random:
                cli_args.append('--random')
            if args.vendor:
                cli_args.extend(['--vendor', args.vendor])
            if args.software_path:
                cli_args.extend(['--software-path', args.software_path])

            # 修改sys.argv并调用mac_cli
            original_argv = sys.argv[:]
            sys.argv = ['mac_cli.py'] + cli_args

            try:
                from mac_cli import main as cli_main
                cli_main()
            finally:
                # 恢复原始argv
                sys.argv = original_argv
            return
        except Exception as e:
            print(f"❌ 执行命令失败: {e}")
            return

    if args.cli:
        launch_cli()
    elif args.gui:
        launch_gui()
    else:
        # 显示交互式菜单
        try:
            while True:
                choice = show_interface_selection()
                
                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    launch_cli()
                    break
                elif choice == '2':
                    launch_gui()
                    break
                elif choice == '3':
                    install_dependencies()
                elif choice == '4':
                    run_tests()
                elif choice == '5':
                    show_help()
                
                if choice in ['3', '4', '5']:
                    input("\n按回车键继续...")
                    
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
        except Exception as e:
            print(f"\n❌ 程序运行出错: {e}")

if __name__ == "__main__":
    main()
