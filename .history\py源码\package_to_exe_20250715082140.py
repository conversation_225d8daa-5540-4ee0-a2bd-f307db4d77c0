#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量零件编号处理工具 - EXE打包脚本
使用PyInstaller将Python程序打包成独立的exe文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_dependencies():
    """检查打包依赖"""
    print("=" * 60)
    print("检查打包依赖")
    print("=" * 60)
    
    required_packages = [
        ('PyQt5', 'PyQt5'),
        ('pandas', 'pandas'), 
        ('openpyxl', 'openpyxl'),
        ('pyinstaller', 'PyInstaller')
    ]
    
    missing_packages = []
    
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
            print(f"  ✓ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"  ✗ {package_name} (缺失)")
    
    if missing_packages:
        print(f"\n需要安装缺失的包:")
        install_cmd = f"pip install {' '.join(missing_packages)}"
        print(f"  {install_cmd}")
        
        # 询问是否自动安装
        try:
            response = input("\n是否自动安装缺失的包? (y/n): ").lower().strip()
            if response == 'y':
                print("正在安装缺失的包...")
                result = subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing_packages, 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✓ 包安装成功")
                    return True
                else:
                    print(f"✗ 包安装失败: {result.stderr}")
                    return False
            else:
                return False
        except KeyboardInterrupt:
            return False
    
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("\n创建PyInstaller规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['批量零件编号处理工具_Qt.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'pandas',
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.styles',
        'xlsxwriter',
        'pickle',
        'numpy',
        'numpy.random',
        'numpy.random._pickle',
        'pandas._libs',
        'pandas._libs.tslibs',
        'pandas.io.formats.style',
        'pandas.plotting',
        'pandas.io.clipboard',
        'openpyxl.chart',
        'openpyxl.drawing',
        'openpyxl.formula',
        'openpyxl.pivot',
        'openpyxl.utils',
        'pkg_resources.py2_warn'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy.random._pickle',
        'PIL',
        'tkinter',
        'scipy',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='批量零件编号处理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)
'''
    
    spec_file = '批量零件编号处理工具.spec'
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✓ 创建规格文件: {spec_file}")
    return spec_file

def cleanup_build_files():
    """清理构建文件"""
    print("清理构建文件...")
    
    cleanup_items = [
        'build',
        'dist', 
        '__pycache__',
        '批量零件编号处理工具.spec'
    ]
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"  清理目录: {item}")
            else:
                os.remove(item)
                print(f"  清理文件: {item}")

def build_exe():
    """构建exe文件"""
    print("\n" + "=" * 60)
    print("开始构建exe文件")
    print("=" * 60)
    
    # 清理之前的构建文件
    cleanup_build_files()
    
    # 创建规格文件
    spec_file = create_spec_file()
    
    # 运行PyInstaller
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        '--log-level=INFO',
        spec_file
    ]
    
    print(f"\n执行命令: {' '.join(cmd)}")
    print("正在构建，请稍候...")
    
    try:
        # 实时显示构建过程
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 text=True, encoding='utf-8', universal_newlines=True)
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 只显示重要信息
                if any(keyword in output for keyword in ['INFO:', 'WARNING:', 'ERROR:', 'Building']):
                    print(f"  {output.strip()}")
        
        return_code = process.poll()
        
        if return_code == 0:
            print("\n✓ PyInstaller构建成功")
            
            # 检查输出文件
            exe_path = os.path.join('dist', '批量零件编号处理工具.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"✓ exe文件已生成: {exe_path}")
                print(f"  文件大小: {file_size:.1f} MB")
                return True
            else:
                print("✗ exe文件未找到")
                return False
        else:
            print(f"\n✗ PyInstaller构建失败 (返回码: {return_code})")
            return False
            
    except Exception as e:
        print(f"✗ 构建过程中出现异常: {str(e)}")
        return False

def create_distribution_package():
    """创建分发包"""
    print("\n" + "=" * 60)
    print("创建分发包")
    print("=" * 60)
    
    # 创建分发目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    dist_dir = f"批量零件编号处理工具_v{timestamp}"
    
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    
    os.makedirs(dist_dir)
    print(f"创建分发目录: {dist_dir}")
    
    # 复制exe文件
    exe_source = os.path.join('dist', '批量零件编号处理工具.exe')
    exe_dest = os.path.join(dist_dir, '批量零件编号处理工具.exe')
    
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        print(f"✓ 复制exe文件")
    else:
        print(f"✗ 源exe文件不存在: {exe_source}")
        return None
    
    # 创建使用说明
    create_readme(dist_dir, timestamp)
    
    # 创建示例文件
    create_example_files(dist_dir)
    
    print(f"\n✅ 分发包创建完成: {dist_dir}")
    return dist_dir

def create_readme(dist_dir, version):
    """创建使用说明文件"""
    readme_content = f"""批量零件编号处理工具 v{version}

【程序功能】
- 批量处理零件编号
- 支持Excel文件更新（.xlsx, .xls, .xlsm）
- 支持DXF文件重命名（.dxf）
- 智能前缀匹配和保持
- 自动备份原始文件

【使用步骤】
1. 双击运行"批量零件编号处理工具.exe"
2. 设置输入路径（包含Excel和DXF文件的目录）
3. 设置输出路径（处理后文件的保存位置）
4. 配置Excel设置：
   - 工作表名称（默认：Sheet1）
   - 目标列名（默认：零件编号）
5. 输入零件名称（每行一个，如：LP22A）
6. 点击"开始处理"

【功能特点】
- 智能匹配：支持精确匹配和前缀匹配
- 前缀保持：自动保持连字符前缀（如"2-LP22" → "2-LP22A"）
- 安全备份：自动创建.backup备份文件
- 详细日志：显示处理过程和结果
- 错误处理：友好的错误提示和处理

【注意事项】
- 确保Excel文件未被其他程序打开
- 确保有足够的磁盘空间
- 建议先用小批量数据测试
- 处理前会自动创建备份文件

【示例】
输入零件名称：LP22A
Excel表格中的"LP22"会更新为"LP22A"
Excel表格中的"2-LP22"会更新为"2-LP22A"（保持前缀"2-"）

【技术支持】
如有问题请联系开发团队。

构建时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
    
    readme_path = os.path.join(dist_dir, "使用说明.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ 创建使用说明")

def create_example_files(dist_dir):
    """创建示例文件"""
    try:
        import pandas as pd
        
        # 创建示例文件夹
        example_dir = os.path.join(dist_dir, "示例文件")
        os.makedirs(example_dir)
        
        # 创建示例Excel文件
        example_data = {
            '序号': [1, 2, 3, 4, 5],
            '零件编号': ['LP22', '2-LP22', 'C38', 'A-C38', 'D12'],
            '零件名称': ['支撑板1', '支撑板2', '连接件1', '连接件2', '固定块'],
            '材质': ['Q235', 'Q235', 'Q345', 'Q345', 'Q235'],
            '数量': [2, 1, 4, 2, 3],
            '备注': ['', '', '', '', '']
        }
        
        df = pd.DataFrame(example_data)
        example_excel = os.path.join(example_dir, "示例零件清单.xlsx")
        
        with pd.ExcelWriter(example_excel, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Sheet1', index=False)
        
        # 创建示例零件名称文件
        example_parts = os.path.join(example_dir, "示例零件名称.txt")
        with open(example_parts, 'w', encoding='utf-8') as f:
            f.write("LP22A\nC38B\nD12C\n")
        
        print(f"✓ 创建示例文件")
        
    except Exception as e:
        print(f"⚠ 创建示例文件失败: {str(e)}")

def main():
    """主函数"""
    print("批量零件编号处理工具 - EXE打包程序")
    print("=" * 60)
    
    # 检查当前目录
    main_file = '批量零件编号处理工具_Qt.py'
    if not os.path.exists(main_file):
        print(f"❌ 未找到主程序文件: {main_file}")
        print("请在包含主程序的目录中运行此脚本")
        return False
    
    print(f"✓ 找到主程序文件: {main_file}")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败")
        return False
    
    # 构建exe
    if not build_exe():
        print("\n❌ exe构建失败")
        return False
    
    # 创建分发包
    dist_dir = create_distribution_package()
    if not dist_dir:
        print("\n❌ 分发包创建失败")
        return False
    
    # 最终清理
    print(f"\n清理临时文件...")
    cleanup_build_files()
    
    print(f"\n🎉 打包完成！")
    print(f"分发包位置: {os.path.abspath(dist_dir)}")
    print(f"exe文件: {os.path.join(dist_dir, '批量零件编号处理工具.exe')}")
    print(f"\n可以将整个 '{dist_dir}' 文件夹分发给用户使用")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
