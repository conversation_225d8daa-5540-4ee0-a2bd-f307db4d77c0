import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import socket
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

class PortScannerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("端口扫描器")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        self.stop_scan = False

        self.create_widgets()

    def create_widgets(self):
        tk.Label(self.root, text="目标地址 (IP或域名):").place(x=60, y=50)
        self.entry_host = tk.Entry(self.root, width=30)
        self.entry_host.place(x=200, y=50)
        self.entry_host.insert(0, "ewain.top")

        tk.Label(self.root, text="端口范围:").place(x=60, y=90)
        self.entry_ports = tk.Entry(self.root, width=30)
        self.entry_ports.place(x=200, y=90)
        self.entry_ports.insert(0, "1-1024")

        tk.Label(self.root, text="协议类型:").place(x=60, y=130)
        self.protocol = tk.StringVar(value="TCP")
        tk.Radiobutton(self.root, text="TCP", variable=self.protocol, value="TCP").place(x=200, y=130)
        tk.Radiobutton(self.root, text="UDP", variable=self.protocol, value="UDP").place(x=260, y=130)

        self.btn_scan = tk.Button(self.root, text="开始扫描", width=15, bg="#3399ff", command=self.start_scan)
        self.btn_scan.place(x=120, y=170)
        self.btn_stop = tk.Button(self.root, text="停止扫描", width=15, bg="#ff6666", command=self.stop_scanning)
        self.btn_stop.place(x=260, y=170)

        tk.Label(self.root, text="扫描进度:").place(x=60, y=220)
        self.progress = ttk.Progressbar(self.root, length=350, mode='determinate')
        self.progress.place(x=120, y=220)

        self.text_result = scrolledtext.ScrolledText(self.root, width=60, height=8)
        self.text_result.place(x=30, y=260)

    def start_scan(self):
        self.text_result.delete(1.0, tk.END)
        self.stop_scan = False
        host = self.entry_host.get().strip()
        port_range = self.entry_ports.get().strip()
        protocol = self.protocol.get()
        try:
            ip = socket.gethostbyname(host)
        except Exception as e:
            messagebox.showerror("错误", f"无法解析主机: {e}")
            return

        try:
            start_port, end_port = map(int, port_range.split('-'))
            if start_port < 1 or end_port > 65535 or start_port > end_port:
                raise ValueError
        except:
            messagebox.showerror("错误", "端口范围格式错误，应为 1-1024 这种格式")
            return

        self.progress["value"] = 0
        self.progress["maximum"] = end_port - start_port + 1
        self.text_result.insert(tk.END, f"目标地址: {ip}\n")
        threading.Thread(target=self.scan_ports, args=(ip, start_port, end_port, protocol), daemon=True).start()

    def stop_scanning(self):
        self.stop_scan = True

    def scan_ports(self, ip, start_port, end_port, protocol):
        open_ports = []
        total_ports = end_port - start_port + 1

        def scan_one(port):
            if self.stop_scan:
                return None
            if protocol == "TCP":
                if self.check_tcp_port(ip, port):
                    return port
            else:
                if self.check_udp_port(ip, port):
                    return port
            return None

        with ThreadPoolExecutor(max_workers=100) as executor:
            future_to_port = {executor.submit(scan_one, port): port for port in range(start_port, end_port + 1)}
            for idx, future in enumerate(as_completed(future_to_port), 1):
                port = future_to_port[future]
                result = future.result()
                if self.stop_scan:
                    self.text_result.insert(tk.END, "扫描已停止！\n")
                    break
                if result:
                    open_ports.append(result)
                    self.text_result.insert(tk.END, f"端口 {result} ({protocol}) 开放\n")
                self.progress["value"] = idx
                self.root.update_idletasks()

        self.text_result.insert(tk.END, f"\n扫描完成！开放端口:\n")
        if open_ports:
            for port in open_ports:
                self.text_result.insert(tk.END, f"端口 {port} ({protocol}) 开放\n")
        else:
            self.text_result.insert(tk.END, "无开放端口。\n")

    def check_tcp_port(self, ip, port):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(0.5)
                result = s.connect_ex((ip, port))
                return result == 0
        except:
            return False

    def check_udp_port(self, ip, port):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.settimeout(0.5)
                s.sendto(b'', (ip, port))
                s.recvfrom(1024)
                return True
        except:
            return False

if __name__ == "__main__":
    root = tk.Tk()
    app = PortScannerApp(root)
    root.mainloop()