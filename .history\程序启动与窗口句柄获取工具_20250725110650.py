#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网卡修改程序自动化工具
功能：启动指定的可执行文件、获取窗口句柄并自动化操作MAC地址编辑框

依赖包安装：
pip install pywin32 pyautogui uiautomation

作者：Augment Agent
创建时间：2025-07-25
更新时间：2025-07-25
"""

import subprocess
import time
import sys
import os
from typing import Optional, List, Tuple, Dict
import logging
import ctypes
from ctypes import wintypes

try:
    import win32gui
    import win32process
    import win32con
    import win32api
    import win32clipboard
except ImportError:
    print("错误：缺少pywin32库，请运行以下命令安装：")
    print("pip install pywin32 pyautogui uiautomation")
    sys.exit(1)

try:
    import pyautogui
    import uiautomation as auto
except ImportError:
    print("错误：缺少UI自动化库，请运行以下命令安装：")
    print("pip install pyautogui uiautomation")
    sys.exit(1)

# 设置pyautogui安全模式
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.5


class ProcessWindowManager:
    """进程和窗口管理器"""
    
    def __init__(self, exe_path: str):
        """
        初始化管理器
        
        Args:
            exe_path (str): 要启动的可执行文件路径
        """
        self.exe_path = exe_path
        self.process = None
        self.window_handles = []
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('window_manager.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def validate_exe_path(self) -> bool:
        """
        验证可执行文件路径是否有效
        
        Returns:
            bool: 路径有效返回True，否则返回False
        """
        if not os.path.exists(self.exe_path):
            self.logger.error(f"可执行文件不存在: {self.exe_path}")
            return False
        
        if not os.path.isfile(self.exe_path):
            self.logger.error(f"路径不是文件: {self.exe_path}")
            return False
        
        if not self.exe_path.lower().endswith('.exe'):
            self.logger.warning(f"文件可能不是可执行文件: {self.exe_path}")
        
        return True
    
    def start_process(self) -> bool:
        """
        启动指定的可执行文件
        
        Returns:
            bool: 启动成功返回True，否则返回False
        """
        if not self.validate_exe_path():
            return False
        
        try:
            self.logger.info(f"正在启动程序: {self.exe_path}")
            
            # 使用subprocess启动程序
            self.process = subprocess.Popen(
                [self.exe_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            # 等待程序启动
            time.sleep(2)
            
            # 检查进程是否仍在运行
            if self.process.poll() is None:
                self.logger.info(f"程序启动成功，进程ID: {self.process.pid}")
                return True
            else:
                self.logger.error("程序启动后立即退出")
                return False
                
        except FileNotFoundError:
            self.logger.error(f"找不到可执行文件: {self.exe_path}")
            return False
        except PermissionError:
            self.logger.error(f"没有权限执行文件: {self.exe_path}")
            return False
        except Exception as e:
            self.logger.error(f"启动程序时发生错误: {str(e)}")
            return False
    
    def enum_windows_callback(self, hwnd: int, windows: List[Tuple[int, str, int]]) -> bool:
        """
        枚举窗口的回调函数
        
        Args:
            hwnd (int): 窗口句柄
            windows (List): 窗口信息列表
            
        Returns:
            bool: 继续枚举返回True
        """
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            try:
                _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                windows.append((hwnd, window_text, process_id))
            except:
                pass
        return True
    
    def get_all_windows(self) -> List[Tuple[int, str, int]]:
        """
        获取所有可见窗口
        
        Returns:
            List[Tuple[int, str, int]]: 窗口信息列表 (句柄, 标题, 进程ID)
        """
        windows = []
        try:
            win32gui.EnumWindows(self.enum_windows_callback, windows)
        except Exception as e:
            self.logger.error(f"枚举窗口时发生错误: {str(e)}")
        return windows
    
    def find_process_windows(self, max_attempts: int = 10, wait_interval: float = 1.0) -> List[int]:
        """
        查找与启动进程相关的窗口句柄
        
        Args:
            max_attempts (int): 最大尝试次数
            wait_interval (float): 每次尝试间隔时间（秒）
            
        Returns:
            List[int]: 窗口句柄列表
        """
        if not self.process:
            self.logger.error("进程未启动，无法查找窗口")
            return []
        
        process_id = self.process.pid
        self.logger.info(f"正在查找进程ID {process_id} 的窗口...")
        
        for attempt in range(max_attempts):
            windows = self.get_all_windows()
            process_windows = []
            
            for hwnd, title, pid in windows:
                if pid == process_id:
                    process_windows.append(hwnd)
                    self.logger.info(f"找到窗口: 句柄={hwnd}, 标题='{title}'")
            
            if process_windows:
                self.window_handles = process_windows
                return process_windows
            
            self.logger.info(f"第 {attempt + 1} 次尝试未找到窗口，等待 {wait_interval} 秒后重试...")
            time.sleep(wait_interval)
        
        self.logger.warning(f"经过 {max_attempts} 次尝试，未找到进程的窗口")
        return []
    
    def get_window_info(self, hwnd: int) -> dict:
        """
        获取窗口详细信息
        
        Args:
            hwnd (int): 窗口句柄
            
        Returns:
            dict: 窗口信息字典
        """
        try:
            info = {
                'handle': hwnd,
                'title': win32gui.GetWindowText(hwnd),
                'class_name': win32gui.GetClassName(hwnd),
                'is_visible': win32gui.IsWindowVisible(hwnd),
                'is_enabled': win32gui.IsWindowEnabled(hwnd),
                'rect': win32gui.GetWindowRect(hwnd)
            }
            
            try:
                _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                info['process_id'] = process_id
            except:
                info['process_id'] = None
            
            return info
        except Exception as e:
            self.logger.error(f"获取窗口信息时发生错误: {str(e)}")
            return {}
    
    def print_window_details(self):
        """打印所有找到的窗口详细信息"""
        if not self.window_handles:
            self.logger.info("没有找到窗口")
            return
        
        print("\n" + "="*60)
        print("窗口详细信息:")
        print("="*60)
        
        for i, hwnd in enumerate(self.window_handles, 1):
            info = self.get_window_info(hwnd)
            if info:
                print(f"\n窗口 {i}:")
                print(f"  句柄: {info['handle']}")
                print(f"  标题: '{info['title']}'")
                print(f"  类名: {info['class_name']}")
                print(f"  可见: {info['is_visible']}")
                print(f"  启用: {info['is_enabled']}")
                print(f"  位置: {info['rect']}")
                print(f"  进程ID: {info['process_id']}")
    
    def cleanup(self):
        """清理资源"""
        if self.process:
            try:
                if self.process.poll() is None:
                    self.logger.info("正在终止进程...")
                    self.process.terminate()
                    time.sleep(1)
                    if self.process.poll() is None:
                        self.process.kill()
                        self.logger.info("强制终止进程")
            except Exception as e:
                self.logger.error(f"清理进程时发生错误: {str(e)}")


class MACModifierAutomation:
    """MAC修改器自动化操作类"""

    def __init__(self, window_handle: int):
        """
        初始化自动化操作类

        Args:
            window_handle (int): 目标窗口句柄
        """
        self.window_handle = window_handle
        self.logger = logging.getLogger(__name__)

        # 设置uiautomation的搜索超时时间
        auto.SetGlobalSearchTimeout(5.0)

    def bring_window_to_front(self) -> bool:
        """
        将窗口置于前台

        Returns:
            bool: 成功返回True，否则返回False
        """
        try:
            # 检查窗口是否存在
            if not win32gui.IsWindow(self.window_handle):
                self.logger.error("窗口句柄无效")
                return False

            # 尝试多种方法将窗口置于前台
            try:
                # 方法1: 直接设置前台窗口
                win32gui.SetForegroundWindow(self.window_handle)
            except Exception as e1:
                self.logger.warning(f"SetForegroundWindow失败: {e1}")
                try:
                    # 方法2: 先显示窗口再设置前台
                    win32gui.ShowWindow(self.window_handle, win32con.SW_RESTORE)
                    time.sleep(0.2)
                    win32gui.SetForegroundWindow(self.window_handle)
                except Exception as e2:
                    self.logger.warning(f"第二次尝试失败: {e2}")
                    try:
                        # 方法3: 使用BringWindowToTop
                        win32gui.BringWindowToTop(self.window_handle)
                        win32gui.SetActiveWindow(self.window_handle)
                    except Exception as e3:
                        self.logger.warning(f"第三次尝试失败: {e3}")
                        # 继续执行，可能窗口已经在前台

            time.sleep(0.5)

            # 验证窗口是否在前台
            foreground_window = win32gui.GetForegroundWindow()
            if foreground_window == self.window_handle:
                self.logger.info("窗口已成功置于前台")
                return True
            else:
                self.logger.warning(f"窗口可能未完全置于前台，当前前台窗口: {foreground_window}")
                # 即使不在前台也继续执行，因为可能仍然可以操作
                return True

        except Exception as e:
            self.logger.error(f"置于前台失败: {str(e)}")
            return False

    def find_edit_controls(self) -> List[Dict]:
        """
        查找窗口中的所有编辑框控件

        Returns:
            List[Dict]: 编辑框控件信息列表
        """
        edit_controls = []

        try:
            # 使用uiautomation查找窗口
            window = auto.WindowControl(Handle=self.window_handle)
            if not window.Exists(0, 0):
                self.logger.error("无法找到目标窗口")
                return edit_controls

            # 查找所有编辑框控件
            edit_list = window.GetChildren()
            for control in edit_list:
                if control.ControlType == auto.ControlType.EditControl:
                    try:
                        control_info = {
                            'handle': control.NativeWindowHandle,
                            'name': control.Name,
                            'value': control.GetValuePattern().Value if control.GetValuePattern() else "",
                            'rect': control.BoundingRectangle,
                            'automation_id': control.AutomationId,
                            'class_name': control.ClassName,
                            'control': control
                        }
                        edit_controls.append(control_info)
                        self.logger.info(f"找到编辑框: {control_info['name']} - 值: '{control_info['value']}'")
                    except Exception as e:
                        self.logger.warning(f"获取控件信息失败: {str(e)}")

            # 如果uiautomation没找到，尝试使用win32gui枚举子窗口
            if not edit_controls:
                self.logger.info("使用win32gui查找编辑框...")
                edit_controls = self._find_edit_controls_win32()

        except Exception as e:
            self.logger.error(f"查找编辑框时发生错误: {str(e)}")

        return edit_controls

    def _find_edit_controls_win32(self) -> List[Dict]:
        """
        使用win32gui查找编辑框控件

        Returns:
            List[Dict]: 编辑框控件信息列表
        """
        edit_controls = []

        def enum_child_proc(hwnd, param):
            class_name = win32gui.GetClassName(hwnd)
            if class_name.lower() in ['edit', 'tedit', 'richedit', 'richedit20a', 'richedit20w']:
                try:
                    text = win32gui.GetWindowText(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)

                    control_info = {
                        'handle': hwnd,
                        'name': f"Edit_{hwnd}",
                        'value': text,
                        'rect': rect,
                        'class_name': class_name,
                        'control': None
                    }
                    edit_controls.append(control_info)
                    self.logger.info(f"Win32找到编辑框: 句柄={hwnd}, 类名={class_name}, 值='{text}'")
                except Exception as e:
                    self.logger.warning(f"获取Win32控件信息失败: {str(e)}")
            return True

        try:
            win32gui.EnumChildWindows(self.window_handle, enum_child_proc, None)
        except Exception as e:
            self.logger.error(f"枚举子窗口失败: {str(e)}")

        return edit_controls

    def find_mac_edit_control(self) -> Optional[Dict]:
        """
        查找MAC地址编辑框控件

        Returns:
            Optional[Dict]: MAC地址编辑框控件信息，未找到返回None
        """
        edit_controls = self.find_edit_controls()

        if not edit_controls:
            self.logger.error("未找到任何编辑框控件")
            return None

        # 根据图片分析，寻找包含MAC地址格式的编辑框
        # MAC地址通常格式为 XX-XX-XX-XX-XX-XX 或 XX:XX:XX:XX:XX:XX
        for control in edit_controls:
            value = control.get('value', '').strip()

            # 检查是否包含MAC地址格式
            if self._is_mac_address_format(value):
                self.logger.info(f"找到MAC地址编辑框: {control['name']} - 值: '{value}'")
                return control

            # 如果值为空但位置合适，也可能是MAC编辑框
            if not value and control.get('rect'):
                rect = control['rect']
                # 根据图片，MAC编辑框大概在窗口的中上部分
                if isinstance(rect, (list, tuple)) and len(rect) >= 4:
                    self.logger.info(f"找到可能的MAC编辑框: {control['name']} (空值)")
                    return control

        # 如果没有找到明确的MAC编辑框，返回第一个编辑框
        if edit_controls:
            self.logger.warning("未找到明确的MAC编辑框，使用第一个编辑框")
            return edit_controls[0]

        return None

    def _is_mac_address_format(self, text: str) -> bool:
        """
        检查文本是否为MAC地址格式

        Args:
            text (str): 要检查的文本

        Returns:
            bool: 是MAC地址格式返回True，否则返回False
        """
        if not text:
            return False

        # 移除空格
        text = text.replace(' ', '')

        # 检查常见的MAC地址格式
        import re
        mac_patterns = [
            r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$',  # XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX
            r'^([0-9A-Fa-f]{2}){6}$',  # XXXXXXXXXXXX
            r'^([0-9A-Fa-f]{4}\.){2}([0-9A-Fa-f]{4})$'  # XXXX.XXXX.XXXX
        ]

        for pattern in mac_patterns:
            if re.match(pattern, text):
                return True

        return False

    def click_edit_control(self, control_info: Dict) -> bool:
        """
        点击编辑框控件获得焦点

        Args:
            control_info (Dict): 编辑框控件信息

        Returns:
            bool: 成功返回True，否则返回False
        """
        try:
            # 确保窗口在前台
            if not self.bring_window_to_front():
                return False

            # 如果有uiautomation控件对象，使用它
            if control_info.get('control'):
                control = control_info['control']
                control.Click()
                self.logger.info("使用uiautomation点击编辑框")
                time.sleep(0.3)
                return True

            # 否则使用坐标点击
            rect = control_info.get('rect')
            if rect and len(rect) >= 4:
                # 计算编辑框中心点
                center_x = (rect[0] + rect[2]) // 2
                center_y = (rect[1] + rect[3]) // 2

                # 使用pyautogui点击
                pyautogui.click(center_x, center_y)
                self.logger.info(f"点击编辑框坐标: ({center_x}, {center_y})")
                time.sleep(0.3)
                return True

            self.logger.error("无法获取编辑框位置信息")
            return False

        except Exception as e:
            self.logger.error(f"点击编辑框失败: {str(e)}")
            return False

    def select_all_text(self) -> bool:
        """
        选中编辑框中的所有文本

        Returns:
            bool: 成功返回True，否则返回False
        """
        try:
            # 使用Ctrl+A选中所有文本
            pyautogui.hotkey('ctrl', 'a')
            self.logger.info("执行全选操作 (Ctrl+A)")
            time.sleep(0.2)
            return True

        except Exception as e:
            self.logger.error(f"全选文本失败: {str(e)}")
            return False

    def clear_text(self) -> bool:
        """
        清除编辑框中的文本内容

        Returns:
            bool: 成功返回True，否则返回False
        """
        try:
            # 使用Delete键删除选中的文本
            pyautogui.press('delete')
            self.logger.info("执行删除操作 (Delete)")
            time.sleep(0.2)
            return True

        except Exception as e:
            self.logger.error(f"清除文本失败: {str(e)}")
            return False

    def automate_mac_edit_clear(self) -> bool:
        """
        自动化操作：定位MAC编辑框并清除内容

        Returns:
            bool: 成功返回True，否则返回False
        """
        try:
            self.logger.info("开始自动化操作MAC编辑框...")

            # 1. 将窗口置于前台
            if not self.bring_window_to_front():
                self.logger.error("无法将窗口置于前台")
                return False

            # 2. 查找MAC编辑框
            mac_control = self.find_mac_edit_control()
            if not mac_control:
                self.logger.error("未找到MAC编辑框")
                return False

            self.logger.info(f"找到MAC编辑框: {mac_control.get('name', 'Unknown')}")

            # 3. 点击编辑框获得焦点
            if not self.click_edit_control(mac_control):
                self.logger.error("点击编辑框失败")
                return False

            # 4. 选中所有文本
            if not self.select_all_text():
                self.logger.error("选中文本失败")
                return False

            # 5. 清除文本内容
            if not self.clear_text():
                self.logger.error("清除文本失败")
                return False

            self.logger.info("MAC编辑框自动化操作完成")
            return True

        except Exception as e:
            self.logger.error(f"自动化操作失败: {str(e)}")
            return False


def main():
    """主函数"""
    # 指定要启动的可执行文件路径
    exe_path = r"C:\Users\<USER>\Desktop\网卡修改.exe"
    
    # 创建进程窗口管理器
    manager = ProcessWindowManager(exe_path)
    
    try:
        # 启动程序
        if not manager.start_process():
            print("程序启动失败")
            return
        
        # 查找窗口句柄
        window_handles = manager.find_process_windows()
        
        if window_handles:
            print(f"\n成功找到 {len(window_handles)} 个窗口")
            print("窗口句柄列表:", window_handles)
            
            # 打印窗口详细信息
            manager.print_window_details()
            
            # 获取主窗口句柄
            main_window = window_handles[0]
            print(f"\n主窗口句柄: {main_window}")

            # 创建自动化操作对象
            automation = MACModifierAutomation(main_window)

            # 等待用户确认是否执行自动化操作
            print("\n" + "="*60)
            print("准备执行自动化操作:")
            print("1. 定位MAC地址编辑框")
            print("2. 点击编辑框获得焦点")
            print("3. 选中所有文本内容")
            print("4. 清除文本内容")
            print("="*60)

            user_input = input("\n是否执行自动化操作? (y/n): ").strip().lower()

            if user_input in ['y', 'yes', '是']:
                print("\n开始执行自动化操作...")

                if automation.automate_mac_edit_clear():
                    print("✓ 自动化操作成功完成!")
                else:
                    print("✗ 自动化操作失败!")
            else:
                print("用户取消自动化操作")

        else:
            print("未找到程序窗口")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行过程中发生错误: {str(e)}")
    finally:
        # 清理资源（可选，如果需要保持程序运行可以注释掉）
        # manager.cleanup()
        pass


if __name__ == "__main__":
    main()
