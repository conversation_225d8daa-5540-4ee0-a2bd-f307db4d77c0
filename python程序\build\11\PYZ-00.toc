('C:\\Users\\<USER>\\Desktop\\python程序\\build\\11\\PYZ-00.pyz',
 [('_compat_pickle', 'D:\\Miniconda3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\Miniconda3\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Miniconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\Miniconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Miniconda3\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\Miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\Miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\Miniconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Miniconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Miniconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\Miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\Miniconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\Miniconda3\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\Miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\Miniconda3\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\Miniconda3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\Miniconda3\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\Miniconda3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Miniconda3\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\Miniconda3\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\Miniconda3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Miniconda3\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\Miniconda3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\Miniconda3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\Miniconda3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Miniconda3\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\Miniconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Miniconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\Miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\Miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'D:\\Miniconda3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Miniconda3\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Miniconda3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Miniconda3\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\Miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Miniconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Miniconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Miniconda3\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Miniconda3\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Miniconda3\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\Miniconda3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\Miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\Miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\Miniconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Miniconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\Miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\Miniconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\Miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Miniconda3\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\Miniconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Miniconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Miniconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\Miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Miniconda3\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\Miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Miniconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\Miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\Miniconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Miniconda3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('zipfile', 'D:\\Miniconda3\\Lib\\zipfile.py', 'PYMODULE')])
