(['C:\\Users\\<USER>\\Desktop\\python程序\\11.py'],
 ['C:\\Users\\<USER>\\Desktop\\python程序'],
 ['json', 'json.decoder', 'json.encoder'],
 [('D:\\Miniconda3\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('454646.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\454646.py',
   'DATA'),
  ('EXCEL表格绘制矩形.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\EXCEL表格绘制矩形.py',
   'DATA'),
  ('python转换exe工具.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\python转换exe工具.py',
   'DATA'),
  ('柏楚清单整理-加入检查.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\柏楚清单整理-加入检查.py',
   'DATA'),
  ('柏楚清单整理-手动路径.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\柏楚清单整理-手动路径.py',
   'DATA'),
  ('柏楚清单整理.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\柏楚清单整理.py',
   'DATA')],
 '3.11.4 | packaged by Anaconda, Inc. | (main, Jul  5 2023, 13:47:18) [MSC '
 'v.1916 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('11',
   'C:\\Users\\<USER>\\Desktop\\python程序\\11.py',
   'PYSOURCE')],
 [('inspect', 'D:\\Miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('importlib', 'D:\\Miniconda3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\Miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\Miniconda3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\Miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\Miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('_strptime', 'D:\\Miniconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'D:\\Miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('calendar', 'D:\\Miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('statistics', 'D:\\Miniconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\Miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Miniconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Miniconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\Miniconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\Miniconda3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\Miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Miniconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('copy', 'D:\\Miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Miniconda3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('struct', 'D:\\Miniconda3\\Lib\\struct.py', 'PYMODULE'),
  ('threading', 'D:\\Miniconda3\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Miniconda3\\Lib\\_threading_local.py', 'PYMODULE'),
  ('string', 'D:\\Miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\Miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'D:\\Miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\Miniconda3\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\Miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Miniconda3\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('contextlib', 'D:\\Miniconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\Miniconda3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\Miniconda3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\Miniconda3\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\Miniconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\Miniconda3\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\Miniconda3\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('email.charset', 'D:\\Miniconda3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\Miniconda3\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Miniconda3\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\Miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\Miniconda3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\Miniconda3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\Miniconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\Miniconda3\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Miniconda3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Miniconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'D:\\Miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Miniconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\Miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('textwrap', 'D:\\Miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'D:\\Miniconda3\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\Miniconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.util', 'D:\\Miniconda3\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('pathlib', 'D:\\Miniconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('email', 'D:\\Miniconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\Miniconda3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Miniconda3\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'D:\\Miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse', 'D:\\Miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('token', 'D:\\Miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'D:\\Miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Miniconda3\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Miniconda3\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Miniconda3\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('json', 'D:\\Miniconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Miniconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\Miniconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Miniconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\Miniconda3\\Lib\\signal.py', 'PYMODULE')],
 [('python311.dll', 'D:\\Miniconda3\\python311.dll', 'BINARY'),
  ('_decimal.pyd', 'D:\\Miniconda3\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Miniconda3\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Miniconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Miniconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\Miniconda3\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Miniconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Miniconda3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\Miniconda3\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\Miniconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Miniconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll', 'D:\\Miniconda3\\Library\\bin\\liblzma.dll', 'BINARY'),
  ('LIBBZ2.dll', 'D:\\Miniconda3\\Library\\bin\\LIBBZ2.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\Miniconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Miniconda3\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('454646.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\454646.py',
   'DATA'),
  ('EXCEL表格绘制矩形.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\EXCEL表格绘制矩形.py',
   'DATA'),
  ('python转换exe工具.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\python转换exe工具.py',
   'DATA'),
  ('柏楚清单整理-加入检查.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\柏楚清单整理-加入检查.py',
   'DATA'),
  ('柏楚清单整理-手动路径.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\柏楚清单整理-手动路径.py',
   'DATA'),
  ('柏楚清单整理.py',
   'C:\\Users\\<USER>\\Desktop\\python程序\\柏楚清单整理.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\python程序\\build\\11\\base_library.zip',
   'DATA')])
