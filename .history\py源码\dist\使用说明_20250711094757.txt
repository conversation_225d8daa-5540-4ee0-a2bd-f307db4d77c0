# 清单处理工具 使用说明

## 文件说明
- 清单处理工具.exe - 主程序
- list_processor_config.ini - 配置文件（首次运行后自动生成）

## 使用方法
1. 双击运行"清单处理工具.exe"
2. 选择包含"清单"和"NC_dxf"文件夹的项目根目录
3. 根据需要设置处理选项：
   - 处理完成后显示提醒
   - 自动创建DXF压缩包
4. 点击"开始处理"按钮
5. 根据弹出的对话框进行确认操作
6. 查看处理结果

## 功能特点
- **HI型材智能处理**: 自动将HI型材分解为F1、F2、W1三个子零件
- **全局花纹板关键词检测**: 检测花、花纹、花纹板、HWB、HW等关键词
- **PL前缀数据格式确认**: 智能识别需要PL前缀的数据格式
- **乘法格式自动处理**: 自动将x替换为*进行处理
- **DXF文件ZIP压缩功能**: 处理完成后可自动创建DXF文件压缩包
- **智能提醒和用户交互**: 醒目的提醒对话框和用户确认机制
- **大小写重复检测**: 检测零件名的大小写重复问题
- **直发配件重复警告**: 检测直发配件与跨零件清单的重复

## 注意事项
- 确保项目目录包含"清单"和"NC_dxf"两个子文件夹
- 处理过程中请勿关闭程序
- 建议在处理前备份重要文件
- 如遇问题，请查看程序日志信息
- **HI型材处理**: 确保报料模板中包含对应的F1、F2、W1子零件信息
- **Excel文件格式**: 支持.xlsx和.xls格式，建议使用.xlsx
- **数据完整性**: 确保Excel文件中的必要列存在且数据完整

## HI型材处理说明
当跨零件清单中包含HI型材时（如HI200*100*5.5*8），程序会自动：
1. 识别HI型材零件（如B1）
2. 分解为3个子零件：
   - B1F1（第一个翼缘板）
   - B1F2（第二个翼缘板）
   - B1W1（腹板）
3. 从报料模板中查找对应的厚度、宽度、长度信息
4. 保持原零件的数量不变
5. 合并到最终的清单中

## 系统要求
- Windows 7/8/10/11
- 无需安装Python环境
- 建议内存2GB以上

版本: 1.0.0
更新日期: 2024年
