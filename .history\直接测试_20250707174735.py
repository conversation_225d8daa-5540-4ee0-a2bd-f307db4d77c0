#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接测试清单处理程序
"""

import sys
import os

# 添加源码路径
sys.path.insert(0, 'py源码')

def test_import():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        import pandas as pd
        print("✅ pandas导入成功")
    except Exception as e:
        print(f"❌ pandas导入失败: {e}")
        return False
    
    try:
        import openpyxl
        print("✅ openpyxl导入成功")
    except Exception as e:
        print(f"❌ openpyxl导入失败: {e}")
        return False
    
    try:
        import xlrd
        print("✅ xlrd导入成功")
    except Exception as e:
        print(f"❌ xlrd导入失败: {e}")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5导入成功")
    except Exception as e:
        print(f"❌ PyQt5导入失败: {e}")
        return False
    
    return True

def test_source_code():
    """测试源代码"""
    print("\n🧪 测试源代码...")
    
    try:
        # 尝试导入主程序
        from 清单处理_Qt import ListProcessorApp, ReminderDialog
        print("✅ 主程序类导入成功")
        
        # 尝试创建应用程序
        app = QApplication([])
        print("✅ QApplication创建成功")
        
        # 尝试创建主窗口
        window = ListProcessorApp()
        print("✅ 主窗口创建成功")
        
        # 不显示窗口，只是测试创建
        app.quit()
        print("✅ 程序测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 源代码测试失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False

def test_minimal_qt():
    """测试最小Qt程序"""
    print("\n🧪 测试最小Qt程序...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        
        app = QApplication([])
        
        window = QWidget()
        window.setWindowTitle("测试窗口")
        window.setGeometry(100, 100, 300, 200)
        
        layout = QVBoxLayout()
        label = QLabel("Qt测试成功！")
        layout.addWidget(label)
        window.setLayout(layout)
        
        print("✅ 最小Qt程序创建成功")
        
        # 显示窗口1秒钟
        window.show()
        app.processEvents()
        
        import time
        time.sleep(1)
        
        window.close()
        app.quit()
        
        print("✅ 最小Qt程序运行成功")
        return True
        
    except Exception as e:
        print(f"❌ 最小Qt程序失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 直接测试清单处理程序")
    print("=" * 50)
    
    # 1. 测试导入
    if not test_import():
        print("\n❌ 基础模块导入失败，无法继续测试")
        return
    
    # 2. 测试最小Qt程序
    if not test_minimal_qt():
        print("\n❌ Qt基础功能失败")
        return
    
    # 3. 测试源代码
    if not test_source_code():
        print("\n❌ 源代码测试失败")
        return
    
    print("\n✅ 所有测试通过！")
    print("💡 源代码本身没有问题，exe打包可能有问题")

if __name__ == "__main__":
    main()
    input("\n按任意键退出...")
