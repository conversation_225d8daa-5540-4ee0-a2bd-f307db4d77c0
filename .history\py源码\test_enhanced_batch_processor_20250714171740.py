#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强批量零件编号处理工具功能测试脚本
测试新的匹配逻辑和文件处理规则
"""

import os
import sys
import pandas as pd
import shutil
import tempfile
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

def test_enhanced_processing_logic():
    """测试增强处理逻辑"""
    print("=" * 70)
    print("批量零件编号处理工具 - 增强功能测试")
    print("=" * 70)
    
    # 使用实际的测试数据目录
    test_dir = os.path.join(os.path.dirname(__file__), "test_data")
    input_dir = os.path.join(test_dir, "enhanced_input")
    output_dir = os.path.join(test_dir, "enhanced_output")
    
    # 确保输出目录存在且为空
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    print(f"使用测试目录:")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    
    # 测试参数
    part_names = ['LP3A', 'C38B', 'D12C', 'E56D', 'F23E', 'G78F', 'H91G', 'I34H', 'J45I', 'K56J']
    sheet_name = "Sheet1"
    column_name = "零件编号"
    
    print(f"\n测试参数:")
    print(f"零件名称: {part_names}")
    print(f"工作表: {sheet_name}")
    print(f"目标列: {column_name}")
    
    # 导入处理线程类
    try:
        from 批量零件编号处理工具_Qt import ProcessingThread
    except ImportError as e:
        print(f"错误：无法导入处理类: {e}")
        return False
    
    # 创建处理线程实例
    processor = ProcessingThread(
        part_names, input_dir, output_dir, sheet_name, column_name
    )
    
    # 测试零件名称解析
    print(f"\n{'='*25} 测试零件名称解析 {'='*25}")
    processed_parts = processor.parse_part_names()
    
    print("解析结果:")
    for part in processed_parts:
        print(f"  {part['original_name']} → 匹配目标: {part['match_target']}")
    
    # 测试新的匹配逻辑
    print(f"\n{'='*25} 测试新匹配逻辑 {'='*25}")
    test_matching_logic(processor)
    
    # 测试Excel文件批量处理
    print(f"\n{'='*25} 测试Excel批量处理 {'='*25}")
    
    # 显示处理前的Excel文件
    import glob
    excel_files = []
    for pattern in ['*.xlsx', '*.xls', '*.xlsm']:
        excel_files.extend(glob.glob(os.path.join(input_dir, pattern)))
    
    print("处理前的Excel文件:")
    for excel_file in excel_files:
        print(f"  {os.path.basename(excel_file)}")
        try:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            print(f"    零件编号: {list(df[column_name])}")
        except Exception as e:
            print(f"    读取失败: {e}")
    
    # 执行Excel处理
    excel_success = processor.process_excel_files(processed_parts)
    print(f"Excel批量处理结果: {'成功' if excel_success else '失败'}")
    
    if excel_success:
        print(f"\nExcel处理统计:")
        print(f"匹配数量: {processor.stats['excel_matched']}")
        print(f"更新数量: {processor.stats['excel_updated']}")
        
        # 显示处理后的Excel内容
        print("\n处理后的Excel文件内容:")
        for excel_file in excel_files:
            if os.path.exists(excel_file + '.backup'):  # 只显示被处理过的文件
                try:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name)
                    print(f"  {os.path.basename(excel_file)}:")
                    print(f"    零件编号: {list(df[column_name])}")
                except Exception as e:
                    print(f"    读取失败: {e}")
    
    # 测试DXF文件处理
    print(f"\n{'='*25} 测试DXF文件处理 {'='*25}")
    
    # 显示处理前的DXF文件
    dxf_files_before = [f for f in os.listdir(input_dir) if f.endswith('.dxf')]
    print("处理前的DXF文件:")
    for f in sorted(dxf_files_before):
        print(f"  {f}")
    
    # 执行DXF处理
    processor.process_dxf_files(processed_parts)
    
    # 显示处理后的DXF文件
    dxf_files_after = [f for f in os.listdir(input_dir) if f.endswith('.dxf')]
    print("\n处理后的DXF文件:")
    for f in sorted(dxf_files_after):
        print(f"  {f}")
    
    print(f"\nDXF处理统计:")
    print(f"找到数量: {processor.stats['dxf_found']}")
    print(f"重命名数量: {processor.stats['dxf_renamed']}")
    
    # 测试文件复制（只复制重命名的文件）
    print(f"\n{'='*25} 测试选择性文件复制 {'='*25}")
    processor.copy_files_to_output()
    
    output_files = os.listdir(output_dir)
    print("输出目录文件:")
    for f in sorted(output_files):
        print(f"  {f}")
    
    print(f"\n文件复制统计:")
    print(f"复制数量: {processor.stats['files_copied']}")
    
    # 验证复制逻辑
    print(f"\n{'='*25} 验证复制逻辑 {'='*25}")
    
    # 检查哪些DXF文件被重命名和复制
    renamed_dxf_names = [os.path.basename(f) for f in getattr(processor, 'renamed_dxf_files', [])]
    all_dxf_files = [f for f in os.listdir(input_dir) if f.endswith('.dxf')]
    
    print("DXF文件处理结果:")
    for dxf_file in sorted(all_dxf_files):
        if dxf_file in renamed_dxf_names:
            print(f"  ✓ {dxf_file} - 已重命名并复制")
        else:
            print(f"  ✗ {dxf_file} - 未重命名，未复制")
    
    # 检查Excel文件处理结果
    print("\nExcel文件处理结果:")
    for excel_file in excel_files:
        backup_file = excel_file + '.backup'
        excel_name = os.path.basename(excel_file)
        if os.path.exists(backup_file):
            if excel_name in output_files:
                print(f"  ✓ {excel_name} - 已处理并复制")
            else:
                print(f"  ? {excel_name} - 已处理但未复制")
        else:
            print(f"  ✗ {excel_name} - 未处理，未复制")
    
    # 测试日志生成
    print(f"\n{'='*25} 测试日志生成 {'='*25}")
    processor.create_log_file()
    
    log_files = [f for f in os.listdir(output_dir) if f.startswith('批量零件编号处理日志')]
    if log_files:
        log_file = os.path.join(output_dir, log_files[0])
        print(f"生成日志文件: {log_files[0]}")
        
        # 显示日志文件前几行
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:30]  # 显示前30行
            print("\n日志文件内容预览:")
            for line in lines:
                print(f"  {line.rstrip()}")
            if len(lines) == 30:
                print("  ...")
    
    # 显示最终统计
    print(f"\n{'='*25} 最终统计 {'='*25}")
    print(f"总零件数: {processor.stats['total_parts']}")
    print(f"Excel匹配数: {processor.stats['excel_matched']}")
    print(f"Excel更新数: {processor.stats['excel_updated']}")
    print(f"DXF找到数: {processor.stats['dxf_found']}")
    print(f"DXF重命名数: {processor.stats['dxf_renamed']}")
    print(f"文件复制数: {processor.stats['files_copied']}")
    print(f"错误数: {len(processor.stats['errors'])}")
    
    if processor.stats['errors']:
        print("\n错误信息:")
        for error in processor.stats['errors']:
            print(f"  ERROR: {error}")
    
    print(f"\n{'='*70}")
    print("增强功能测试完成！")
    print(f"{'='*70}")
    
    return True

def test_matching_logic(processor):
    """测试新的匹配逻辑"""
    # 创建测试数据框
    test_data = {
        '零件编号': ['LP3', '2-LP3', 'LP30', 'LP3A', 'C38', '1-C38', 'C380', 'F23', 'F230', 'F23A', 'AB-I34', 'I34']
    }
    df = pd.DataFrame(test_data)
    
    # 测试不同的匹配目标
    test_cases = [
        ('LP3', ['LP3', '2-LP3']),  # 应该匹配精确和前缀
        ('C38', ['C38', '1-C38']),  # 应该匹配精确和前缀
        ('F23', ['F23']),           # 应该只匹配精确，不匹配F230或F23A
        ('I34', ['I34', 'AB-I34'])  # 应该匹配精确和复杂前缀
    ]
    
    print("匹配逻辑测试结果:")
    for target, expected in test_cases:
        matched_indices = processor.find_flexible_matches(df, target)
        matched_values = [df.loc[idx, '零件编号'] for idx in matched_indices]
        
        print(f"  目标: {target}")
        print(f"    匹配到: {matched_values}")
        print(f"    期望: {expected}")
        print(f"    结果: {'✓ 正确' if set(matched_values) == set(expected) else '✗ 错误'}")
        print()

if __name__ == '__main__':
    try:
        success = test_enhanced_processing_logic()
        if success:
            print("\n✓ 所有增强功能测试通过！")
        else:
            print("\n✗ 增强功能测试失败！")
            sys.exit(1)
    except Exception as e:
        print(f"\n✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
