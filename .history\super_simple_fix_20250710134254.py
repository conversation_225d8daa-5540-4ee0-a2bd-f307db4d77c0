#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
超级简单的PyInstaller修复工具
避免所有选项冲突问题
"""

import os
import sys
import subprocess
import shutil
import warnings

def simple_package(source_file, output_name=None, icon_path=None):
    """超级简单的打包方案，避免所有冲突"""
    
    if not os.path.exists(source_file):
        print(f"❌ 错误: 源文件 {source_file} 不存在")
        return False
    
    if not output_name:
        output_name = os.path.splitext(os.path.basename(source_file))[0]
    
    print(f"🚀 超级简单打包工具")
    print(f"源文件: {source_file}")
    print(f"输出名称: {output_name}")
    if icon_path:
        print(f"图标文件: {icon_path}")
    
    try:
        # 设置环境变量忽略警告
        env = os.environ.copy()
        env['PYTHONWARNINGS'] = 'ignore::DeprecationWarning'
        
        # 清理构建文件
        for dir_name in ["build", "dist", "__pycache__"]:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"🧹 已清理{dir_name}目录")
        
        # 构建最简单的命令
        print("🚀 开始简单打包...")
        command = ['pyinstaller']
        
        # 基本选项
        command.extend(['--onefile', '--noconsole', '--clean'])
        
        # 图标
        if icon_path and os.path.exists(icon_path):
            command.extend(['--icon', icon_path])
        
        # 输出名称
        command.extend(['--name', output_name])
        
        # 隐藏导入 - 只添加最必要的
        essential_imports = [
            'PyQt5.QtCore',
            'PyQt5.QtGui', 
            'PyQt5.QtWidgets',
            'json',
            'subprocess',
            'pathlib'
        ]
        
        for imp in essential_imports:
            command.extend(['--hidden-import', imp])
        
        # 排除问题模块
        excludes = ['pkg_resources', 'tkinter', 'matplotlib', 'numpy', 'PyQt6']
        for exc in excludes:
            command.extend(['--exclude-module', exc])
        
        # 源文件
        command.append(source_file)
        
        print(f"执行命令: {' '.join(command)}")
        
        # 执行打包
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env,
            cwd=os.path.dirname(source_file) or '.'
        )
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            
            # 检查输出文件
            dist_dir = "dist"
            exe_file = os.path.join(dist_dir, f"{output_name}.exe")
            if os.path.exists(exe_file):
                file_size = os.path.getsize(exe_file) / (1024 * 1024)
                print(f"📦 输出文件: {exe_file}")
                print(f"📏 文件大小: {file_size:.1f} MB")
            
            return True
        else:
            print("❌ 打包失败:")
            # 过滤输出，只显示重要错误
            if result.stderr:
                lines = result.stderr.split('\n')
                error_lines = [line for line in lines 
                             if 'ERROR' in line and 'pkg_resources' not in line]
                if error_lines:
                    print("错误信息:")
                    for line in error_lines[-3:]:
                        print(line)
                else:
                    # 如果没有ERROR，显示最后几行输出
                    print("输出信息:")
                    for line in lines[-5:]:
                        if line.strip():
                            print(line)
            return False
            
    except Exception as e:
        print(f"❌ 发生异常: {e}")
        return False

def main():
    print("=" * 50)
    print("🛠️  超级简单PyInstaller工具")
    print("    避免所有选项冲突问题")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        source_file = sys.argv[1]
        output_name = sys.argv[2] if len(sys.argv) > 2 else None
        icon_path = sys.argv[3] if len(sys.argv) > 3 else None
    else:
        source_file = input("请输入Python文件路径: ").strip().strip('"')
        output_name = input("请输入输出名称 (回车使用默认): ").strip() or None
        icon_path = input("请输入图标文件路径 (可选): ").strip().strip('"') or None
    
    success = simple_package(source_file, output_name, icon_path)
    
    if success:
        print("\n🎉 简单打包完成！请检查dist目录中的文件。")
    else:
        print("\n💥 打包失败，请检查错误信息。")
    
    input("\n按Enter键退出...")

if __name__ == '__main__':
    main()
