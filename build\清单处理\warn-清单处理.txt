
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pep517 - imported by importlib.metadata (delayed)
missing module named grp - imported by subprocess (optional), shutil (optional), tarfile (optional), pathlib (delayed, optional)
missing module named pwd - imported by posixpath (delayed, conditional), subprocess (optional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named org - imported by pickle (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by D:\Miniconda3\envs\Excel\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named charset_normalizer - imported by numpy.f2py.crackfortran (optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named yaml - imported by numpy.__config__ (delayed)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named defusedxml - imported by openpyxl.xml (delayed, optional), PIL.Image (optional)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named 'lxml.etree' - imported by openpyxl.xml.functions (conditional), pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed), fontTools.misc.etree (optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named lxml - imported by openpyxl.xml (delayed, optional), pandas.io.xml (conditional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional)
missing module named ezdxf.math.UCS - imported by ezdxf.math (top-level), ezdxf.render.forms (top-level), ezdxf.render.mesh (top-level), ezdxf.proxygraphic (top-level), ezdxf.render.r12spline (top-level), ezdxf.render.mleader (top-level), ezdxf.path.tools (top-level), ezdxf.addons.menger_sponge (top-level), ezdxf.addons.sierpinski_pyramid (top-level), ezdxf.entities.mtext (top-level), ezdxf.disassemble (top-level), ezdxf.entities.mline (top-level), ezdxf.render.dimension (top-level), ezdxf.entities.dimstyleoverride (top-level), ezdxf.render.dim_base (top-level), ezdxf.render.dim_curved (top-level), ezdxf.render.dim_diameter (top-level), ezdxf.render.dim_radius (top-level), ezdxf.render.dim_linear (top-level), ezdxf.render.dim_ordinate (top-level), ezdxf.entities.ucs (top-level), ezdxf.render.point (top-level), ezdxf.entities.insert (top-level), ezdxf.graphicsfactory (top-level)
missing module named ezdxf.math.Vec3 - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.layouts.blocklayout (top-level), ezdxf.sections.blocks (top-level), ezdxf.render.forms (top-level), ezdxf.render.mesh (top-level), ezdxf.entities.xdata (top-level), ezdxf.xref (top-level), ezdxf.proxygraphic (top-level), ezdxf.query (top-level), ezdxf.acis.mesh (top-level), ezdxf.acis.sab (top-level), ezdxf.acis.abstract (conditional), ezdxf.acis.sat (conditional), ezdxf.acis.entities (top-level), ezdxf.math.triangulation (top-level), ezdxf.render.r12spline (top-level), ezdxf.render.curves (top-level), ezdxf.render.trace (top-level), ezdxf.render.mleader (top-level), ezdxf.path.commands (top-level), ezdxf.path.path (top-level), ezdxf.path.converter (top-level), ezdxf.entities.polygon (top-level), ezdxf.math.transformtools (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.render.hatching (top-level), ezdxf.path.tools (top-level), ezdxf.transform (top-level), ezdxf.path.shapes (top-level), ezdxf.npshapes (top-level), ezdxf.addons.mtextsurrogate (top-level), ezdxf.addons.menger_sponge (top-level), ezdxf.addons.sierpinski_pyramid (top-level), ezdxf.addons.dimlines (top-level), ezdxf.tools.text (top-level), ezdxf.entities.mtext (top-level), ezdxf.entities.mleader (top-level), ezdxf.explode (top-level), ezdxf.disassemble (top-level), ezdxf.entities.mline (top-level), ezdxf.render.mline (top-level), ezdxf.entities.dimstyleoverride (top-level), ezdxf.render.dim_base (top-level), ezdxf.render.dim_curved (top-level), ezdxf.render.dim_linear (top-level), ezdxf.entities.image (top-level), ezdxf.entities.view (top-level), ezdxf.entities.layout (top-level), ezdxf.entities.oleframe (top-level), ezdxf.entities.line (top-level), ezdxf.entities.point (top-level), ezdxf.render.point (top-level), ezdxf.entities.circle (top-level), ezdxf.entities.arc (top-level), ezdxf.entities.solid (top-level), ezdxf.entities.text (top-level), ezdxf.entities.insert (top-level), ezdxf.entities.attrib (top-level), ezdxf.entities.polyline (top-level), ezdxf.render.polyline (top-level), ezdxf.entities.lwpolyline (top-level), ezdxf.entities.dimension (top-level), ezdxf.entities.viewport (top-level), ezdxf.entities.ellipse (top-level), ezdxf.entities.xline (top-level), ezdxf.entities.spline (top-level), ezdxf.entities.mesh (top-level), ezdxf.entities.underlay (top-level), ezdxf.entities.leader (top-level), ezdxf.render.leader (top-level), ezdxf.entities.acad_table (top-level), ezdxf.entities.geodata (top-level), ezdxf.graphicsfactory (top-level), ezdxf.math.construct2d (top-level), ezdxf.math.construct3d (top-level), ezdxf.math.parametrize (top-level), ezdxf.math.bezier_interpolation (top-level), ezdxf.math.bspline (top-level), ezdxf.math.bbox (top-level), ezdxf.math.bezier (top-level), ezdxf.math.eulerspiral (top-level), ezdxf.math.ucs (top-level), ezdxf.math.curvetools (top-level), ezdxf.math.polyline (top-level), ezdxf.lldxf.types (top-level), ezdxf.lldxf.validator (top-level)
missing module named fontTools.ttLib.TTFont - imported by fontTools.ttLib (top-level), fontTools.varLib (top-level), fontTools.cffLib (top-level), fontTools.cffLib.CFFToCFF2 (top-level), fontTools.cffLib.CFF2ToCFF (top-level), fontTools.cffLib.width (top-level), fontTools.ttLib.ttVisitor (top-level), fontTools.varLib.varStore (delayed), fontTools.otlLib.optimize.gpos (top-level), fontTools.otlLib.optimize (top-level), fontTools.varLib.stat (top-level), fontTools.colorLib.unbuilder (conditional), fontTools.cffLib.specializer (conditional), fontTools.ttx (top-level), fontTools.ttLib.woff2 (top-level), ezdxf.fonts.font_manager (top-level), ezdxf.fonts.ttfonts (top-level), ezdxf.fonts.fonts (delayed)
missing module named uharfbuzz - imported by fontTools.ttLib.tables.otBase (optional)
missing module named unicodedata2 - imported by fontTools.unicode (delayed, optional)
missing module named xattr - imported by fontTools.misc.macCreatorType (optional)
missing module named cython - imported by fontTools.varLib.iup (optional), fontTools.misc.bezierTools (optional)
missing module named 'lz4.block' - imported by fontTools.ttLib.tables.grUtils (optional)
missing module named lz4 - imported by fontTools.ttLib.tables.grUtils (optional)
missing module named zopfli - imported by fontTools.ttLib.sfnt (delayed, conditional)
missing module named brotli - imported by fontTools.ttLib.woff2 (optional)
missing module named brotlicffi - imported by fontTools.ttLib.woff2 (optional)
missing module named fontTools.ttLib.getTableClass - imported by fontTools.ttLib (top-level), fontTools.ttLib.woff2 (top-level)
missing module named fontTools.ttLib.getTableModule - imported by fontTools.ttLib (top-level), fontTools.ttLib.woff2 (top-level)
missing module named fontTools.ttLib.getSearchRange - imported by fontTools.ttLib (top-level), fontTools.ttLib.tables.otConverters (top-level), fontTools.ttLib.tables._c_m_a_p (top-level), fontTools.ttLib.tables._k_e_r_n (top-level), fontTools.ttLib.woff2 (top-level), fontTools.ttLib.sfnt (delayed, conditional)
missing module named fontTools.ttLib.getClassTag - imported by fontTools.ttLib (top-level), fontTools.ttLib.tables.DefaultTable (top-level)
missing module named fontTools.ttLib.newTable - imported by fontTools.ttLib (top-level), fontTools.varLib (top-level), fontTools.cffLib.CFFToCFF2 (top-level), fontTools.cffLib.CFF2ToCFF (top-level), fontTools.ttLib.tables._n_a_m_e (top-level), fontTools.varLib.featureVars (top-level), fontTools.varLib.cff (top-level)
missing module named ezdxf.math.BoundingBox2d - imported by ezdxf.math (top-level), ezdxf.fonts.shapefile (top-level), ezdxf.npshapes (top-level), ezdxf.tools.text_layout (top-level), ezdxf.fonts.lff (top-level), ezdxf.fonts.ttfonts (top-level), ezdxf.entities.image (top-level), ezdxf.entities.viewport (top-level), ezdxf.math.shape (conditional), ezdxf.math.polyline (top-level)
missing module named ezdxf.math.Matrix44 - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.render.forms (top-level), ezdxf.render.mesh (top-level), ezdxf.entities.xdata (top-level), ezdxf.entities.dxfentity (conditional), ezdxf.entities.dxfgfx (top-level), ezdxf.proxygraphic (top-level), ezdxf.acis.mesh (top-level), ezdxf.acis.entities (top-level), ezdxf.render.curves (top-level), ezdxf.render.mleader (top-level), ezdxf.tools.text_size (top-level), ezdxf.fonts.shapefile (top-level), ezdxf.path.path (top-level), ezdxf.path.converter (top-level), ezdxf.entities.polygon (top-level), ezdxf.math.transformtools (top-level), ezdxf.path.tools (top-level), ezdxf.entities.temporary_transform (conditional), ezdxf.transform (top-level), ezdxf.protocols (conditional), ezdxf.path.shapes (top-level), ezdxf.npshapes (top-level), ezdxf.addons.menger_sponge (top-level), ezdxf.addons.sierpinski_pyramid (top-level), ezdxf.addons.mtxpl (top-level), ezdxf.tools.text_layout (top-level), ezdxf.entities.mtext (top-level), ezdxf.fonts.lff (top-level), ezdxf.fonts.ttfonts (top-level), ezdxf.entities.mleader (top-level), ezdxf.entities.material (top-level), ezdxf.entities.mline (conditional), ezdxf.entities.image (top-level), ezdxf.entities.spatial_filter (top-level), ezdxf.entities.line (top-level), ezdxf.entities.point (top-level), ezdxf.entities.circle (top-level), ezdxf.entities.arc (top-level), ezdxf.entities.shape (conditional), ezdxf.entities.solid (top-level), ezdxf.entities.text (top-level), ezdxf.entities.insert (top-level), ezdxf.entities.attrib (top-level), ezdxf.entities.polyline (top-level), ezdxf.entities.lwpolyline (top-level), ezdxf.lldxf.packedtags (top-level), ezdxf.entities.dimension (top-level), ezdxf.entities.viewport (top-level), ezdxf.entities.ellipse (top-level), ezdxf.entities.xline (top-level), ezdxf.entities.spline (top-level), ezdxf.entities.mesh (top-level), ezdxf.entities.underlay (top-level), ezdxf.entities.leader (conditional), ezdxf.entities.tolerance (conditional), ezdxf.entities.helix (conditional), ezdxf.entities.acis (top-level), ezdxf.entities.light (conditional), ezdxf.entities.acad_table (top-level), ezdxf.entities.geodata (top-level), ezdxf.math.construct2d (top-level), ezdxf.math.construct3d (top-level), ezdxf.math.bspline (conditional), ezdxf.math.bezier (top-level), ezdxf.math.ucs (top-level), ezdxf.math.shape (top-level)
missing module named 'matplotlib.path' - imported by ezdxf.npshapes (delayed)
missing module named ezdxf.entities.Hatch - imported by ezdxf.entities (conditional), ezdxf.proxygraphic (conditional), ezdxf.path.converter (top-level), ezdxf.addons.importer (top-level), ezdxf.render.mline (conditional), ezdxf.graphicsfactory (conditional)
missing module named ezdxf.math.ConstructionArc - imported by ezdxf.math (top-level), ezdxf.proxygraphic (top-level), ezdxf.render.trace (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.render.dim_base (top-level), ezdxf.entities.arc (top-level), ezdxf.graphicsfactory (top-level), ezdxf.math.bspline (conditional)
missing module named ezdxf.math.ConstructionCircle - imported by ezdxf.math (top-level), ezdxf.proxygraphic (top-level)
missing module named ezdxf.math.arc_angle_span_rad - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.render.forms (top-level), ezdxf.math.transformtools (top-level), ezdxf.render.dim_curved (top-level), ezdxf.math.construct2d (top-level)
missing module named ezdxf.math.sign - imported by ezdxf.math (top-level), ezdxf.math.transformtools (top-level)
missing module named ezdxf.math.Y_AXIS - imported by ezdxf.math (top-level), ezdxf.math.transformtools (top-level), ezdxf.entities.mleader (top-level), ezdxf.entities.mline (top-level), ezdxf.entities.ucs (top-level), ezdxf.entities.layout (top-level), ezdxf.entities.insert (top-level), ezdxf.entities.viewport (top-level), ezdxf.entities.geodata (top-level), ezdxf.math.construct2d (top-level), ezdxf.math.construct3d (top-level), ezdxf.math.ucs (top-level)
missing module named ezdxf.math.X_AXIS - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.render.forms (top-level), ezdxf.proxygraphic (top-level), ezdxf.render.mleader (top-level), ezdxf.math.transformtools (top-level), ezdxf.entities.mtext (top-level), ezdxf.entities.mleader (top-level), ezdxf.disassemble (top-level), ezdxf.entities.mline (top-level), ezdxf.entities.ucs (top-level), ezdxf.entities.layout (top-level), ezdxf.entities.insert (top-level), ezdxf.entities.viewport (top-level), ezdxf.entities.ellipse (top-level), ezdxf.entities.leader (top-level), ezdxf.entities.tolerance (top-level), ezdxf.entities.helix (top-level), ezdxf.math.construct2d (top-level), ezdxf.math.construct3d (top-level), ezdxf.math.ucs (top-level)
missing module named ezdxf.math.Z_AXIS - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.render.forms (top-level), ezdxf.proxygraphic (top-level), ezdxf.render.mleader (top-level), ezdxf.path.converter (top-level), ezdxf.math.transformtools (top-level), ezdxf.path.tools (top-level), ezdxf.entities.mtext (top-level), ezdxf.entities.mleader (top-level), ezdxf.disassemble (top-level), ezdxf.entities.mline (top-level), ezdxf.render.dim_base (top-level), ezdxf.entities.vport (top-level), ezdxf.entities.spatial_filter (top-level), ezdxf.entities.line (top-level), ezdxf.entities.point (top-level), ezdxf.entities.circle (top-level), ezdxf.entities.shape (top-level), ezdxf.entities.solid (top-level), ezdxf.entities.text (top-level), ezdxf.entities.insert (top-level), ezdxf.entities.attrib (top-level), ezdxf.entities.polyline (top-level), ezdxf.entities.lwpolyline (top-level), ezdxf.entities.dimension (top-level), ezdxf.entities.viewport (top-level), ezdxf.entities.ellipse (top-level), ezdxf.entities.xline (top-level), ezdxf.entities.spline (top-level), ezdxf.entities.hatch (top-level), ezdxf.entities.mpolygon (top-level), ezdxf.entities.underlay (top-level), ezdxf.entities.leader (top-level), ezdxf.entities.tolerance (top-level), ezdxf.entities.helix (top-level), ezdxf.entities.geodata (top-level), ezdxf.math.construct3d (top-level), ezdxf.math.ucs (top-level)
missing module named ezdxf.math.OCS - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.render.mesh (top-level), ezdxf.entities.dxfgfx (top-level), ezdxf.proxygraphic (top-level), ezdxf.math.triangulation (top-level), ezdxf.render.trace (top-level), ezdxf.render.mleader (top-level), ezdxf.path.path (top-level), ezdxf.path.converter (top-level), ezdxf.math.transformtools (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.path.tools (top-level), ezdxf.entities.mtext (top-level), ezdxf.fonts.lff (delayed), ezdxf.explode (top-level), ezdxf.entities.mline (top-level), ezdxf.render.mline (top-level), ezdxf.entities.line (top-level), ezdxf.entities.point (top-level), ezdxf.entities.attrib (top-level), ezdxf.render.polyline (top-level), ezdxf.entities.dimension (conditional), ezdxf.entities.ellipse (top-level), ezdxf.entities.spline (top-level), ezdxf.math.construct3d (delayed)
missing module named ezdxf.entities.Dimension - imported by ezdxf.entities (top-level), ezdxf.addons.importer (top-level), ezdxf.explode (conditional), ezdxf.entities.dimstyleoverride (conditional), ezdxf.render.dim_base (top-level), ezdxf.render.dim_curved (top-level), ezdxf.render.dim_radius (conditional), ezdxf.render.dim_diameter (conditional), ezdxf.render.dim_linear (conditional), ezdxf.render.dim_ordinate (top-level), ezdxf.render.dimension (conditional), ezdxf.render.leader (conditional), ezdxf.graphicsfactory (conditional)
missing module named ezdxf.math.ConstructionRay - imported by ezdxf.math (top-level), ezdxf.render.trace (top-level), ezdxf.addons.dimlines (top-level), ezdxf.render.dim_linear (top-level)
missing module named ezdxf.math.lerp - imported by ezdxf.math (top-level), ezdxf.addons.dimlines (top-level)
missing module named ezdxf.math.distance - imported by ezdxf.math (top-level), ezdxf.addons.dimlines (top-level)
missing module named 'PySide6.QtGui' - imported by ezdxf.addons.xqt (conditional, optional)
missing module named 'PySide6.QtCore' - imported by ezdxf.addons.xqt (conditional, optional)
missing module named 'PySide6.QtWidgets' - imported by ezdxf.addons.xqt (conditional, optional)
missing module named PySide6 - imported by ezdxf.addons.xqt (conditional, optional)
missing module named ezdxf.math.InsertTransformationError - imported by ezdxf.math (top-level), ezdxf.transform (top-level)
missing module named ezdxf.math.NonUniformScalingError - imported by ezdxf.math (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.transform (top-level), ezdxf.entities.mleader (top-level)
missing module named ezdxf.math.AbstractBoundingBox - imported by ezdxf.math (top-level), ezdxf.path.nesting (top-level), ezdxf.protocols (conditional), ezdxf.math.polyline (top-level)
missing module named ezdxf.math.BoundingBox - imported by ezdxf.math (top-level), ezdxf.render.mesh (top-level), ezdxf.acis.mesh (top-level), ezdxf.render.mleader (top-level), ezdxf.path.path (top-level), ezdxf.path.tools (top-level), ezdxf.npshapes (top-level), ezdxf.disassemble (top-level), ezdxf.bbox (top-level), ezdxf.entities.oleframe (top-level), ezdxf.math.construct3d (delayed), ezdxf.math.curvetools (top-level), ezdxf.math.polyline (top-level)
missing module named ezdxf.math.Bezier4P - imported by ezdxf.math (top-level), ezdxf.render.curves (top-level), ezdxf.path.path (top-level), ezdxf.path.converter (top-level), ezdxf.render.hatching (top-level), ezdxf.path.tools (top-level), ezdxf.npshapes (top-level), ezdxf.math.bezier_interpolation (top-level), ezdxf.math.bspline (conditional), ezdxf.math.curvetools (top-level)
missing module named ezdxf.math.Bezier3P - imported by ezdxf.math (top-level), ezdxf.path.path (top-level), ezdxf.path.converter (top-level), ezdxf.render.hatching (top-level), ezdxf.path.tools (top-level), ezdxf.npshapes (top-level), ezdxf.math.curvetools (top-level)
missing module named ezdxf.math.has_clockwise_orientation - imported by ezdxf.math (top-level), ezdxf.path.path (top-level), ezdxf.npshapes (top-level)
missing module named ezdxf.math.bulge_to_arc - imported by ezdxf.math (top-level), ezdxf.render.trace (top-level), ezdxf.fonts.shapefile (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.path.tools (top-level), ezdxf.render.polyline (top-level)
missing module named ezdxf.math.ConstructionEllipse - imported by ezdxf.math (top-level), ezdxf.fonts.shapefile (top-level), ezdxf.path.converter (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.path.tools (top-level), ezdxf.entities.ellipse (top-level), ezdxf.entities.spline (top-level), ezdxf.math.bspline (conditional)
missing module named ezdxf.math.intersection_line_line_2d - imported by ezdxf.math (top-level), ezdxf.render.dim_curved (top-level), ezdxf.math.line (top-level), ezdxf.math.polyline (top-level)
missing module named ezdxf.math.decdeg2dms - imported by ezdxf.math (top-level), ezdxf.render.dim_curved (top-level)
missing module named ezdxf.math.PassTroughUCS - imported by ezdxf.math (top-level), ezdxf.render.dim_base (top-level)
missing module named ezdxf.math.ConstructionBox - imported by ezdxf.math (top-level), ezdxf.render.dim_base (top-level)
missing module named ezdxf.math.ConstructionLine - imported by ezdxf.math (top-level), ezdxf.render.dim_base (top-level)
missing module named jinja2 - imported by pyparsing.diagram (top-level), pandas.io.formats.style (top-level)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named ezdxf.math.arc_angle_span_deg - imported by ezdxf.math (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.entities.arc (top-level), ezdxf.graphicsfactory (top-level), ezdxf.math.bspline (top-level)
missing module named ezdxf.math.fit_points_to_cad_cv - imported by ezdxf.math (top-level), ezdxf.render.mleader (top-level), ezdxf.path.converter (top-level), ezdxf.entities.spline (top-level), ezdxf.render.leader (top-level), ezdxf.graphicsfactory (top-level)
missing module named ezdxf.math.global_bspline_interpolation - imported by ezdxf.math (top-level), ezdxf.render.forms (top-level), ezdxf.render.curves (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.graphicsfactory (top-level)
missing module named ezdxf.math.param_to_angle - imported by ezdxf.math (top-level), ezdxf.entities.boundary_paths (top-level)
missing module named ezdxf.math.angle_to_param - imported by ezdxf.math (top-level), ezdxf.entities.boundary_paths (top-level)
missing module named ezdxf.math.open_uniform_knot_vector - imported by ezdxf.math (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.entities.spline (top-level)
missing module named ezdxf.math.BSpline - imported by ezdxf.math (top-level), ezdxf.render.r12spline (top-level), ezdxf.render.curves (top-level), ezdxf.render.trace (top-level), ezdxf.path.converter (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.path.tools (top-level), ezdxf.entities.spline (top-level), ezdxf.math.curvetools (top-level)
missing module named ezdxf.math.OCSTransform - imported by ezdxf.math (top-level), ezdxf.entities.mleader (top-level)
missing module named ezdxf.math.WCSTransform - imported by ezdxf.math (top-level), ezdxf.entities.mleader (top-level)
missing module named ezdxf.math.is_point_left_of_line - imported by ezdxf.math (top-level), ezdxf.render.mleader (top-level)
missing module named ezdxf.path.from_hatch_boundary_path - imported by ezdxf.path (delayed), ezdxf.render.hatching (delayed)
missing module named ezdxf.math.quadratic_to_cubic_bezier - imported by ezdxf.math (top-level), ezdxf.render.hatching (top-level)
missing module named ezdxf.math.intersection_ray_cubic_bezier_2d - imported by ezdxf.math (top-level), ezdxf.render.hatching (top-level)
missing module named ezdxf.math.ParallelRaysError - imported by ezdxf.math (top-level), ezdxf.render.trace (top-level)
missing module named ezdxf.math.is_face_normal_pointing_outwards - imported by ezdxf.math (delayed), ezdxf.render.mesh (delayed)
missing module named ezdxf.math.subdivide_ngons - imported by ezdxf.math (top-level), ezdxf.render.mesh (top-level)
missing module named ezdxf.math.subdivide_face - imported by ezdxf.math (top-level), ezdxf.render.mesh (top-level)
missing module named ezdxf.math.safe_normal_vector - imported by ezdxf.math (top-level), ezdxf.render.mesh (top-level), ezdxf.math.triangulation (top-level)
missing module named ezdxf.math.normal_vector_3p - imported by ezdxf.math (top-level), ezdxf.render.mesh (top-level)
missing module named ezdxf.math.is_planar_face - imported by ezdxf.math (top-level), ezdxf.render.mesh (top-level)
missing module named ezdxf.math.area - imported by ezdxf.math (top-level), ezdxf.render.mesh (top-level)
missing module named ezdxf.math.EulerSpiral - imported by ezdxf.math (top-level), ezdxf.render.forms (top-level), ezdxf.render.curves (top-level)
missing module named ezdxf.math.closed_uniform_bspline - imported by ezdxf.math (top-level), ezdxf.render.r12spline (top-level), ezdxf.render.curves (top-level)
missing module named ezdxf.math.open_uniform_bspline - imported by ezdxf.math (top-level), ezdxf.render.curves (top-level)
missing module named ezdxf.math.round_knots - imported by ezdxf.math (top-level), ezdxf.entities.spline (top-level)
missing module named ezdxf.math.required_control_points - imported by ezdxf.math (top-level), ezdxf.entities.spline (top-level)
missing module named ezdxf.math.required_fit_points - imported by ezdxf.math (top-level), ezdxf.entities.spline (top-level)
missing module named ezdxf.math.required_knot_values - imported by ezdxf.math (top-level), ezdxf.entities.spline (top-level)
missing module named ezdxf.math.uniform_knot_vector - imported by ezdxf.math (top-level), ezdxf.entities.spline (top-level)
missing module named ezdxf.math.enclosing_angles - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level)
missing module named ezdxf.math.distance_point_line_3d - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.math.bspline (top-level)
missing module named ezdxf.math.arc_segment_count - imported by ezdxf.math (top-level), ezdxf.entities.circle (top-level)
missing module named ezdxf.entities.SplineEdge - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.entities.EllipseEdge - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.entities.ArcEdge - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.entities.LineEdge - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.entities.EdgePath - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.entities.PolylinePath - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.entities.AbstractBoundaryPath - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.entities.BoundaryPaths - imported by ezdxf.entities (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.math.intersection_ray_ray_3d - imported by ezdxf.math (top-level), ezdxf.render.forms (top-level), ezdxf.math.construct3d (delayed)
missing module named ezdxf.math.UVec - imported by ezdxf.math (conditional), ezdxf.math._vector (conditional), ezdxf.math._matrix44 (conditional), ezdxf.math._construct (conditional), ezdxf.math._bezier4p (conditional), ezdxf.math.ellipse (top-level), ezdxf.layouts.blocklayout (top-level), ezdxf.sections.blocks (top-level), ezdxf.render.arrows (top-level), ezdxf.render.forms (top-level), ezdxf.render.mesh (top-level), ezdxf.layouts.layout (top-level), ezdxf.xref (top-level), ezdxf.entities.dxfgfx (top-level), ezdxf.eztypes (conditional), ezdxf.math.triangulation (top-level), ezdxf.render.r12spline (top-level), ezdxf.render.curves (top-level), ezdxf.render.trace (top-level), ezdxf.render.mleader (top-level), ezdxf.fonts.shapefile (top-level), ezdxf.path.path (top-level), ezdxf.path.converter (top-level), ezdxf.math.transformtools (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.entities.pattern (top-level), ezdxf.path.tools (top-level), ezdxf.transform (top-level), ezdxf.path.shapes (top-level), ezdxf.npshapes (top-level), ezdxf.addons.mtextsurrogate (top-level), ezdxf.addons.tablepainter (top-level), ezdxf.addons.menger_sponge (top-level), ezdxf.addons.sierpinski_pyramid (top-level), ezdxf.addons.dimlines (top-level), ezdxf.tools.text (top-level), ezdxf.entities.mtext (top-level), ezdxf.fonts.ttfonts (top-level), ezdxf.entities.mline (top-level), ezdxf.entities.dimstyleoverride (top-level), ezdxf.render.dim_base (top-level), ezdxf.render.dim_radius (top-level), ezdxf.render.dim_linear (top-level), ezdxf.entities.image (top-level), ezdxf.entities.spatial_filter (top-level), ezdxf.entities.text (top-level), ezdxf.entities.insert (top-level), ezdxf.entities.polyline (top-level), ezdxf.entities.spline (top-level), ezdxf.entities.mesh (top-level), ezdxf.entities.underlay (top-level), ezdxf.entities.leader (top-level), ezdxf.entities.geodata (top-level), ezdxf.graphicsfactory (top-level), ezdxf.math.construct2d (top-level), ezdxf.math.construct3d (top-level), ezdxf.math.bezier_interpolation (top-level), ezdxf.math.bspline (top-level), ezdxf.math.bbox (top-level), ezdxf.math.bezier (top-level), ezdxf.math.ucs (top-level), ezdxf.math.bulge (top-level), ezdxf.math.arc (top-level), ezdxf.math.circle (top-level), ezdxf.math.line (top-level), ezdxf.math.box (top-level), ezdxf.math.shape (top-level), ezdxf.math.offset2d (top-level), ezdxf.math.curvetools (top-level), ezdxf.math.polyline (top-level)
missing module named ezdxf.math.NULLVEC - imported by ezdxf.math (top-level), ezdxf.math.ellipse (top-level), ezdxf.audit (top-level), ezdxf.sections.blocks (top-level), ezdxf.render.arrows (top-level), ezdxf.render.forms (top-level), ezdxf.render.mesh (top-level), ezdxf.acis.mesh (top-level), ezdxf.acis.entities (top-level), ezdxf.render.mleader (top-level), ezdxf.path.path (top-level), ezdxf.path.converter (top-level), ezdxf.entities.mtext (top-level), ezdxf.entities.mleader (top-level), ezdxf.entities.mline (top-level), ezdxf.render.dim_curved (top-level), ezdxf.render.dim_ordinate (top-level), ezdxf.entities.view (top-level), ezdxf.entities.vport (top-level), ezdxf.entities.ucs (top-level), ezdxf.entities.layout (top-level), ezdxf.entities.spatial_filter (top-level), ezdxf.entities.line (top-level), ezdxf.entities.point (top-level), ezdxf.render.point (top-level), ezdxf.entities.circle (top-level), ezdxf.entities.shape (top-level), ezdxf.entities.solid (top-level), ezdxf.entities.text (top-level), ezdxf.entities.insert (top-level), ezdxf.entities.attrib (top-level), ezdxf.entities.block (top-level), ezdxf.entities.polyline (top-level), ezdxf.entities.dimension (top-level), ezdxf.entities.viewport (top-level), ezdxf.entities.ellipse (top-level), ezdxf.entities.xline (top-level), ezdxf.entities.spline (top-level), ezdxf.entities.hatch (top-level), ezdxf.entities.mpolygon (top-level), ezdxf.entities.underlay (top-level), ezdxf.entities.leader (top-level), ezdxf.entities.tolerance (top-level), ezdxf.entities.helix (top-level), ezdxf.entities.geodata (top-level), ezdxf.graphicsfactory (top-level), ezdxf.math.bspline (top-level), ezdxf.math.bezier (top-level), ezdxf.math.polyline (top-level), ezdxf.lldxf.validator (top-level)
missing module named ezdxf.math.Shape2d - imported by ezdxf.math (top-level), ezdxf.render.arrows (top-level)
missing module named ezdxf.math.Vec2 - imported by ezdxf.math (top-level), ezdxf.render.arrows (top-level), ezdxf.render.forms (top-level), ezdxf.layouts.layout (top-level), ezdxf.proxygraphic (top-level), ezdxf.query (top-level), ezdxf.math.triangulation (top-level), ezdxf.render.curves (top-level), ezdxf.render.trace (top-level), ezdxf.render.mleader (top-level), ezdxf.tools.text_size (top-level), ezdxf.fonts.shapefile (top-level), ezdxf.path.commands (top-level), ezdxf.path.converter (top-level), ezdxf.tools.pattern (top-level), ezdxf.math.transformtools (top-level), ezdxf.entities.boundary_paths (top-level), ezdxf.entities.pattern (top-level), ezdxf.render.hatching (top-level), ezdxf.path.tools (top-level), ezdxf.npshapes (top-level), ezdxf.addons.tablepainter (top-level), ezdxf.addons.dimlines (top-level), ezdxf.tools.text (top-level), ezdxf.fonts.lff (top-level), ezdxf.render.dim_base (top-level), ezdxf.render.dim_curved (top-level), ezdxf.render.dim_diameter (top-level), ezdxf.render.dim_radius (top-level), ezdxf.render.dim_linear (top-level), ezdxf.render.dim_ordinate (top-level), ezdxf.entities.image (top-level), ezdxf.entities.vport (top-level), ezdxf.entities.layout (top-level), ezdxf.entities.spatial_filter (top-level), ezdxf.entities.viewport (top-level), ezdxf.entities.geodata (top-level), ezdxf.math.construct2d (top-level), ezdxf.math.construct3d (top-level), ezdxf.math.bbox (top-level), ezdxf.math.bulge (top-level), ezdxf.math.arc (top-level), ezdxf.math.circle (top-level), ezdxf.math.line (top-level), ezdxf.math.box (top-level), ezdxf.math.shape (top-level), ezdxf.math.offset2d (top-level), ezdxf.math.curvetools (top-level), ezdxf.math.polyline (top-level)
missing module named ezdxf.math.basic_transformation - imported by ezdxf.math (top-level), ezdxf.path.shapes (top-level)
missing module named ezdxf.math.bezier_to_bspline - imported by ezdxf.math (delayed), ezdxf.path.converter (delayed), ezdxf.math.bspline (delayed)
missing module named ezdxf.math.quadratic_bezier_bbox - imported by ezdxf.math (top-level), ezdxf.path.tools (top-level)
missing module named ezdxf.math.cubic_bezier_bbox - imported by ezdxf.math (top-level), ezdxf.path.tools (top-level)
missing module named ezdxf.math.cubic_bezier_arc_parameters - imported by ezdxf.math (top-level), ezdxf.path.tools (top-level), ezdxf.path.shapes (top-level)
missing module named ezdxf.math.inscribe_circle_tangent_length - imported by ezdxf.math (top-level), ezdxf.path.tools (top-level)
missing module named ezdxf.math.linear_vertex_spacing - imported by ezdxf.math (top-level), ezdxf.path.tools (top-level)
missing module named ezdxf.math.reverse_bezier_curves - imported by ezdxf.math (top-level), ezdxf.path.tools (top-level)
missing module named ezdxf.math.cubic_bezier_from_ellipse - imported by ezdxf.math (top-level), ezdxf.path.tools (top-level)
missing module named ezdxf.math.have_bezier_curves_g1_continuity - imported by ezdxf.math (top-level), ezdxf.path.converter (top-level)
missing module named ezdxf.math.AnyVec - imported by ezdxf.math (conditional), ezdxf.math._vector (conditional), ezdxf.math.curvetools (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named StringIO - imported by six (conditional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.arrow.accessors (conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pyarrow - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.utils (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io._util (conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional)
missing module named scipy - imported by pandas.core.dtypes.common (delayed, conditional, optional), pandas.core.missing (delayed)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional)
missing module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named cStringIO - imported by xlrd.timemachine (conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional)
missing module named matplotlib - imported by pandas.plotting._core (conditional), pandas.io.formats.style (optional)
missing module named 'matplotlib.colors' - imported by pandas.plotting._misc (conditional), pandas.io.formats.style (conditional)
missing module named markupsafe - imported by pandas.io.formats.style_render (top-level)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named fsspec - imported by pandas.io.orc (conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named google - imported by pandas.io.gbq (conditional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4 - imported by pandas.io.html (delayed)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed)
missing module named 'matplotlib.axes' - imported by pandas.plotting._misc (conditional), pandas._testing.asserters (delayed)
missing module named 'matplotlib.artist' - imported by pandas._testing.asserters (delayed)
missing module named 'matplotlib.table' - imported by pandas.plotting._misc (conditional)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional)
