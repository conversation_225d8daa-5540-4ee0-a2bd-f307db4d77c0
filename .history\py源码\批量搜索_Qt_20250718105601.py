import sys
import os
import glob
import configparser
import datetime
import shutil
from send2trash import send2trash
import win32clipboard
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QTextEdit, QListWidget, QCheckBox, QGroupBox,
                             QMessageBox, QFileDialog, QGridLayout, QFormLayout,
                             QFrame, QScrollArea, QMenu, QAbstractItemView,
                             QSplitter, QTabWidget, QListWidgetItem, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QEvent
from PyQt5.QtGui import QFont, QColor, QPalette, QBrush, QCursor


class ClickableLegendItem(QWidget):
    """可点击的图例项组件"""
    clicked = pyqtSignal(str, bool)  # 关键词, 是否选中

    def __init__(self, keyword, color, parent=None):
        super().__init__(parent)
        self.keyword = keyword
        self.color = color
        self.is_selected = False
        self.is_hovered = False

        self.init_ui()
        self.setFixedHeight(30)
        self.setCursor(QCursor(Qt.PointingHandCursor))

        # 启用鼠标跟踪以支持悬停效果
        self.setMouseTracking(True)

    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)  # 紧凑的边距
        layout.setSpacing(8)  # 减少间距

        # 颜色方块
        self.color_label = QLabel()
        self.color_label.setFixedSize(16, 16)  # 稍微小一点的颜色方块
        self.update_color_style()

        # 关键词文本
        self.text_label = QLabel(self.keyword)
        self.text_label.setFont(QFont("Microsoft YaHei", 8))

        layout.addWidget(self.color_label)
        layout.addWidget(self.text_label)
        layout.addStretch()

        self.setLayout(layout)
        self.update_style()

    def update_color_style(self):
        """更新颜色方块样式"""
        border_color = "black"
        border_width = 1

        if self.is_selected:
            border_color = "#0078d4"  # 蓝色边框表示选中
            border_width = 2
        elif self.is_hovered:
            border_color = "#666666"  # 灰色边框表示悬停
            border_width = 2

        self.color_label.setStyleSheet(
            f"background-color: rgb({self.color.red()}, {self.color.green()}, {self.color.blue()}); "
            f"border: {border_width}px solid {border_color};"
        )

    def update_style(self):
        """更新整体样式"""
        bg_color = "transparent"
        if self.is_selected:
            bg_color = "#e3f2fd"  # 浅蓝色背景表示选中
        elif self.is_hovered:
            bg_color = "#f5f5f5"  # 浅灰色背景表示悬停

        self.setStyleSheet(f"background-color: {bg_color}; border-radius: 3px;")
        self.update_color_style()

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            # 检查是否按住Ctrl键进行多选
            modifiers = event.modifiers()
            ctrl_pressed = modifiers & Qt.ControlModifier

            if not ctrl_pressed:
                # 单选模式：切换当前项的选中状态
                self.is_selected = not self.is_selected
            else:
                # 多选模式：直接切换选中状态
                self.is_selected = not self.is_selected

            self.update_style()
            self.clicked.emit(self.keyword, self.is_selected)

    def enterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.update_style()

    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        self.update_style()

    def set_selected(self, selected):
        """设置选中状态"""
        self.is_selected = selected
        self.update_style()


class SearchThread(QThread):
    """搜索线程，避免界面卡顿"""
    finished = pyqtSignal(list, dict, list, list, dict)  # 匹配文件, 文件路径字典, 未匹配文件, 未匹配搜索词, 文件关键词映射
    progress = pyqtSignal(str)  # 进度信息
    
    def __init__(self, search_terms, search_path, fuzzy_search, case_sensitive, selected_exts):
        super().__init__()
        self.search_terms = search_terms
        self.search_path = search_path
        self.fuzzy_search = fuzzy_search
        self.case_sensitive = case_sensitive
        self.selected_exts = selected_exts
    
    def run(self):
        try:
            # 记录搜索词和匹配结果
            term_matches = {term: [] for term in self.search_terms}
            file_keyword_mapping = {}  # 文件到关键词的映射

            # 收集所有符合条件的文件
            self.progress.emit("正在扫描文件...")
            all_files = set()
            for root_dir, _, _ in os.walk(self.search_path):
                for ext in self.selected_exts:
                    all_files.update(glob.glob(os.path.join(root_dir, f"*{ext}")))

            self.progress.emit(f"找到 {len(all_files)} 个文件，开始匹配...")

            # 执行批量搜索 - 每个关键词独立搜索
            matched_files = []
            file_paths = {}
            unmatched_files = list(all_files)

            for term_index, term in enumerate(self.search_terms):
                if not term.strip():
                    continue

                self.progress.emit(f"正在搜索关键词: {term} ({term_index+1}/{len(self.search_terms)})")

                term_matched_files = []
                for i, file_path in enumerate(all_files):
                    if i % 200 == 0:  # 每处理200个文件更新一次进度
                        self.progress.emit(f"搜索 '{term}': {i+1}/{len(all_files)}")

                    filename = os.path.basename(file_path)

                    # 检查是否匹配当前搜索词
                    if self.case_sensitive:
                        term_match = term in filename
                    else:
                        term_match = term.lower() in filename.lower()

                    if term_match:
                        rel_path = os.path.relpath(os.path.dirname(file_path), start=self.search_path)
                        display_text = f"{filename} (路径: {rel_path}) [关键词: {term}]"

                        term_matched_files.append((display_text, file_path, term))
                        term_matches[term].append(file_path)

                        # 记录文件到关键词的映射（支持多关键词）
                        if display_text not in file_keyword_mapping:
                            file_keyword_mapping[display_text] = []
                        file_keyword_mapping[display_text].append(term)

                        # 从未匹配列表中移除
                        if file_path in unmatched_files:
                            unmatched_files.remove(file_path)

                # 添加当前关键词的匹配结果
                for display_text, file_path, matched_term in term_matched_files:
                    matched_files.append((display_text, matched_term))
                    file_paths[display_text] = file_path

            # 计算未匹配的搜索词
            unmatched_terms = [term for term, matches in term_matches.items() if not matches]

            # 转换格式以兼容原有接口
            matched_files_list = [item[0] for item in matched_files]
            matched_files_with_terms = matched_files  # 保留关键词信息用于着色

            self.finished.emit(matched_files_list, file_paths, unmatched_files, unmatched_terms, file_keyword_mapping)

        except Exception as e:
            self.progress.emit(f"搜索出错: {str(e)}")


class BatchSearchApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_data()
        self.load_config()
    
    def init_ui(self):
        self.setWindowTitle("批量搜索工具 - Qt版")
        self.setGeometry(500, 300, 800, 600)
        self.setMinimumSize(800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 路径选择区域
        path_group = QGroupBox("搜索设置")
        path_layout = QFormLayout()
        
        # 搜索路径
        path_row = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_path)
        path_row.addWidget(self.path_edit)
        path_row.addWidget(self.browse_btn)
        path_layout.addRow("搜索路径:", path_row)
        
        # 搜索选项
        options_row = QHBoxLayout()
        self.fuzzy_check = QCheckBox("模糊搜索")
        self.case_sensitive_check = QCheckBox("区分大小写")
        self.color_match_check = QCheckBox("色彩匹配")
        self.color_match_check.setChecked(True)
        self.color_match_check.toggled.connect(self.toggle_color_legend)
        self.auto_save_check = QCheckBox("关闭自动保存结果")
        self.auto_save_check.setChecked(True)
        options_row.addWidget(self.fuzzy_check)
        options_row.addWidget(self.case_sensitive_check)
        options_row.addWidget(self.color_match_check)
        options_row.addWidget(self.auto_save_check)
        options_row.addStretch()
        path_layout.addRow("搜索选项:", options_row)
        
        path_group.setLayout(path_layout)
        main_layout.addWidget(path_group)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = QWidget()
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        
        # 搜索内容输入
        input_group = QGroupBox("搜索内容")
        input_layout = QVBoxLayout()
        
        self.input_text = QTextEdit()
        self.input_text.setMaximumHeight(200)
        input_layout.addWidget(self.input_text)
        
        # 排序按钮
        sort_layout = QHBoxLayout()
        self.sort_btn = QPushButton("升序")
        self.sort_btn.clicked.connect(self.toggle_sort_order)
        self.search_btn = QPushButton("搜索")
        self.search_btn.clicked.connect(self.search)
        sort_layout.addWidget(self.sort_btn)
        sort_layout.addWidget(self.search_btn)
        sort_layout.addStretch()
        input_layout.addLayout(sort_layout)
        
        input_group.setLayout(input_layout)
        left_layout.addWidget(input_group)
        
        # 文件后缀选择
        ext_group = QGroupBox("文件后缀")
        ext_layout = QVBoxLayout()
        
        # 添加扩展名输入
        add_ext_layout = QHBoxLayout()
        self.ext_entry = QLineEdit()
        self.ext_entry.setPlaceholderText("输入新扩展名")
        self.add_ext_btn = QPushButton("+")
        self.add_ext_btn.clicked.connect(self.add_extension)
        add_ext_layout.addWidget(self.ext_entry)
        add_ext_layout.addWidget(self.add_ext_btn)
        ext_layout.addLayout(add_ext_layout)
        
        # 扩展名复选框容器
        self.ext_scroll = QScrollArea()
        self.ext_widget = QWidget()
        self.ext_widget_layout = QVBoxLayout()
        self.ext_widget.setLayout(self.ext_widget_layout)
        self.ext_scroll.setWidget(self.ext_widget)
        self.ext_scroll.setWidgetResizable(True)
        self.ext_scroll.setMaximumHeight(150)
        ext_layout.addWidget(self.ext_scroll)
        
        ext_group.setLayout(ext_layout)
        left_layout.addWidget(ext_group)
        
        splitter.addWidget(left_panel)
        
        # 右侧结果面板
        right_panel = QWidget()
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        # 色彩图例区域
        self.legend_group = QGroupBox("色彩图例")
        self.legend_layout = QVBoxLayout()
        self.legend_scroll = QScrollArea()
        self.legend_widget = QWidget()
        self.legend_widget_layout = QVBoxLayout()
        self.legend_widget.setLayout(self.legend_widget_layout)
        self.legend_scroll.setWidget(self.legend_widget)
        self.legend_scroll.setWidgetResizable(True)
        self.legend_scroll.setMaximumHeight(120)
        self.legend_layout.addWidget(self.legend_scroll)
        self.legend_group.setLayout(self.legend_layout)
        self.legend_group.setVisible(True)  # 默认显示
        right_layout.addWidget(self.legend_group)

        # 搜索结果
        result_group = QGroupBox("搜索结果")
        result_layout = QVBoxLayout()

        self.result_list = QListWidget()
        self.result_list.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.result_list.itemDoubleClicked.connect(self.open_file)
        self.result_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.result_list.customContextMenuRequested.connect(self.show_context_menu)
        result_layout.addWidget(self.result_list)

        result_group.setLayout(result_layout)
        right_layout.addWidget(result_group)
        
        splitter.addWidget(right_panel)
        splitter.setSizes([300, 500])
        
        # 底部操作区域
        bottom_group = QGroupBox("文件操作")
        bottom_layout = QFormLayout()
        
        # 复制路径
        copy_layout = QHBoxLayout()
        self.copy_path_edit = QLineEdit()
        self.copy_path_edit.setPlaceholderText("选择复制目标路径")
        self.copy_path_btn = QPushButton("浏览")
        self.copy_path_btn.clicked.connect(self.browse_copy_path)
        self.copy_btn = QPushButton("复制文件")
        self.copy_btn.clicked.connect(self.copy_to_path)
        copy_layout.addWidget(self.copy_path_edit)
        copy_layout.addWidget(self.copy_path_btn)
        copy_layout.addWidget(self.copy_btn)
        bottom_layout.addRow("复制到:", copy_layout)
        
        # 其他操作按钮
        action_layout = QHBoxLayout()
        self.config_btn = QPushButton("打开配置文件")
        self.config_btn.clicked.connect(self.open_config_file)
        action_layout.addWidget(self.config_btn)
        action_layout.addStretch()
        bottom_layout.addRow("其他操作:", action_layout)
        
        bottom_group.setLayout(bottom_layout)
        main_layout.addWidget(bottom_group)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def init_data(self):
        """初始化数据"""
        self.config = configparser.ConfigParser()
        self.config_file = "config.ini"
        self.ext_checkboxes = {}
        self.file_paths = {}
        self.sort_order = 1  # 1为升序，-1为降序

        # 色彩匹配颜色列表
        self.colors = [
            QColor(255, 182, 193),  # 浅粉色
            QColor(173, 216, 230),  # 浅蓝色
            QColor(144, 238, 144),  # 浅绿色
            QColor(255, 218, 185),  # 桃色
            QColor(221, 160, 221),  # 梅红色
            QColor(255, 255, 224),  # 浅黄色
            QColor(176, 196, 222),  # 浅钢蓝色
            QColor(255, 160, 122),  # 浅鲑鱼色
            QColor(152, 251, 152),  # 淡绿色
            QColor(255, 192, 203),  # 粉红色
        ]

        # 初始化默认扩展名
        self.init_extensions()

        # 初始化色彩图例
        self.current_search_terms = []
    
    def init_extensions(self):
        """初始化扩展名复选框"""
        default_exts = ['.DWG', '.DXF', '.PDF', '.xlsx', '.jpg']
        
        for ext in default_exts:
            self.add_extension_checkbox(ext, True)
    
    def add_extension_checkbox(self, ext, checked=True):
        """添加扩展名复选框"""
        if ext not in self.ext_checkboxes:
            checkbox = QCheckBox(ext)
            checkbox.setChecked(checked)
            self.ext_checkboxes[ext] = checkbox
            self.ext_widget_layout.addWidget(checkbox)
    
    def add_extension(self):
        """添加新的文件扩展名"""
        new_ext = self.ext_entry.text().strip()
        if not new_ext:
            return

        if not new_ext.startswith('.'):
            new_ext = '.' + new_ext

        if new_ext not in self.ext_checkboxes:
            self.add_extension_checkbox(new_ext, True)
            self.ext_entry.clear()
            self.save_config()
        else:
            QMessageBox.information(self, "提示", "该扩展名已存在")

    def toggle_color_legend(self):
        """切换色彩图例显示"""
        is_visible = self.color_match_check.isChecked()
        self.legend_group.setVisible(is_visible)

        # 如果禁用色彩匹配，清除现有颜色
        if not is_visible:
            self.clear_result_colors()
        else:
            # 如果启用色彩匹配且有搜索结果，重新应用颜色
            if self.current_search_terms:
                self.apply_colors_to_results()

    def clear_result_colors(self):
        """清除搜索结果的颜色"""
        for i in range(self.result_list.count()):
            item = self.result_list.item(i)
            if item:
                item.setBackground(QColor())  # 清除背景色

    def apply_colors_to_results(self):
        """为搜索结果应用颜色"""
        for i in range(self.result_list.count()):
            item = self.result_list.item(i)
            if item:
                file_item = item.text()
                # 从文件项中提取关键词
                if "[关键词: " in file_item:
                    keyword = file_item.split("[关键词: ")[1].split("]")[0]
                    if keyword in self.current_search_terms:
                        color_index = self.current_search_terms.index(keyword) % len(self.colors)
                        item.setBackground(self.colors[color_index])

    def update_color_legend(self, search_terms):
        """更新色彩图例"""
        # 清除现有图例
        for i in reversed(range(self.legend_widget_layout.count())):
            item = self.legend_widget_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)

        self.current_search_terms = search_terms

        # 创建新的图例项
        for i, term in enumerate(search_terms):
            color_index = i % len(self.colors)
            color = self.colors[color_index]

            # 创建图例项布局
            legend_item = QHBoxLayout()

            # 颜色方块
            color_label = QLabel()
            color_label.setFixedSize(20, 20)
            color_label.setStyleSheet(f"background-color: rgb({color.red()}, {color.green()}, {color.blue()}); border: 1px solid black;")

            # 关键词文本
            text_label = QLabel(term)
            text_label.setFont(QFont("Microsoft YaHei", 8))

            legend_item.addWidget(color_label)
            legend_item.addWidget(text_label)
            legend_item.addStretch()

            # 创建容器widget
            legend_widget = QWidget()
            legend_widget.setLayout(legend_item)
            self.legend_widget_layout.addWidget(legend_widget)
    
    def browse_path(self):
        """浏览搜索路径"""
        path = QFileDialog.getExistingDirectory(self, "选择搜索路径")
        if path:
            self.path_edit.setText(path)
    
    def browse_copy_path(self):
        """浏览复制目标路径"""
        path = QFileDialog.getExistingDirectory(self, "选择复制目标路径")
        if path:
            self.copy_path_edit.setText(path)
    
    def toggle_sort_order(self):
        """切换排序顺序"""
        self.sort_order *= -1
        self.sort_btn.setText("升序" if self.sort_order == 1 else "降序")
        self.sort_input_content()
        self.sort_results()
    
    def sort_input_content(self):
        """对输入框内容进行排序"""
        content = self.input_text.toPlainText().splitlines()
        content = [line for line in content if line.strip()]
        content_sorted = sorted(content, key=lambda x: x.strip().lower(),
                               reverse=(self.sort_order == -1))
        self.input_text.setPlainText("\n".join(content_sorted))
    
    def sort_results(self):
        """对搜索结果进行排序"""
        items = []
        for i in range(self.result_list.count()):
            items.append(self.result_list.item(i).text())
        
        if not items:
            return
        
        def get_sort_key(item):
            return item.split(" (路径:")[0].lower()
        
        items_sorted = sorted(items, key=get_sort_key,
                             reverse=(self.sort_order == -1))
        
        self.result_list.clear()
        for item in items_sorted:
            self.result_list.addItem(item)
    
    def search(self):
        """执行搜索"""
        search_terms = [term.strip() for term in self.input_text.toPlainText().splitlines() if term.strip()]
        search_path = self.path_edit.text()
        fuzzy_search = self.fuzzy_check.isChecked()
        case_sensitive = self.case_sensitive_check.isChecked()
        selected_exts = [ext for ext, checkbox in self.ext_checkboxes.items() if checkbox.isChecked()]
        
        if not search_path:
            QMessageBox.critical(self, "错误", "请先选择搜索路径")
            return
        
        if not search_terms:
            QMessageBox.critical(self, "错误", "请输入搜索内容")
            return
        
        if not selected_exts:
            QMessageBox.critical(self, "错误", "请至少选择一个文件扩展名")
            return
        
        # 清空之前的结果
        self.result_list.clear()
        self.file_paths.clear()
        
        # 禁用搜索按钮
        self.search_btn.setEnabled(False)
        self.search_btn.setText("搜索中...")
        
        # 创建并启动搜索线程
        self.search_thread = SearchThread(search_terms, search_path, fuzzy_search, case_sensitive, selected_exts)
        self.search_thread.finished.connect(self.on_search_finished)
        self.search_thread.progress.connect(self.on_search_progress)
        self.search_thread.start()
    
    def on_search_progress(self, message):
        """更新搜索进度"""
        self.statusBar().showMessage(message)
    
    def on_search_finished(self, matched_files, file_paths, unmatched_files, unmatched_terms, file_keyword_mapping=None):
        """搜索完成处理"""
        # 恢复搜索按钮
        self.search_btn.setEnabled(True)
        self.search_btn.setText("搜索")

        # 更新结果
        self.file_paths = file_paths
        self.result_list.clear()

        # 获取搜索词列表用于色彩匹配
        search_terms = [term.strip() for term in self.input_text.toPlainText().splitlines() if term.strip()]

        # 更新色彩图例
        if self.color_match_check.isChecked() and search_terms:
            self.update_color_legend(search_terms)

        # 添加结果项并应用色彩匹配
        for file_item in matched_files:
            item = QListWidgetItem(file_item)

            # 如果启用色彩匹配，根据关键词设置颜色
            if self.color_match_check.isChecked():
                # 从文件项中提取关键词
                if "[关键词: " in file_item:
                    keyword = file_item.split("[关键词: ")[1].split("]")[0]
                    if keyword in search_terms:
                        color_index = search_terms.index(keyword) % len(self.colors)
                        item.setBackground(self.colors[color_index])

                # 处理多关键词匹配的情况
                elif file_keyword_mapping and file_item in file_keyword_mapping:
                    keywords = file_keyword_mapping[file_item]
                    if len(keywords) == 1:
                        # 单关键词匹配
                        keyword = keywords[0]
                        if keyword in search_terms:
                            color_index = search_terms.index(keyword) % len(self.colors)
                            item.setBackground(self.colors[color_index])
                    else:
                        # 多关键词匹配 - 使用混合色或优先级颜色
                        primary_keyword = keywords[0]  # 使用第一个关键词的颜色
                        if primary_keyword in search_terms:
                            color_index = search_terms.index(primary_keyword) % len(self.colors)
                            color = self.colors[color_index]
                            # 为多关键词匹配添加特殊标识（稍微调暗颜色）
                            darker_color = QColor(int(color.red() * 0.8), int(color.green() * 0.8), int(color.blue() * 0.8))
                            item.setBackground(darker_color)

            self.result_list.addItem(item)

        # 自动保存搜索结果
        if self.auto_save_check.isChecked() and matched_files:
            self.save_search_results(matched_files, unmatched_files, unmatched_terms)

        # 保存未匹配文件（如果有）
        message = f"搜索完成，共找到 {len(matched_files)} 个匹配文件"

        if unmatched_files and not self.auto_save_check.isChecked():
            try:
                output_dir = "unmatched_files"
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = os.path.join(output_dir, f"unmatched_files_{timestamp}.txt")

                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write("\n".join(unmatched_files))

                message += f"\n未匹配的 {len(unmatched_files)} 个文件已保存到: {output_file}"
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存未匹配文件失败: {str(e)}")

        if unmatched_terms:
            message += f"\n以下搜索词未匹配到任何文件: {', '.join(unmatched_terms)}"

        self.statusBar().showMessage("搜索完成")
        QMessageBox.information(self, "搜索结果", message)

    def save_search_results(self, matched_files, unmatched_files, unmatched_terms):
        """自动保存搜索结果"""
        try:
            output_dir = "search_results"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存匹配的文件
            matched_file = os.path.join(output_dir, f"matched_files_{timestamp}.txt")
            with open(matched_file, 'w', encoding='utf-8') as f:
                f.write("搜索结果 - 匹配的文件\n")
                f.write("=" * 50 + "\n")
                f.write(f"搜索时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"搜索路径: {self.path_edit.text()}\n")
                f.write(f"搜索关键词: {', '.join([term.strip() for term in self.input_text.toPlainText().splitlines() if term.strip()])}\n")
                f.write(f"匹配文件数量: {len(matched_files)}\n")
                f.write("-" * 50 + "\n\n")

                for file_item in matched_files:
                    # 获取实际文件路径
                    file_path = self.file_paths.get(file_item, "")
                    f.write(f"{file_item}\n")
                    if file_path:
                        f.write(f"完整路径: {file_path}\n")
                    f.write("\n")

            # 保存未匹配的文件（如果有）
            if unmatched_files:
                unmatched_file = os.path.join(output_dir, f"unmatched_files_{timestamp}.txt")
                with open(unmatched_file, 'w', encoding='utf-8') as f:
                    f.write("搜索结果 - 未匹配的文件\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"未匹配文件数量: {len(unmatched_files)}\n")
                    f.write("-" * 50 + "\n\n")
                    for file_path in unmatched_files:
                        f.write(f"{file_path}\n")

            # 保存搜索摘要
            summary_file = os.path.join(output_dir, f"search_summary_{timestamp}.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("搜索摘要\n")
                f.write("=" * 30 + "\n")
                f.write(f"搜索时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"搜索路径: {self.path_edit.text()}\n")
                f.write(f"匹配文件数: {len(matched_files)}\n")
                f.write(f"未匹配文件数: {len(unmatched_files)}\n")
                if unmatched_terms:
                    f.write(f"未匹配关键词: {', '.join(unmatched_terms)}\n")
                f.write(f"\n结果文件:\n")
                f.write(f"- 匹配文件: {matched_file}\n")
                if unmatched_files:
                    f.write(f"- 未匹配文件: {unmatched_file}\n")

            self.statusBar().showMessage(f"搜索结果已自动保存到 {output_dir} 目录", 3000)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"自动保存搜索结果失败: {str(e)}")

    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.result_list.itemAt(position) is None:
            return

        menu = QMenu()

        open_action = menu.addAction("打开文件")
        open_action.triggered.connect(self.open_file)

        open_folder_action = menu.addAction("打开文件夹")
        open_folder_action.triggered.connect(self.open_folder)

        copy_path_action = menu.addAction("复制路径")
        copy_path_action.triggered.connect(self.copy_path_to_clipboard)

        menu.addSeparator()

        delete_action = menu.addAction("删除文件")
        delete_action.triggered.connect(self.delete_files)

        menu.exec_(self.result_list.mapToGlobal(position))

    def open_file(self):
        """打开选中的文件"""
        current_item = self.result_list.currentItem()
        if current_item:
            file_path = self.file_paths.get(current_item.text())
            if file_path and os.path.exists(file_path):
                os.startfile(file_path)
            else:
                QMessageBox.warning(self, "警告", "文件不存在或路径无效")

    def open_folder(self):
        """打开文件所在文件夹"""
        current_item = self.result_list.currentItem()
        if current_item:
            file_path = self.file_paths.get(current_item.text())
            if file_path and os.path.exists(file_path):
                folder_path = os.path.dirname(file_path)
                os.startfile(folder_path)
            else:
                QMessageBox.warning(self, "警告", "文件不存在或路径无效")

    def copy_path_to_clipboard(self):
        """复制文件路径到剪贴板"""
        current_item = self.result_list.currentItem()
        if current_item:
            file_path = self.file_paths.get(current_item.text())
            if file_path:
                try:
                    win32clipboard.OpenClipboard()
                    win32clipboard.EmptyClipboard()
                    win32clipboard.SetClipboardText(file_path)
                    win32clipboard.CloseClipboard()
                    self.statusBar().showMessage("路径已复制到剪贴板", 2000)
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"复制路径失败: {str(e)}")

    def delete_files(self):
        """删除选中的文件"""
        selected_items = self.result_list.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return

        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除选中的 {len(selected_items)} 个文件吗？\n文件将被移动到回收站。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            deleted_count = 0
            for item in selected_items:
                file_path = self.file_paths.get(item.text())
                if file_path and os.path.exists(file_path):
                    try:
                        send2trash(file_path)
                        deleted_count += 1
                        # 从列表中移除
                        row = self.result_list.row(item)
                        self.result_list.takeItem(row)
                        del self.file_paths[item.text()]
                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"删除文件 {file_path} 失败: {str(e)}")

            if deleted_count > 0:
                QMessageBox.information(self, "删除完成", f"成功删除 {deleted_count} 个文件")

    def copy_to_path(self):
        """复制文件到指定路径"""
        target_path = self.copy_path_edit.text()
        if not target_path:
            QMessageBox.critical(self, "错误", "请先选择复制目标路径")
            return

        if not os.path.exists(target_path):
            QMessageBox.critical(self, "错误", "目标路径不存在")
            return

        selected_items = self.result_list.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要复制的文件")
            return

        copied_count = 0
        for item in selected_items:
            file_path = self.file_paths.get(item.text())
            if file_path and os.path.exists(file_path):
                try:
                    filename = os.path.basename(file_path)
                    target_file = os.path.join(target_path, filename)

                    # 如果目标文件已存在，询问是否覆盖
                    if os.path.exists(target_file):
                        reply = QMessageBox.question(self, "文件已存在",
                                                   f"文件 {filename} 已存在，是否覆盖？",
                                                   QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
                        if reply == QMessageBox.Cancel:
                            break
                        elif reply == QMessageBox.No:
                            continue

                    shutil.copy2(file_path, target_file)
                    copied_count += 1
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"复制文件 {file_path} 失败: {str(e)}")

        if copied_count > 0:
            QMessageBox.information(self, "复制完成", f"成功复制 {copied_count} 个文件")

    def open_config_file(self):
        """打开配置文件"""
        if os.path.exists(self.config_file):
            os.startfile(self.config_file)
        else:
            QMessageBox.information(self, "提示", "配置文件不存在，将在首次保存设置时创建")

    def load_config(self):
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding='utf-8')

                # 加载搜索路径
                if self.config.has_option('Settings', 'search_path'):
                    self.path_edit.setText(self.config.get('Settings', 'search_path'))

                # 加载复制路径
                if self.config.has_option('Settings', 'copy_path'):
                    self.copy_path_edit.setText(self.config.get('Settings', 'copy_path'))

                # 加载搜索选项
                if self.config.has_option('Settings', 'fuzzy_search'):
                    self.fuzzy_check.setChecked(self.config.getboolean('Settings', 'fuzzy_search'))

                if self.config.has_option('Settings', 'case_sensitive'):
                    self.case_sensitive_check.setChecked(self.config.getboolean('Settings', 'case_sensitive'))

                if self.config.has_option('Settings', 'color_match'):
                    self.color_match_check.setChecked(self.config.getboolean('Settings', 'color_match'))
                    self.toggle_color_legend()  # 更新图例显示状态

                # 加载扩展名
                if self.config.has_section('Extensions'):
                    for ext in self.config.options('Extensions'):
                        if ext not in self.ext_checkboxes:
                            self.add_extension_checkbox(ext, False)
                        self.ext_checkboxes[ext].setChecked(self.config.getboolean('Extensions', ext))

            except Exception as e:
                QMessageBox.warning(self, "警告", f"加载配置文件失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        try:
            if not self.config.has_section('Settings'):
                self.config.add_section('Settings')

            self.config.set('Settings', 'search_path', self.path_edit.text())
            self.config.set('Settings', 'copy_path', self.copy_path_edit.text())
            self.config.set('Settings', 'fuzzy_search', str(self.fuzzy_check.isChecked()))
            self.config.set('Settings', 'case_sensitive', str(self.case_sensitive_check.isChecked()))
            self.config.set('Settings', 'color_match', str(self.color_match_check.isChecked()))

            if not self.config.has_section('Extensions'):
                self.config.add_section('Extensions')

            for ext, checkbox in self.ext_checkboxes.items():
                self.config.set('Extensions', ext, str(checkbox.isChecked()))

            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置文件失败: {str(e)}")

    def closeEvent(self, event):
        """程序关闭时保存配置"""
        self.save_config()
        event.accept()


def main():
    app = QApplication(sys.argv)

    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = BatchSearchApp()
    window.show()

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
