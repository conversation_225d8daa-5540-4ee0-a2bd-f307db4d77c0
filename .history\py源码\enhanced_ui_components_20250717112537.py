"""
增强的UI组件模块
提供改进的用户界面和交互体验
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QProgressBar, QTextEdit, QListWidget, QListWidgetItem,
                             QComboBox, QPushButton, QGroupBox, QFormLayout,
                             QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QFrame, QScrollArea,
                             QCheckBox, QSpinBox, QSlider, QLineEdit, QMenu,
                             QAbstractItemView, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QIcon
import shutil
import win32clipboard
from send2trash import send2trash


class SearchHistoryManager:
    """搜索历史管理器"""
    
    def __init__(self, history_file: str = "search_history.json"):
        self.history_file = history_file
        self.max_history = 100
        self.history = self.load_history()
    
    def load_history(self) -> List[Dict]:
        """加载搜索历史"""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return []
        return []
    
    def save_history(self):
        """保存搜索历史"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history[-self.max_history:], f, 
                         ensure_ascii=False, indent=2)
        except IOError:
            pass
    
    def add_search(self, query: str, path: str, results_count: int,
                   search_time: float, extensions: List[str]):
        """添加搜索记录"""
        if not query.strip() or not path.strip():
            return

        record = {
            'query': query.strip(),
            'path': path.strip(),
            'results_count': int(results_count),
            'search_time': float(search_time),
            'extensions': list(extensions) if extensions else [],
            'timestamp': datetime.now().isoformat()
        }

        # 避免重复记录 - 检查最近的几条记录
        for i in range(len(self.history) - 1, max(-1, len(self.history) - 5), -1):
            existing = self.history[i]
            if (existing.get('query', '') == record['query'] and
                existing.get('path', '') == record['path'] and
                existing.get('extensions', []) == record['extensions']):
                # 更新现有记录的时间戳和结果数
                self.history[i] = record
                self.save_history()
                return

        self.history.append(record)

        # 限制历史记录数量
        if len(self.history) > self.max_history:
            self.history = self.history[-self.max_history:]

        self.save_history()
    
    def get_recent_queries(self, limit: int = 10) -> List[str]:
        """获取最近的查询"""
        recent = []
        seen = set()
        
        for record in reversed(self.history):
            query = record['query']
            if query not in seen:
                recent.append(query)
                seen.add(query)
                if len(recent) >= limit:
                    break
        
        return recent
    
    def get_recent_paths(self, limit: int = 10) -> List[str]:
        """获取最近的搜索路径"""
        recent = []
        seen = set()
        
        for record in reversed(self.history):
            path = record['path']
            if path not in seen:
                recent.append(path)
                seen.add(path)
                if len(recent) >= limit:
                    break
        
        return recent
    
    def get_statistics(self) -> Dict:
        """获取搜索统计信息"""
        if not self.history:
            return {}
        
        total_searches = len(self.history)
        total_results = sum(record['results_count'] for record in self.history)
        avg_results = total_results / total_searches if total_searches > 0 else 0
        
        # 最常用的查询
        query_counts = {}
        for record in self.history:
            query = record['query']
            query_counts[query] = query_counts.get(query, 0) + 1
        
        most_common_query = max(query_counts.items(), key=lambda x: x[1]) if query_counts else ("", 0)
        
        return {
            'total_searches': total_searches,
            'total_results': total_results,
            'average_results': avg_results,
            'most_common_query': most_common_query[0],
            'most_common_count': most_common_query[1]
        }


class EnhancedProgressWidget(QWidget):
    """增强的进度显示组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.start_time = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_elapsed_time)
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 主进度条
        self.main_progress = QProgressBar()
        self.main_progress.setVisible(False)
        layout.addWidget(self.main_progress)
        
        # 状态信息
        info_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        info_layout.addWidget(self.status_label)
        
        info_layout.addStretch()
        
        self.time_label = QLabel("")
        self.time_label.setStyleSheet("color: #666; font-size: 12px;")
        info_layout.addWidget(self.time_label)
        
        layout.addLayout(info_layout)
        
        # 详细信息
        self.detail_label = QLabel("")
        self.detail_label.setStyleSheet("color: #888; font-size: 11px;")
        self.detail_label.setWordWrap(True)
        layout.addWidget(self.detail_label)
        
        self.setLayout(layout)
    
    def start_progress(self, message: str = "开始搜索..."):
        """开始进度显示"""
        self.start_time = datetime.now()
        self.main_progress.setVisible(True)
        self.main_progress.setRange(0, 0)  # 不确定进度
        self.status_label.setText(message)
        self.timer.start(100)  # 每100ms更新一次
    
    def update_progress(self, message: str, current: int = 0, total: int = 0):
        """更新进度"""
        self.status_label.setText(message)
        
        if total > 0:
            self.main_progress.setRange(0, total)
            self.main_progress.setValue(current)
            percentage = (current / total) * 100
            self.detail_label.setText(f"进度: {current}/{total} ({percentage:.1f}%)")
        else:
            self.detail_label.setText(message)
    
    def finish_progress(self, message: str = "搜索完成"):
        """完成进度显示"""
        self.timer.stop()
        self.main_progress.setVisible(False)
        self.status_label.setText(message)
        
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            self.time_label.setText(f"耗时: {elapsed.total_seconds():.2f}秒")
    
    def update_elapsed_time(self):
        """更新已用时间"""
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            self.time_label.setText(f"已用时间: {elapsed.total_seconds():.1f}秒")


class SearchHistoryWidget(QWidget):
    """搜索历史组件"""
    
    history_selected = pyqtSignal(str, str, list)  # query, path, extensions
    
    def __init__(self, history_manager: SearchHistoryManager):
        super().__init__()
        self.history_manager = history_manager
        self.init_ui()
        self.refresh_history()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("搜索历史")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 历史列表
        self.history_list = QListWidget()
        self.history_list.itemDoubleClicked.connect(self.on_history_selected)
        layout.addWidget(self.history_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("清空历史")
        self.clear_btn.clicked.connect(self.clear_history)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_history)
        button_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def refresh_history(self):
        """刷新历史列表"""
        self.history_list.clear()

        if not self.history_manager.history:
            # 如果没有历史记录，显示提示
            item = QListWidgetItem("暂无搜索历史")
            item.setData(Qt.UserRole, None)
            self.history_list.addItem(item)
            return

        # 显示最近20条记录，按时间倒序
        recent_history = self.history_manager.history[-20:]
        for record in reversed(recent_history):
            try:
                timestamp = datetime.fromisoformat(record['timestamp'])
                time_str = timestamp.strftime("%m-%d %H:%M")

                # 处理查询文本显示
                query_lines = record['query'].split('\n')
                display_query = query_lines[0][:25]  # 只显示第一行的前25个字符
                if len(query_lines) > 1:
                    display_query += f"... (+{len(query_lines)-1}行)"
                elif len(record['query']) > 25:
                    display_query += "..."

                # 格式化显示文本
                item_text = f"[{time_str}] {display_query}"
                item_text += f" ({record.get('results_count', 0)}个结果)"

                # 添加路径信息（简化显示）
                path = record.get('path', '')
                if path:
                    path_name = os.path.basename(path) or path
                    if len(path_name) > 15:
                        path_name = path_name[:12] + "..."
                    item_text += f" - {path_name}"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, record)

                # 设置工具提示显示完整信息
                tooltip = f"查询: {record['query']}\n路径: {record.get('path', '')}\n"
                tooltip += f"结果: {record.get('results_count', 0)}个\n"
                tooltip += f"耗时: {record.get('search_time', 0):.2f}秒\n"
                tooltip += f"时间: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
                item.setToolTip(tooltip)

                self.history_list.addItem(item)

            except Exception as e:
                # 如果某条记录有问题，跳过它
                continue
    
    def on_history_selected(self, item):
        """历史项被选中"""
        record = item.data(Qt.UserRole)
        if record and isinstance(record, dict):
            # 确保数据完整性
            query = record.get('query', '')
            path = record.get('path', '')
            extensions = record.get('extensions', [])

            if query and path:
                self.history_selected.emit(query, path, extensions)
    
    def clear_history(self):
        """清空历史"""
        self.history_manager.history.clear()
        self.history_manager.save_history()
        self.refresh_history()


class SearchStatisticsWidget(QWidget):
    """搜索统计组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.reset_statistics()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("搜索统计")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 统计表格
        self.stats_table = QTableWidget(0, 2)
        self.stats_table.setHorizontalHeaderLabels(["项目", "值"])
        self.stats_table.horizontalHeader().setStretchLastSection(True)
        self.stats_table.verticalHeader().setVisible(False)
        self.stats_table.setAlternatingRowColors(True)
        layout.addWidget(self.stats_table)
        
        self.setLayout(layout)
    
    def update_statistics(self, stats: Dict):
        """更新统计信息"""
        self.stats_table.setRowCount(0)

        if not stats:
            # 如果没有统计数据，显示提示
            self.stats_table.insertRow(0)
            self.stats_table.setItem(0, 0, QTableWidgetItem("暂无统计数据"))
            self.stats_table.setItem(0, 1, QTableWidgetItem(""))
            return

        # 按特定顺序显示统计信息
        display_order = [
            'total_matches', 'unique_files', 'total_size', 'average_size',
            'search_time', 'pattern_stats'
        ]

        # 先显示有序的统计项
        for key in display_order:
            if key in stats:
                self._add_stat_row(key, stats[key])

        # 再显示其他统计项
        for key, value in stats.items():
            if key not in display_order:
                self._add_stat_row(key, value)

        # 调整列宽
        self.stats_table.resizeColumnsToContents()

    def _add_stat_row(self, key: str, value):
        """添加统计行"""
        row = self.stats_table.rowCount()
        self.stats_table.insertRow(row)

        # 格式化显示名称
        display_name = self._format_stat_name(key)
        self.stats_table.setItem(row, 0, QTableWidgetItem(display_name))

        # 格式化值
        display_value = self._format_stat_value(key, value)
        self.stats_table.setItem(row, 1, QTableWidgetItem(display_value))
    
    def _format_stat_name(self, key: str) -> str:
        """格式化统计项名称"""
        name_map = {
            'total_matches': '匹配文件数',
            'total_size': '文件总大小',
            'average_size': '平均文件大小',
            'unique_files': '唯一文件数',
            'search_time': '搜索耗时',
            'pattern_stats': '模式统计'
        }
        return name_map.get(key, key)
    
    def _format_stat_value(self, key: str, value) -> str:
        """格式化统计值"""
        try:
            if key in ['total_size', 'average_size']:
                return self._format_file_size(int(value) if value else 0)
            elif key == 'search_time':
                return f"{float(value):.2f}秒"
            elif key == 'pattern_stats' and isinstance(value, dict):
                # 显示模式统计的详细信息
                if not value:
                    return "无匹配模式"
                total = sum(value.values())
                return f"{len(value)}个模式, 共{total}个匹配"
            elif isinstance(value, dict):
                return f"{len(value)}项"
            elif isinstance(value, (int, float)):
                if key in ['total_matches', 'unique_files']:
                    return f"{int(value)}个"
                else:
                    return str(value)
            else:
                return str(value) if value is not None else "0"
        except Exception:
            return str(value) if value is not None else "0"
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats_table.setRowCount(0)


class EnhancedResultWidget(QWidget):
    """增强的结果显示组件"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.results_data = []
        self.file_paths = {}  # 存储显示文本到文件路径的映射
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 结果工具栏
        toolbar_layout = QHBoxLayout()
        
        self.view_mode_combo = QComboBox()
        self.view_mode_combo.addItems(["列表视图", "详细视图", "图标视图"])
        self.view_mode_combo.currentTextChanged.connect(self.change_view_mode)
        toolbar_layout.addWidget(QLabel("视图:"))
        toolbar_layout.addWidget(self.view_mode_combo)
        
        toolbar_layout.addStretch()
        
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["按名称", "按大小", "按修改时间", "按路径"])
        toolbar_layout.addWidget(QLabel("排序:"))
        toolbar_layout.addWidget(self.sort_combo)
        
        layout.addLayout(toolbar_layout)
        
        # 结果显示区域
        self.result_stack = QTabWidget()
        
        # 列表视图
        self.list_widget = QListWidget()
        self.list_widget.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.list_widget.customContextMenuRequested.connect(self.show_context_menu)
        self.list_widget.itemDoubleClicked.connect(self.open_file)
        self.result_stack.addTab(self.list_widget, "列表")

        # 详细视图
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(4)
        self.table_widget.setHorizontalHeaderLabels(["文件名", "大小", "修改时间", "路径"])
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.show_table_context_menu)
        self.table_widget.itemDoubleClicked.connect(self.open_file_from_table)
        self.result_stack.addTab(self.table_widget, "详细")
        
        layout.addWidget(self.result_stack)
        
        self.setLayout(layout)
    
    def change_view_mode(self, mode: str):
        """切换视图模式"""
        if mode == "列表视图":
            self.result_stack.setCurrentIndex(0)
        elif mode == "详细视图":
            self.result_stack.setCurrentIndex(1)
    
    def update_results(self, results: List[Dict]):
        """更新结果显示"""
        self.results_data = results
        self._update_list_view()
        self._update_table_view()
    
    def _update_list_view(self):
        """更新列表视图"""
        self.list_widget.clear()
        
        for result in self.results_data:
            item_text = f"{result['filename']} ({result['relative_path']})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, result)
            self.list_widget.addItem(item)
    
    def _update_table_view(self):
        """更新表格视图"""
        self.table_widget.setRowCount(len(self.results_data))
        
        for row, result in enumerate(self.results_data):
            # 文件名
            self.table_widget.setItem(row, 0, QTableWidgetItem(result['filename']))
            
            # 文件大小
            size_str = self._format_file_size(result.get('file_size', 0))
            self.table_widget.setItem(row, 1, QTableWidgetItem(size_str))
            
            # 修改时间
            if result.get('modified_time'):
                time_str = datetime.fromtimestamp(result['modified_time']).strftime("%Y-%m-%d %H:%M")
                self.table_widget.setItem(row, 2, QTableWidgetItem(time_str))
            else:
                self.table_widget.setItem(row, 2, QTableWidgetItem(""))
            
            # 路径
            self.table_widget.setItem(row, 3, QTableWidgetItem(result['relative_path']))
        
        # 调整列宽
        self.table_widget.resizeColumnsToContents()
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
