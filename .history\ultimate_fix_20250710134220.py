#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ultimate PyInstaller Fix Tool
Solves pkg_resources warnings and all packaging issues
"""

import os
import sys
import subprocess
import shutil
import warnings

# Suppress pkg_resources warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="pkg_resources")

def create_clean_spec(source_file, output_name, icon_path=None):
    """Create clean spec file without pkg_resources issues"""
    
    source_file = os.path.abspath(source_file)
    source_dir = os.path.dirname(source_file)
    
    # Build data files list
    datas = []
    if icon_path and os.path.exists(icon_path):
        datas.append(f"('{icon_path}', '.')")
    
    # Check for common data files
    data_files = ['使用说明.md', 'README.md', 'readme.txt']
    for file_name in data_files:
        file_path = os.path.join(source_dir, file_name)
        if os.path.exists(file_path):
            datas.append(f"('{file_path}', '.')")
    
    datas_str = ',\\n        '.join(datas) if datas else ''
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# Clean PyInstaller spec file - No pkg_resources warnings

import sys
import os
import warnings

# Suppress all pkg_resources warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*pkg_resources.*")

block_cipher = None

a = Analysis(
    [r'{source_file}'],
    pathex=[r'{source_dir}'],
    binaries=[],
    datas=[
        {datas_str}
    ],
    hiddenimports=[
        # PyQt5 core modules
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.sip',
        
        # Standard library modules
        'json', 'subprocess', 'pathlib', 'datetime', 'shutil',
        'tempfile', 'threading', 'queue', 'configparser',
        'os', 'sys', 'time', 'logging', 'glob', 'fnmatch',
        
        # Modern package management (no pkg_resources)
        'importlib', 'importlib.util', 'importlib.metadata',
        'setuptools', 'distutils.util',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        # Exclude problematic modules
        'tkinter', 'matplotlib', 'numpy', 'scipy', 'pandas',
        'IPython', 'jupyter', 'notebook', 'pytest', 'test', 'tests',
        '_pytest', 'PyQt6', 'PySide2', 'PySide6',
        # Explicitly exclude pkg_resources
        'pkg_resources', 'pkg_resources.py2_warn', 'pkg_resources._vendor',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='{output_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # Disable UPX for better compatibility
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon={repr(icon_path) if icon_path and os.path.exists(icon_path) else None},
)
'''
    
    return spec_content

def clean_package(source_file, output_name=None, icon_path=None):
    """Clean packaging without warnings"""
    
    if not os.path.exists(source_file):
        print(f"❌ Error: Source file {source_file} does not exist")
        return False
    
    if not output_name:
        output_name = os.path.splitext(os.path.basename(source_file))[0]
    
    print(f"🚀 Ultimate Clean Packaging Tool")
    print(f"Source: {source_file}")
    print(f"Output: {output_name}")
    if icon_path:
        print(f"Icon: {icon_path}")
    
    try:
        # Set environment variables to suppress warnings
        env = os.environ.copy()
        env['PYTHONWARNINGS'] = 'ignore::DeprecationWarning'
        env['PYINSTALLER_SUPPRESS_WARNINGS'] = '1'
        
        # Create spec file
        spec_content = create_clean_spec(source_file, output_name, icon_path)
        spec_filename = f"{output_name}_clean.spec"
        
        with open(spec_filename, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"✅ Created clean spec file: {spec_filename}")
        
        # Clean build directories
        for dir_name in ["build", "__pycache__"]:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"🧹 Cleaned {dir_name} directory")
        
        # Execute packaging with minimal output
        print("🚀 Starting clean packaging...")
        command = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            '--log-level=WARN',  # Reduce verbosity
            spec_filename  # Only spec file, no other conflicting options
        ]
        
        print(f"Command: {' '.join(command)}")
        
        # Run with suppressed warnings
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env,
            cwd=os.path.dirname(source_file) or '.'
        )
        
        if result.returncode == 0:
            print("✅ Packaging successful! (pkg_resources warnings filtered)")
            
            # Check output file
            dist_dir = "dist"
            exe_file = os.path.join(dist_dir, f"{output_name}.exe")
            if os.path.exists(exe_file):
                file_size = os.path.getsize(exe_file) / (1024 * 1024)
                print(f"📦 Output file: {exe_file}")
                print(f"📏 File size: {file_size:.1f} MB")
            
            return True
        else:
            print("❌ Packaging failed:")
            # Only show real errors, filter warnings
            if result.stderr:
                error_lines = [line for line in result.stderr.split('\\n') 
                             if 'ERROR' in line and 'pkg_resources' not in line]
                if error_lines:
                    print("Error messages:")
                    for line in error_lines[-3:]:
                        print(line)
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False
    
    finally:
        # Clean temporary files
        if 'spec_filename' in locals() and os.path.exists(spec_filename):
            try:
                os.remove(spec_filename)
                print(f"🧹 Cleaned temporary file: {spec_filename}")
            except:
                pass

def main():
    print("=" * 60)
    print("🛠️  Ultimate Clean PyInstaller Tool")
    print("    Solves pkg_resources warnings and all packaging issues")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        source_file = sys.argv[1]
        output_name = sys.argv[2] if len(sys.argv) > 2 else None
        icon_path = sys.argv[3] if len(sys.argv) > 3 else None
    else:
        source_file = input("Enter Python file path: ").strip().strip('"')
        output_name = input("Enter output name (press Enter for default): ").strip() or None
        icon_path = input("Enter icon file path (optional): ").strip().strip('"') or None
    
    success = clean_package(source_file, output_name, icon_path)
    
    if success:
        print("\\n🎉 Ultimate packaging complete! Check the dist directory.")
        print("📝 All pkg_resources warnings have been filtered for clean output.")
    else:
        print("\\n💥 Packaging failed, please check error messages.")
    
    input("\\nPress Enter to exit...")

if __name__ == '__main__':
    main()
