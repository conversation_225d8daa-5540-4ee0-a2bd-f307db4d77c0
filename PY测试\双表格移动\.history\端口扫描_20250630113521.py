import sys
import socket
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit, QPushButton, QTextEdit,
    QProgressBar, QRadioButton, QButtonGroup, QSpinBox, QHBoxLayout, QVBoxLayout, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
import datetime

class PortScannerApp(QWidget):
    append_text_signal = pyqtSignal(str)
    set_progress_signal = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("端口扫描器")
        self.setFixedSize(520, 420)
        self.stop_scan = False
        self.pause_scan = False
        self.current_port = None
        self.estimated_time_shown = False  # 新增标志
        self.init_ui()
        self.append_text_signal.connect(self.text_result.append)
        self.set_progress_signal.connect(self.progress.setValue)

    def init_ui(self):
        # 目标地址
        lbl_host = QLabel("目标地址 (IP或域名):")
        self.edit_host = QLineEdit("*************")

        # 端口范围
        lbl_ports = QLabel("端口范围:")
        self.edit_ports = QLineEdit("8080-8200")

        # 协议类型
        lbl_proto = QLabel("协议类型:")
        self.radio_tcp = QRadioButton("TCP")
        self.radio_udp = QRadioButton("UDP")
        self.radio_tcp.setChecked(True)
        self.proto_group = QButtonGroup()
        self.proto_group.addButton(self.radio_tcp)
        self.proto_group.addButton(self.radio_udp)

        # 并发数
        lbl_workers = QLabel("并发数:")
        self.spin_workers = QSpinBox()
        self.spin_workers.setRange(1, 1000)
        self.spin_workers.setValue(4)

        # 端口间隔
        lbl_delay = QLabel("端口间隔(ms):")
        self.spin_delay = QSpinBox()
        self.spin_delay.setRange(0, 10000)
        self.spin_delay.setValue(200)

        # 按钮
        self.btn_scan = QPushButton("开始扫描")
        self.btn_scan.setStyleSheet("background-color:#3399ff; color:white;")
        self.btn_pause = QPushButton("暂停扫描")  # 新增暂停按钮
        self.btn_pause.setStyleSheet("background-color:#ffcc00; color:black;")
        self.btn_stop = QPushButton("停止扫描")
        self.btn_stop.setStyleSheet("background-color:#ff6666; color:white;")

        # 进度条
        lbl_progress = QLabel("扫描进度:")
        self.progress = QProgressBar()

        # 结果输出
        self.text_result = QTextEdit()
        self.text_result.setReadOnly(True)

        # 布局
        layout = QVBoxLayout()
        row1 = QHBoxLayout()
        row1.addWidget(lbl_host)
        row1.addWidget(self.edit_host)
        layout.addLayout(row1)

        row2 = QHBoxLayout()
        row2.addWidget(lbl_ports)
        row2.addWidget(self.edit_ports)
        layout.addLayout(row2)

        row3 = QHBoxLayout()
        row3.addWidget(lbl_proto)
        row3.addWidget(self.radio_tcp)
        row3.addWidget(self.radio_udp)
        row3.addStretch()
        layout.addLayout(row3)

        row4 = QHBoxLayout()
        row4.addWidget(lbl_workers)
        row4.addWidget(self.spin_workers)
        row4.addSpacing(1)  # 增加间距，避免遮挡
        row4.addWidget(lbl_delay)
        row4.addSpacing(1)   # 标签和输入框之间加点间距
        row4.addWidget(self.spin_delay)
        row4.addSpacing(5)  # 增加间距，避免按钮挤压
        row4.addWidget(self.btn_scan)
        row4.addWidget(self.btn_pause)
        row4.addWidget(self.btn_stop)
        layout.addLayout(row4)

        row5 = QHBoxLayout()
        row5.addWidget(lbl_progress)
        row5.addWidget(self.progress)
        layout.addLayout(row5)

        layout.addWidget(self.text_result)
        self.setLayout(layout)

        # 事件绑定
        self.btn_scan.clicked.connect(self.start_scan)
        self.btn_pause.clicked.connect(self.pause_scanning)  # 绑定暂停
        self.btn_stop.clicked.connect(self.stop_scanning)

    def start_scan(self):
        self.text_result.clear()
        self.stop_scan = False
        self.estimated_time_shown = False  # 每次开始扫描时重置
        self.start_time = time.time()  # 记录开始时间
        host = self.edit_host.text().strip()
        port_range = self.edit_ports.text().strip()
        protocol = "TCP" if self.radio_tcp.isChecked() else "UDP"
        try:
            ip = socket.gethostbyname(host)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法解析主机: {e}")
            return

        try:
            start_port, end_port = map(int, port_range.split('-'))
            if start_port < 1 or end_port > 65535 or start_port > end_port:
                raise ValueError
        except:
            QMessageBox.critical(self, "错误", "端口范围格式错误，应为先小后大 1-1024 这种格式")
            return

        workers = self.spin_workers.value()
        delay = self.spin_delay.value()

        self.progress.setValue(0)
        self.progress.setMaximum(end_port - start_port + 1)
        self.append_text_signal.emit(f"目标地址: {ip}")
        threading.Thread(target=self.scan_ports, args=(ip, start_port, end_port, protocol, workers, delay), daemon=True).start()

    def pause_scanning(self):
        self.pause_scan = not self.pause_scan
        if self.pause_scan:
            self.btn_pause.setText("继续扫描")
            self.append_text_signal.emit(f"扫描已暂停，当前端口：{self.current_port}")
        else:
            self.btn_pause.setText("暂停扫描")
            self.append_text_signal.emit("扫描已继续。")
            self.estimated_time_shown = False  # 恢复扫描时重置，允许再次显示

    def stop_scanning(self):
        self.stop_scan = True
        self.pause_scan = False
        self.btn_pause.setText("暂停扫描")
        self.append_text_signal.emit(f"扫描已停止，当前端口：{self.current_port}")

    def scan_ports(self, ip, start_port, end_port, protocol, workers, delay):
        open_ports = []
        total_ports = end_port - start_port + 1

        def scan_one(port):
            while self.pause_scan:
                time.sleep(0.1)
            if self.stop_scan:
                return None
            time.sleep(delay / 1000.0)
            if protocol == "TCP":
                if self.check_tcp_port(ip, port):
                    return port
            else:
                if self.check_udp_port(ip, port):
                    return port
            return None

        with ThreadPoolExecutor(max_workers=workers) as executor:
            future_to_port = {executor.submit(scan_one, port): port for port in range(start_port, end_port + 1)}
            for idx, future in enumerate(as_completed(future_to_port), 1):
                while self.pause_scan:
                    time.sleep(0.1)
                port = future_to_port[future]
                self.current_port = port  # 记录当前端口
                result = future.result()
                if self.stop_scan:
                    self.append_text_signal.emit(f"扫描已停止，当前端口：{self.current_port}")
                    break
                if result:
                    open_ports.append(result)
                    self.append_text_signal.emit(f"端口 {result} ({protocol}) 开放")
                self.set_progress_signal.emit(idx)

                # 只显示一次预计完成时间
                if not self.estimated_time_shown and idx > 1:
                    elapsed = time.time() - self.start_time
                    # 按并发数修正平均时间
                    avg_time_per_batch = elapsed / ((idx - 1) / workers + 1e-6)
                    remain_ports = total_ports - idx
                    # 剩余批次数 = 剩余端口 / 并发数
                    remain_batches = remain_ports / workers
                    remain_time = avg_time_per_batch * remain_batches
                    finish_time = datetime.datetime.now() + datetime.timedelta(seconds=remain_time)
                    finish_time_str = finish_time.strftime('%H:%M:%S')
                    self.append_text_signal.emit(f"预计完成时间: {finish_time_str}")
                    self.estimated_time_shown = True

        self.append_text_signal.emit("\n扫描完成！")
        if open_ports:
            for port in open_ports:
                protocol = "TCP" if self.radio_tcp.isChecked() else "UDP"
                # self.append_text_signal.emit(f"端口 {port} ({protocol}) 开放")
        else:
            self.append_text_signal.emit("无开放端口。")

    def check_tcp_port(self, ip, port):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(0.5)
                result = s.connect_ex((ip, port))
                return result == 0
        except:
            return False

    def check_udp_port(self, ip, port):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.settimeout(0.5)
                s.sendto(b'', (ip, port))
                s.recvfrom(1024)
                return True
        except:
            return False

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = PortScannerApp()
    win.show()
    sys.exit(app.exec_())