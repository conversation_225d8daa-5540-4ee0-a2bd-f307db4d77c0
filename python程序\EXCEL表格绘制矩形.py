import pandas as pd
import ezdxf
import os


def main():
    # 提示用户输入包含Excel文件的路径
    directory_path = input("读取Excel文件内第一个sheet的第一列数据并创建DXF文件的目录路径。\n" +
                           "xlsx文件应包含一个sheet，且确保表头有 零件编号，宽度，长度三列。\n" +
                           "\n" +
                           "请输入包含Excel文件的目录路径（例如：C:\\path\\to\\directory 或 C:/path/to/directory）：")

    # 规范化路径
    directory_path = os.path.normpath(directory_path)

    # 检查路径是否存在
    if not os.path.exists(directory_path):
        print("指定的目录路径不存在，请检查路径是否正确。")
        return

    # 遍历目录下的所有文件
    for file_name in os.listdir(directory_path):
        if file_name.endswith('.xlsx'):
            # 拼接完整的Excel文件路径
            excel_path = os.path.join(directory_path, file_name)

            try:
                # 读取Excel文件
                df = pd.read_excel(excel_path)
            except Exception as e:
                print(f"读取Excel文件 {excel_path} 时出错: {e}")
                continue

            # 遍历每一行数据
            for index, row in df.iterrows():
                part_number = str(row['零件编号'])  # 假设表头为'零件编号'
                width = row['宽度']  # 假设表头为'宽度'
                length = row['长度']  # 假设表头为'长度'

                # 创建一个新的DXF文档
                doc = ezdxf.new('R2010')
                msp = doc.modelspace()

                # 定义矩形的四个顶点
                points = [(0, 0), (length, 0), (length, width), (0, width), (0, 0)]

                # 使用add_polyline2d方法添加矩形
                msp.add_polyline2d(points, close=True)

                # 保存DXF文件，文件名用零件编号
                save_path = os.path.join(directory_path, f'{part_number}.dxf')
                doc.saveas(save_path)

                print(f'已创建并保存DXF文件: {save_path}')


if __name__ == '__main__':
    main()
