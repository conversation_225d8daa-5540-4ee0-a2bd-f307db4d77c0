#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Python全自动打包工具
支持单文件/文件夹打包，图标设置，版本信息，自动依赖处理
基于PyInstaller 5.13.0构建
"""

import sys
import os
import json
import subprocess
import shutil
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QGridLayout,
    QWidget, QLabel, QLineEdit, QPushButton, QFileDialog, QCheckBox,
    QGroupBox, QTextEdit, QProgressBar, QMessageBox, QComboBox,
    QTabWidget, QFormLayout, QSpinBox, QRadioButton
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPixmap

class PackageThread(QThread):
    """打包线程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, config):
        super().__init__()
        self.config = config
    
    def run(self):
        try:
            self.progress.emit("开始检查环境...")
            
            # 检查PyInstaller
            if not self.check_pyinstaller():
                self.progress.emit("正在安装PyInstaller...")
                if not self.install_pyinstaller():
                    self.finished.emit(False, "PyInstaller安装失败")
                    return
            
            # 构建命令
            command = self.build_command()
            self.progress.emit(f"执行命令: {' '.join(command)}")
            
            # 设置环境变量，解决conda环境问题
            env = os.environ.copy()
            if self.config.get('python_path') and ('conda' in self.config['python_path'].lower() or 'miniconda' in self.config['python_path'].lower()):
                # 为conda环境设置特殊环境变量
                self.progress.emit("为Conda环境设置特殊配置...")
                env['PYTHONHOME'] = os.path.dirname(self.config['python_path'])
                # 禁用UPX压缩，提高兼容性
                if '--upx-dir' not in command and '--noupx' not in command:
                    command.append('--noupx')
            
            # 执行打包
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=os.path.dirname(self.config['source_file']) or '.',
                env=env
            )
            
            if result.returncode == 0:
                self.progress.emit("打包成功！正在整理文件...")
                self.post_process()
                self.finished.emit(True, "打包完成！文件保存在dist目录")
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                self.progress.emit(f"打包命令输出: {result.stdout}")
                self.progress.emit(f"打包命令错误: {result.stderr}")
                self.finished.emit(False, f"打包失败: {error_msg}")
                
        except Exception as e:
            self.finished.emit(False, f"发生错误: {str(e)}")
    
    def check_pyinstaller(self):
        """检查PyInstaller是否安装"""
        try:
            import PyInstaller
            return True
        except ImportError:
            return False
    
    def install_pyinstaller(self):
        """安装PyInstaller"""
        try:
            # 先尝试升级pip
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip"
            ])
            # 安装兼容的PyInstaller版本
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "pyinstaller>=5.0,<7.0"
            ])
            return True
        except:
            return False
    
    def build_command(self):
        """构建PyInstaller命令"""
        # 使用自定义Python解释器路径(如果指定)
        if self.config.get('python_path') and os.path.exists(self.config.get('python_path')):
            python_path = self.config['python_path']
            # 检测是否为conda环境
            is_conda = 'conda' in python_path.lower() or 'miniconda' in python_path.lower()
            
            if is_conda:
                self.progress.emit("检测到Conda环境，使用spec文件方式打包...")
                # 为conda环境创建spec文件
                spec_file = self.create_spec_file(is_conda=True)
                if spec_file:
                    # 使用spec文件打包
                    command = [python_path, '-m', 'PyInstaller', '--clean', spec_file]
                    return command
            
            # 非conda环境或spec文件创建失败，使用常规命令
            command = [python_path, '-m', 'PyInstaller', '--clean']
        else:
            command = ['pyinstaller', '--clean']
        
        # 基本选项
        if self.config['onefile']:
            command.append('--onefile')
        else:
            command.append('--onedir')
        
        if self.config['noconsole']:
            command.extend(['--noconsole', '--windowed'])
        
        # 图标
        if self.config['icon'] and os.path.exists(self.config['icon']):
            command.extend(['--icon', self.config['icon']])
        
        # 输出名称
        if self.config['name']:
            command.extend(['--name', self.config['name']])
        
        # 版本信息文件
        if self.config['version_info']:
            version_file = self.create_version_file()
            if version_file:
                command.extend(['--version-file', version_file])
        
        # 添加数据文件
        for data_path in self.config.get('add_data', []):
            if os.path.exists(data_path):
                command.extend(['--add-data', f"{data_path};."])
        
        # 隐藏导入 - 只添加确实需要的模块
        hidden_imports = [
            'pkg_resources', 'setuptools', 'distutils',
            'pkg_resources.py2_warn', 'pkg_resources._vendor',
            'importlib_metadata'
        ]

        # 检查并添加可选的隐藏导入
        optional_imports = ['pandas', 'numpy', 'openpyxl', 'xlrd', 'requests']
        for imp in optional_imports:
            try:
                __import__(imp)
                hidden_imports.append(imp)
            except ImportError:
                pass

        for imp in hidden_imports:
            command.extend(['--hidden-import', imp])

        # 添加排除项以减少包大小和避免冲突
        excludes = [
            'tkinter', 'matplotlib', 'scipy', 'IPython', 'jupyter',
            'notebook', 'pytest', 'test', 'tests', '_pytest'
        ]
        for exc in excludes:
            command.extend(['--exclude-module', exc])
        
        # 源文件
        command.append(self.config['source_file'])
        
        return command

    def create_spec_file(self, is_conda=False):
        """创建PyInstaller spec文件"""
        try:
            source_file = self.config['source_file']
            output_name = self.config['name'] or os.path.splitext(os.path.basename(source_file))[0]
            
            # 获取源文件目录
            source_dir = os.path.dirname(source_file) or '.'
            
            # 构建spec文件内容
            spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

block_cipher = None

a = Analysis(
    [r'{source_file}'],
    pathex=[r'{source_dir}'],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas', 'numpy', 'matplotlib', 'requests', 'openpyxl',
        'xlrd', 'xlwt', 'pillow', 'opencv-python', 'scipy',
        'pkg_resources', 'setuptools', 'distutils',
        'pkg_resources.py2_warn'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

'''
            
            # 添加数据文件
            data_paths = []
            for data_path in self.config.get('add_data', []):
                if os.path.exists(data_path):
                    data_paths.append(f"(r'{data_path}', '.')")
            
            if data_paths:
                data_str = ", ".join(data_paths)
                spec_content = spec_content.replace('datas=[]', f'datas=[{data_str}]')
            
            # 添加EXE部分
            spec_content += f'''
exe = EXE(
    pyz,
    a.scripts,
    {"a.binaries," if self.config['onefile'] else ""}
    {"a.zipfiles," if self.config['onefile'] else ""}
    {"a.datas," if self.config['onefile'] else ""}
    [],
    name='{output_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩以提高兼容性
    upx_exclude=[],
    runtime_tmpdir=None,
    console={not self.config['noconsole']},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
'''
            
            # 添加图标
            if self.config['icon'] and os.path.exists(self.config['icon']):
                spec_content += f"    icon=r'{self.config['icon']}',\n"
            
            # 添加版本信息
            if self.config['version_info']:
                version_file = self.create_version_file()
                if version_file:
                    spec_content += f"    version=r'{version_file}',\n"
            
            # 结束EXE部分
            spec_content += ')'
            
            # 如果不是单文件模式，添加COLLECT部分
            if not self.config['onefile']:
                spec_content += f'''

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='{output_name}',
)
'''
            
            # 写入spec文件
            spec_file = f"{output_name}_conda.spec" if is_conda else f"{output_name}.spec"
            with open(spec_file, 'w', encoding='utf-8') as f:
                f.write(spec_content)
            
            self.progress.emit(f"已创建spec文件: {spec_file}")
            return spec_file
        except Exception as e:
            self.progress.emit(f"创建spec文件失败: {str(e)}")
            return None
    
    def create_version_file(self):
        """创建版本信息文件"""
        try:
            if not self.config.get('version_info'):
                return None
            
            version_info = self.config['version_info']
            version_content = f'''
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({version_info['file_version'].replace('.', ', ')}),
    prodvers=({version_info['product_version'].replace('.', ', ')}),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [StringStruct(u'CompanyName', u'{version_info['company']}'),
           StringStruct(u'FileDescription', u'{version_info['description']}'),
           StringStruct(u'FileVersion', u'{version_info['file_version']}'),
           StringStruct(u'InternalName', u'{version_info['internal_name']}'),
           StringStruct(u'LegalCopyright', u'{version_info['copyright']}'),
           StringStruct(u'OriginalFilename', u'{version_info['original_filename']}'),
           StringStruct(u'ProductName', u'{version_info['product_name']}'),
           StringStruct(u'ProductVersion', u'{version_info['product_version']}')]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
            version_file = 'version_info.txt'
            with open(version_file, 'w', encoding='utf-8') as f:
                f.write(version_content)
            return version_file
        except:
            return None
    
    def post_process(self):
        """后处理：复制文件，清理等"""
        try:
            # 如果有自动运行选项，可以在这里实现
            if self.config.get('auto_run', False):
                self.progress.emit("准备自动运行...")
        except:
            pass

class PythonPackager(QMainWindow):
    """Python打包工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.config = self.load_config()
        self.init_ui()
        self.package_thread = None
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("Python项目打包工具")
        self.setFixedSize(800, 700)
        
        # 设置图标（如果存在）
        if os.path.exists("mylogo.ico"):
            self.setWindowIcon(QIcon("mylogo.ico"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 基本设置选项卡
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "基本设置")
        
        # 高级设置选项卡
        advanced_tab = self.create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级设置")
        
        # 版本信息选项卡
        version_tab = self.create_version_tab()
        tab_widget.addTab(version_tab, "版本信息")
        
        # 进度显示
        progress_group = QGroupBox("打包进度")
        progress_layout = QVBoxLayout()
        
        self.progress_text = QTextEdit()
        self.progress_text.setMaximumHeight(150)
        self.progress_text.setReadOnly(True)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        progress_layout.addWidget(self.progress_text)
        progress_layout.addWidget(self.progress_bar)
        progress_group.setLayout(progress_layout)
        
        main_layout.addWidget(progress_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.package_btn = QPushButton("开始打包")
        self.package_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        self.clear_btn = QPushButton("清空日志")
        self.save_config_btn = QPushButton("保存配置")
        self.load_config_btn = QPushButton("加载配置")
        
        button_layout.addWidget(self.package_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.save_config_btn)
        button_layout.addWidget(self.load_config_btn)
        
        main_layout.addLayout(button_layout)
        
        # 连接信号
        self.package_btn.clicked.connect(self.start_package)
        self.clear_btn.clicked.connect(self.clear_log)
        self.save_config_btn.clicked.connect(self.save_config)
        self.load_config_btn.clicked.connect(self.load_config_dialog)
        
        # 加载配置到界面
        self.load_config_to_ui()

    def create_basic_tab(self):
        """创建基本设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()

        # 主文件选择
        file_group = QGroupBox("主文件")
        file_layout = QGridLayout()

        self.main_file_edit = QLineEdit()
        self.main_file_edit.setPlaceholderText("选择要打包的Python文件(.py)")
        main_file_btn = QPushButton("选择目录")
        main_file_btn.clicked.connect(self.select_main_file)

        file_layout.addWidget(QLabel("主文件:"), 0, 0)
        file_layout.addWidget(self.main_file_edit, 0, 1)
        file_layout.addWidget(main_file_btn, 0, 2)
        file_group.setLayout(file_layout)

        # 图标设置
        icon_group = QGroupBox("图标设置")
        icon_layout = QGridLayout()

        self.icon_edit = QLineEdit()
        self.icon_edit.setPlaceholderText("选择图标文件(.ico)")
        icon_btn = QPushButton("选择图标")
        icon_btn.clicked.connect(self.select_icon)

        icon_layout.addWidget(QLabel("图标文件:"), 0, 0)
        icon_layout.addWidget(self.icon_edit, 0, 1)
        icon_layout.addWidget(icon_btn, 0, 2)
        icon_group.setLayout(icon_layout)

        # 基本选项
        options_group = QGroupBox("打包选项")
        options_layout = QGridLayout()

        self.onefile_check = QCheckBox("打包为单文件 (--onefile)")
        self.onefile_check.setChecked(True)

        self.noconsole_check = QCheckBox("隐藏控制台 (--noconsole)")
        self.noconsole_check.setChecked(True)

        self.auto_run_check = QCheckBox("打包完成后自动运行")

        options_layout.addWidget(self.onefile_check, 0, 0)
        options_layout.addWidget(self.noconsole_check, 0, 1)
        options_layout.addWidget(self.auto_run_check, 1, 0)
        options_group.setLayout(options_layout)

        # 输出设置
        output_group = QGroupBox("输出设置")
        output_layout = QFormLayout()

        self.output_name_edit = QLineEdit()
        self.output_name_edit.setPlaceholderText("输出文件名（不含扩展名）")

        output_layout.addRow("输出文件名:", self.output_name_edit)
        output_group.setLayout(output_layout)

        layout.addWidget(file_group)
        layout.addWidget(icon_group)
        layout.addWidget(options_group)
        layout.addWidget(output_group)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_advanced_tab(self):
        """创建高级设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()

        # Python环境选择
        python_group = QGroupBox("Python环境")
        python_layout = QGridLayout()
        
        self.default_python_radio = QRadioButton("使用默认Python环境")
        self.default_python_radio.setChecked(True)
        self.custom_python_radio = QRadioButton("使用自定义Python环境")
        
        self.python_path_edit = QLineEdit()
        self.python_path_edit.setPlaceholderText("选择Python解释器路径(python.exe)")
        self.python_path_edit.setEnabled(False)
        
        python_path_btn = QPushButton("浏览")
        python_path_btn.clicked.connect(self.select_python_path)
        python_path_btn.setEnabled(False)
        
        # 连接信号
        self.default_python_radio.toggled.connect(lambda checked: self.toggle_python_path(not checked))
        self.custom_python_radio.toggled.connect(self.toggle_python_path)
        
        python_layout.addWidget(self.default_python_radio, 0, 0, 1, 3)
        python_layout.addWidget(self.custom_python_radio, 1, 0, 1, 3)
        python_layout.addWidget(QLabel("解释器路径:"), 2, 0)
        python_layout.addWidget(self.python_path_edit, 2, 1)
        python_layout.addWidget(python_path_btn, 2, 2)
        
        python_group.setLayout(python_layout)
        
        # 依赖目录
        data_group = QGroupBox("资源/依赖目录")
        data_layout = QVBoxLayout()

        data_info = QLabel("支持添加资源/依赖目录，程序会自动打包相关依赖")
        data_info.setStyleSheet("color: #666; font-style: italic;")

        self.data_paths_edit = QTextEdit()
        self.data_paths_edit.setMaximumHeight(100)
        self.data_paths_edit.setPlaceholderText("每行一个路径，支持文件夹下所有依赖自动打包")

        data_btn_layout = QHBoxLayout()
        add_data_btn = QPushButton("添加目录")
        add_data_btn.clicked.connect(self.add_data_path)
        clear_data_btn = QPushButton("清空")
        clear_data_btn.clicked.connect(lambda: self.data_paths_edit.clear())

        data_btn_layout.addWidget(add_data_btn)
        data_btn_layout.addWidget(clear_data_btn)
        data_btn_layout.addStretch()

        data_layout.addWidget(data_info)
        data_layout.addWidget(self.data_paths_edit)
        data_layout.addLayout(data_btn_layout)
        data_group.setLayout(data_layout)

        # 环境信息
        env_group = QGroupBox("环境信息")
        env_layout = QFormLayout()

        env_info = QLabel("内置 Python 3.9 环境，基于 PyInstaller 5.13.0 构建")
        env_info.setStyleSheet("color: #4CAF50; font-weight: bold;")

        env_layout.addRow("环境:", env_info)
        env_group.setLayout(env_layout)

        layout.addWidget(python_group)
        layout.addWidget(data_group)
        layout.addWidget(env_group)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_version_tab(self):
        """创建版本信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()

        version_group = QGroupBox("版本信息")
        version_layout = QFormLayout()

        self.company_edit = QLineEdit()
        self.company_edit.setPlaceholderText("公司名称")

        self.product_name_edit = QLineEdit()
        self.product_name_edit.setPlaceholderText("产品名称")

        self.file_version_edit = QLineEdit()
        self.file_version_edit.setPlaceholderText("*******")

        self.product_version_edit = QLineEdit()
        self.product_version_edit.setPlaceholderText("*******")

        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("程序描述")

        self.copyright_edit = QLineEdit()
        self.copyright_edit.setPlaceholderText("版权信息")

        version_layout.addRow("公司名称:", self.company_edit)
        version_layout.addRow("产品名称:", self.product_name_edit)
        version_layout.addRow("文件版本:", self.file_version_edit)
        version_layout.addRow("产品版本:", self.product_version_edit)
        version_layout.addRow("程序描述:", self.description_edit)
        version_layout.addRow("版权信息:", self.copyright_edit)

        version_group.setLayout(version_layout)

        layout.addWidget(version_group)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def select_main_file(self):
        """选择主文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Python文件", "", "Python Files (*.py)"
        )
        if file_path:
            self.main_file_edit.setText(file_path)
            # 自动设置输出文件名
            name = os.path.splitext(os.path.basename(file_path))[0]
            self.output_name_edit.setText(name)

    def select_icon(self):
        """选择图标文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图标文件", "", "Icon Files (*.ico)"
        )
        if file_path:
            self.icon_edit.setText(file_path)

    def add_data_path(self):
        """添加数据路径"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择资源/依赖目录")
        if dir_path:
            current_text = self.data_paths_edit.toPlainText()
            if current_text:
                current_text += "\n"
            current_text += dir_path
            self.data_paths_edit.setPlainText(current_text)

    def start_package(self):
        """开始打包"""
        # 验证输入
        if not self.main_file_edit.text():
            QMessageBox.warning(self, "警告", "请选择要打包的Python文件！")
            return

        if not os.path.exists(self.main_file_edit.text()):
            QMessageBox.warning(self, "警告", "选择的Python文件不存在！")
            return

        # 准备配置
        config = {
            'source_file': self.main_file_edit.text(),
            'icon': self.icon_edit.text(),
            'name': self.output_name_edit.text() or os.path.splitext(
                os.path.basename(self.main_file_edit.text()))[0],
            'onefile': self.onefile_check.isChecked(),
            'noconsole': self.noconsole_check.isChecked(),
            'auto_run': self.auto_run_check.isChecked(),
            'add_data': [path.strip() for path in
                        self.data_paths_edit.toPlainText().split('\n') if path.strip()],
            'version_info': None
        }
        
        # 添加Python环境配置
        if hasattr(self, 'custom_python_radio') and self.custom_python_radio.isChecked():
            python_path = self.python_path_edit.text()
            if python_path and os.path.exists(python_path):
                config['python_path'] = python_path
            elif python_path:
                QMessageBox.warning(self, "警告", "指定的Python解释器路径不存在！")
                return

        # 版本信息
        if any([self.company_edit.text(), self.product_name_edit.text(),
                self.file_version_edit.text(), self.product_version_edit.text()]):
            config['version_info'] = {
                'company': self.company_edit.text() or "Unknown Company",
                'product_name': self.product_name_edit.text() or config['name'],
                'file_version': self.file_version_edit.text() or "*******",
                'product_version': self.product_version_edit.text() or "*******",
                'description': self.description_edit.text() or config['name'],
                'copyright': self.copyright_edit.text() or "Copyright (C) 2024",
                'internal_name': config['name'],
                'original_filename': f"{config['name']}.exe"
            }

        # 禁用按钮
        self.package_btn.setEnabled(False)
        self.package_btn.setText("打包中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(0)  # 无限进度条

        # 清空日志
        self.progress_text.clear()
        self.log_message("开始打包...")

        # 启动打包线程
        self.package_thread = PackageThread(config)
        self.package_thread.progress.connect(self.log_message)
        self.package_thread.finished.connect(self.package_finished)
        self.package_thread.start()

    def package_finished(self, success, message):
        """打包完成"""
        self.package_btn.setEnabled(True)
        self.package_btn.setText("开始打包")
        self.progress_bar.setVisible(False)

        self.log_message(message)

        if success:
            QMessageBox.information(self, "成功", message)
            # 如果选择了自动运行
            if self.auto_run_check.isChecked():
                self.try_auto_run()
        else:
            QMessageBox.critical(self, "失败", message)

    def try_auto_run(self):
        """尝试自动运行生成的exe"""
        try:
            dist_dir = Path("dist")
            if dist_dir.exists():
                exe_files = list(dist_dir.glob("*.exe"))
                if exe_files:
                    exe_path = exe_files[0]
                    self.log_message(f"正在运行: {exe_path}")
                    subprocess.Popen([str(exe_path)], shell=True)
        except Exception as e:
            self.log_message(f"自动运行失败: {str(e)}")

    def log_message(self, message):
        """记录日志消息"""
        self.progress_text.append(f"[{self.get_timestamp()}] {message}")
        self.progress_text.ensureCursorVisible()

    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def clear_log(self):
        """清空日志"""
        self.progress_text.clear()

    def save_config(self):
        """保存配置"""
        config = {
            'main_file': self.main_file_edit.text(),
            'icon': self.icon_edit.text(),
            'output_name': self.output_name_edit.text(),
            'onefile': self.onefile_check.isChecked(),
            'noconsole': self.noconsole_check.isChecked(),
            'auto_run': self.auto_run_check.isChecked(),
            'data_paths': self.data_paths_edit.toPlainText(),
            'company': self.company_edit.text(),
            'product_name': self.product_name_edit.text(),
            'file_version': self.file_version_edit.text(),
            'product_version': self.product_version_edit.text(),
            'description': self.description_edit.text(),
            'copyright': self.copyright_edit.text()
        }

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存配置", "config.json", "JSON Files (*.json)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                QMessageBox.information(self, "成功", "配置已保存")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def load_config_dialog(self):
        """加载配置对话框"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载配置", "", "JSON Files (*.json)"
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.load_config_to_ui(config)
                QMessageBox.information(self, "成功", "配置已加载")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载配置失败: {str(e)}")

    def load_config(self):
        """加载默认配置"""
        default_config = {
            'main_file': '',
            'icon': '',
            'output_name': '',
            'onefile': True,
            'noconsole': True,
            'auto_run': False,
            'data_paths': '',
            'company': '',
            'product_name': '',
            'file_version': '*******',
            'product_version': '*******',
            'description': '',
            'copyright': 'Copyright (C) 2024',
            'python_path': '',  # 添加Python路径配置
            'use_custom_python': False  # 是否使用自定义Python
        }

        config_file = 'packager_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                default_config.update(saved_config)
            except:
                pass

        return default_config

    def load_config_to_ui(self, config=None):
        """将配置加载到界面"""
        if config is None:
            config = self.config

        self.main_file_edit.setText(config.get('main_file', ''))
        self.icon_edit.setText(config.get('icon', ''))
        self.output_name_edit.setText(config.get('output_name', ''))
        self.onefile_check.setChecked(config.get('onefile', True))
        self.noconsole_check.setChecked(config.get('noconsole', True))
        self.auto_run_check.setChecked(config.get('auto_run', False))
        self.data_paths_edit.setPlainText(config.get('data_paths', ''))
        self.company_edit.setText(config.get('company', ''))
        self.product_name_edit.setText(config.get('product_name', ''))
        self.file_version_edit.setText(config.get('file_version', '*******'))
        self.product_version_edit.setText(config.get('product_version', '*******'))
        self.description_edit.setText(config.get('description', ''))
        self.copyright_edit.setText(config.get('copyright', 'Copyright (C) 2024'))

        # 加载Python环境配置
        if hasattr(self, 'custom_python_radio'):
            use_custom = config.get('use_custom_python', False)
            self.default_python_radio.setChecked(not use_custom)
            self.custom_python_radio.setChecked(use_custom)
            self.python_path_edit.setText(config.get('python_path', ''))
            self.toggle_python_path(use_custom)

    def closeEvent(self, event):
        """关闭事件：自动保存配置"""
        config = {
            'main_file': self.main_file_edit.text(),
            'icon': self.icon_edit.text(),
            'output_name': self.output_name_edit.text(),
            'onefile': self.onefile_check.isChecked(),
            'noconsole': self.noconsole_check.isChecked(),
            'auto_run': self.auto_run_check.isChecked(),
            'data_paths': self.data_paths_edit.toPlainText(),
            'company': self.company_edit.text(),
            'product_name': self.product_name_edit.text(),
            'file_version': self.file_version_edit.text(),
            'product_version': self.product_version_edit.text(),
            'description': self.description_edit.text(),
            'copyright': self.copyright_edit.text(),
            # 添加Python环境配置
            'use_custom_python': hasattr(self, 'custom_python_radio') and self.custom_python_radio.isChecked(),
            'python_path': self.python_path_edit.text() if hasattr(self, 'python_path_edit') else ''
        }

        try:
            with open('packager_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except:
            pass

        event.accept()

    def toggle_python_path(self, enabled):
        """启用/禁用Python路径选择"""
        self.python_path_edit.setEnabled(enabled)
        # 找到浏览按钮并设置状态
        for child in self.findChildren(QPushButton):
            if child.parent() == self.python_path_edit.parent() and child != self.sender():
                child.setEnabled(enabled)
                break

    def select_python_path(self):
        """选择Python解释器路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Python解释器", "", "Python解释器 (python.exe)"
        )
        if file_path:
            self.python_path_edit.setText(file_path)


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("Python项目打包工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Python工具箱")

    # 设置样式
    app.setStyle('Fusion')

    # 设置样式表
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #e1e1e1;
            border: 1px solid #999999;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        QPushButton:hover {
            background-color: #d4d4d4;
        }
        QPushButton:pressed {
            background-color: #c0c0c0;
        }
        QLineEdit, QTextEdit {
            border: 1px solid #cccccc;
            border-radius: 3px;
            padding: 5px;
            background-color: white;
        }
        QLineEdit:focus, QTextEdit:focus {
            border: 2px solid #4CAF50;
        }
        QCheckBox {
            spacing: 5px;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QCheckBox::indicator:unchecked {
            border: 1px solid #cccccc;
            background-color: white;
            border-radius: 3px;
        }
        QCheckBox::indicator:checked {
            border: 1px solid #4CAF50;
            background-color: #4CAF50;
            border-radius: 3px;
        }
    """)

    # 创建并显示主窗口
    window = PythonPackager()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()




