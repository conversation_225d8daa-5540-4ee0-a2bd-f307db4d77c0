"""
内存优化的文件处理模块
使用生成器和流式处理减少内存占用
"""

import os
import gc
import psutil
from typing import Generator, List, Dict, Optional, Tuple
from dataclasses import dataclass
from collections import deque
import threading
import time


@dataclass
class FileInfo:
    """轻量级文件信息类"""
    path: str
    name: str
    size: int
    modified: float
    
    @classmethod
    def from_path(cls, file_path: str, base_path: str = ""):
        """从文件路径创建FileInfo对象"""
        try:
            stat = os.stat(file_path)
            return cls(
                path=file_path,
                name=os.path.basename(file_path),
                size=stat.st_size,
                modified=stat.st_mtime
            )
        except (OSError, IOError):
            return cls(
                path=file_path,
                name=os.path.basename(file_path),
                size=0,
                modified=0.0
            )


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, threshold_mb: int = 500):
        self.threshold_bytes = threshold_mb * 1024 * 1024
        self.process = psutil.Process()
        self._monitoring = False
        self._monitor_thread = None
    
    def get_memory_usage(self) -> int:
        """获取当前内存使用量（字节）"""
        try:
            return self.process.memory_info().rss
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            # 如果进程不存在或无权限，重新获取当前进程
            try:
                self.process = psutil.Process()
                return self.process.memory_info().rss
            except:
                return 0

    def get_memory_usage_mb(self) -> float:
        """获取当前内存使用量（MB）"""
        usage_bytes = self.get_memory_usage()
        return usage_bytes / (1024 * 1024) if usage_bytes > 0 else 0.0

    def is_memory_high(self) -> bool:
        """检查内存使用是否过高"""
        usage = self.get_memory_usage()
        return usage > 0 and usage > self.threshold_bytes
    
    def force_gc_if_needed(self):
        """如果内存使用过高则强制垃圾回收"""
        if self.is_memory_high():
            gc.collect()
    
    def start_monitoring(self, callback=None):
        """开始监控内存使用"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            args=(callback,), 
            daemon=True
        )
        self._monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控内存使用"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
    
    def _monitor_loop(self, callback):
        """内存监控循环"""
        while self._monitoring:
            try:
                usage_mb = self.get_memory_usage_mb()
                if callback and usage_mb > 0:
                    callback(usage_mb)

                if self.is_memory_high():
                    gc.collect()

                time.sleep(1.0)
            except Exception:
                # 如果监控过程中出现错误，继续监控但降低频率
                time.sleep(5.0)


class StreamingFileProcessor:
    """流式文件处理器"""
    
    def __init__(self, batch_size: int = 1000, memory_threshold_mb: int = 500):
        self.batch_size = batch_size
        self.memory_monitor = MemoryMonitor(memory_threshold_mb)
        self.processed_count = 0
        self._file_buffer = deque()
    
    def scan_files_streaming(self, search_path: str, extensions: List[str], 
                           progress_callback=None) -> Generator[FileInfo, None, None]:
        """流式扫描文件，逐个返回文件信息"""
        if not os.path.exists(search_path):
            return
        
        # 预处理扩展名
        ext_set = {ext.lower() for ext in extensions}
        processed = 0
        
        try:
            for root, dirs, files in os.walk(search_path):
                # 过滤隐藏目录
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                for filename in files:
                    if filename.startswith('.'):
                        continue
                    
                    # 检查扩展名
                    _, ext = os.path.splitext(filename)
                    if ext.lower() not in ext_set:
                        continue
                    
                    file_path = os.path.join(root, filename)
                    file_info = FileInfo.from_path(file_path, search_path)
                    
                    yield file_info
                    
                    processed += 1
                    
                    # 定期检查内存并更新进度
                    if processed % 100 == 0:
                        self.memory_monitor.force_gc_if_needed()
                        if progress_callback:
                            progress_callback(f"已扫描 {processed} 个文件")
                    
        except Exception as e:
            if progress_callback:
                progress_callback(f"扫描出错: {str(e)}")
    
    def batch_process_files(self, file_generator: Generator[FileInfo, None, None],
                          processor_func, progress_callback=None) -> Generator[List, None, None]:
        """批量处理文件，减少内存占用"""
        batch = []
        processed = 0
        
        try:
            for file_info in file_generator:
                batch.append(file_info)
                
                # 当批次达到指定大小或内存使用过高时处理
                if (len(batch) >= self.batch_size or 
                    self.memory_monitor.is_memory_high()):
                    
                    if batch:
                        result = processor_func(batch)
                        yield result
                        
                        processed += len(batch)
                        batch.clear()
                        
                        # 强制垃圾回收
                        gc.collect()
                        
                        if progress_callback:
                            progress_callback(f"已处理 {processed} 个文件")
            
            # 处理剩余的文件
            if batch:
                result = processor_func(batch)
                yield result
                processed += len(batch)
                
                if progress_callback:
                    progress_callback(f"处理完成，共 {processed} 个文件")
                    
        except Exception as e:
            if progress_callback:
                progress_callback(f"批量处理出错: {str(e)}")
    
    def get_file_stats_streaming(self, search_path: str, extensions: List[str]) -> Dict:
        """流式获取文件统计信息"""
        stats = {
            'total_files': 0,
            'total_size': 0,
            'size_distribution': {'small': 0, 'medium': 0, 'large': 0},
            'extension_count': {},
            'largest_file': None,
            'oldest_file': None,
            'newest_file': None
        }
        
        largest_size = 0
        oldest_time = float('inf')
        newest_time = 0
        
        for file_info in self.scan_files_streaming(search_path, extensions):
            stats['total_files'] += 1
            stats['total_size'] += file_info.size
            
            # 文件大小分布
            if file_info.size < 1024 * 1024:  # < 1MB
                stats['size_distribution']['small'] += 1
            elif file_info.size < 10 * 1024 * 1024:  # < 10MB
                stats['size_distribution']['medium'] += 1
            else:
                stats['size_distribution']['large'] += 1
            
            # 扩展名统计
            _, ext = os.path.splitext(file_info.name)
            ext = ext.lower()
            stats['extension_count'][ext] = stats['extension_count'].get(ext, 0) + 1
            
            # 最大文件
            if file_info.size > largest_size:
                largest_size = file_info.size
                stats['largest_file'] = file_info.path
            
            # 最旧和最新文件
            if file_info.modified < oldest_time:
                oldest_time = file_info.modified
                stats['oldest_file'] = file_info.path
            
            if file_info.modified > newest_time:
                newest_time = file_info.modified
                stats['newest_file'] = file_info.path
            
            # 定期清理内存
            if stats['total_files'] % 1000 == 0:
                self.memory_monitor.force_gc_if_needed()
        
        return stats


class ChunkedResultProcessor:
    """分块结果处理器"""
    
    def __init__(self, chunk_size: int = 500):
        self.chunk_size = chunk_size
    
    def process_results_in_chunks(self, results: List, processor_func) -> Generator:
        """分块处理结果，避免一次性处理大量数据"""
        for i in range(0, len(results), self.chunk_size):
            chunk = results[i:i + self.chunk_size]
            yield processor_func(chunk)
            
            # 处理完一块后清理内存
            gc.collect()
    
    def merge_chunked_results(self, chunk_generator) -> List:
        """合并分块处理的结果"""
        merged_results = []
        
        for chunk_result in chunk_generator:
            if isinstance(chunk_result, list):
                merged_results.extend(chunk_result)
            else:
                merged_results.append(chunk_result)
            
            # 定期清理内存
            if len(merged_results) % 1000 == 0:
                gc.collect()
        
        return merged_results


class MemoryEfficientCache:
    """内存高效的缓存实现"""
    
    def __init__(self, max_items: int = 100, max_memory_mb: int = 50):
        self.max_items = max_items
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.cache = {}
        self.access_order = deque()
        self.memory_usage = 0
        self._lock = threading.Lock()
    
    def get(self, key: str):
        """获取缓存项"""
        with self._lock:
            if key in self.cache:
                # 更新访问顺序
                self.access_order.remove(key)
                self.access_order.append(key)
                return self.cache[key]
            return None
    
    def set(self, key: str, value, size_estimate: int = 0):
        """设置缓存项"""
        with self._lock:
            # 如果键已存在，先删除旧值
            if key in self.cache:
                self._remove_key(key)
            
            # 检查是否需要清理空间
            while (len(self.cache) >= self.max_items or 
                   self.memory_usage + size_estimate > self.max_memory_bytes):
                if not self.access_order:
                    break
                oldest_key = self.access_order.popleft()
                self._remove_key(oldest_key)
            
            # 添加新项
            self.cache[key] = value
            self.access_order.append(key)
            self.memory_usage += size_estimate
    
    def _remove_key(self, key: str):
        """删除缓存项"""
        if key in self.cache:
            del self.cache[key]
            # 简单估算：每个缓存项平均占用内存
            if self.cache:
                avg_size = self.memory_usage // len(self.cache)
                self.memory_usage = max(0, self.memory_usage - avg_size)
            else:
                self.memory_usage = 0
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_order.clear()
            self.memory_usage = 0
    
    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'items': len(self.cache),
                'memory_usage_mb': self.memory_usage / (1024 * 1024),
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
            }
