import sys
import os
import configparser
import subprocess
import time
import logging
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QMessageBox, QFileDialog, QTextEdit, QListWidget, QLineEdit, QInputDialog, QDialog, QListWidgetItem, QMenu, QComboBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor
import ctypes

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理类，处理所有与配置文件相关的操作"""
    def __init__(self, config_file=None):
        # 如果没有指定配置文件路径，使用当前脚本所在目录的config.ini
        if config_file is None:
            self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
        else:
            self.config_file = config_file
        
        logger.info(f"使用配置文件: {self.config_file}")
        self.ensure_config_exists()
    
    def ensure_config_exists(self):
        """确保配置文件存在，如果不存在则创建默认配置"""
        if not os.path.exists(self.config_file):
            logger.info(f"配置文件不存在，创建默认配置: {self.config_file}")
            config = configparser.ConfigParser()
            config['Configurations'] = {'names': '默认配置', 'current': '默认配置'}
            config['默认配置'] = {'startup_order': ''}
            config['Settings'] = {'software_directory': '', 'startup_interval': '1'}
            config['Memo'] = {'text': '请输入备忘录内容'}
            config['StartupOrder'] = {'order': ''}
            
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
            except Exception as e:
                logger.error(f"创建配置文件失败: {str(e)}")
    
    def read_config(self):
        """读取配置文件"""
        config = configparser.ConfigParser()
        try:
            logger.info(f"尝试以UTF-8编码读取配置文件: {self.config_file}")
            config.read(self.config_file, encoding='utf-8')
        except UnicodeDecodeError:
            logger.info(f"UTF-8编码失败，尝试以GBK编码读取配置文件: {self.config_file}")
            config.read(self.config_file, encoding='gbk')
        
        # 检查配置文件是否包含必要的节
        if 'Configurations' not in config:
            logger.warning("配置文件缺少Configurations节，添加默认配置")
            config['Configurations'] = {'names': '默认配置', 'current': '默认配置'}
            
        if '默认配置' not in config:
            logger.warning("配置文件缺少默认配置节，添加默认配置")
            config['默认配置'] = {'startup_order': ''}
            
        if 'Settings' not in config:
            logger.warning("配置文件缺少Settings节，添加默认设置")
            config['Settings'] = {'software_directory': '', 'startup_interval': '1'}
            
        return config
    
    def write_config(self, config):
        """写入配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            logger.info(f"配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            return False
    
    def get_configurations(self):
        """获取所有配置名称"""
        config = self.read_config()
        if 'Configurations' in config and 'names' in config['Configurations']:
            names = config['Configurations']['names'].split(',')
            return [name.strip() for name in names if name.strip()]
        return ['默认配置']
    
    def get_current_configuration(self):
        """获取当前配置名称"""
        config = self.read_config()
        if 'Configurations' in config and 'current' in config['Configurations']:
            return config['Configurations']['current']
        return '默认配置'
    
    def set_current_configuration(self, config_name):
        """设置当前配置"""
        config = self.read_config()
        
        # 确保配置名在names列表中
        if 'Configurations' in config:
            names = config['Configurations'].get('names', '').split(',')
            names = [name.strip() for name in names if name.strip()]
            if config_name not in names:
                names.append(config_name)
                config['Configurations']['names'] = ','.join(names)
            
            # 更新当前配置
            config['Configurations']['current'] = config_name
            
            # 确保该配置节存在
            if config_name not in config:
                config[config_name] = {'startup_order': ''}
            
            return self.write_config(config)
        return False
    
    def get_startup_order(self, config_name):
        """获取指定配置的启动顺序"""
        config = self.read_config()
        if config_name in config and 'startup_order' in config[config_name]:
            startup_order = config[config_name]['startup_order'].split(',')
            return [name for name in startup_order if name]
        return []
    
    def set_startup_order(self, config_name, startup_order):
        """设置指定配置的启动顺序"""
        config = self.read_config()
        if config_name not in config:
            config[config_name] = {}
        config[config_name]['startup_order'] = ','.join(startup_order)
        return self.write_config(config)
    
    def get_software_list(self):
        """获取软件列表"""
        config = self.read_config()
        software_list = {}
        for section in config.sections():
            # 排除特殊节和配置节
            if section in ['Memo', 'Settings', 'StartupOrder', 'Configurations', 'EXTENSIONS'] or section.startswith('配置') or section == '默认配置':
                continue
            if 'path' in config[section]:
                software_list[section] = config[section]['path']
        return software_list
    
    def add_software(self, name, path):
        """添加软件"""
        config = self.read_config()
        config[name] = {'path': path}
        return self.write_config(config)
    
    def remove_software(self, name):
        """删除软件"""
        config = self.read_config()
        if name in config:
            config.remove_section(name)
            return self.write_config(config)
        return False
    
    def get_memo_text(self):
        """获取备忘录内容"""
        config = self.read_config()
        if 'Memo' in config and 'text' in config['Memo']:
            return config['Memo']['text']
        return '请输入备忘录内容'
    
    def set_memo_text(self, text):
        """设置备忘录内容"""
        config = self.read_config()
        if 'Memo' not in config:
            config['Memo'] = {}
        config['Memo']['text'] = text
        return self.write_config(config)
    
    def get_software_directory(self):
        """获取软件目录"""
        config = self.read_config()
        if 'Settings' in config and 'software_directory' in config['Settings']:
            return config['Settings']['software_directory']
        return ''
    
    def set_software_directory(self, directory):
        """设置软件目录"""
        config = self.read_config()
        if 'Settings' not in config:
            config['Settings'] = {}
        config['Settings']['software_directory'] = directory
        return self.write_config(config)
    
    def get_startup_interval(self):
        """获取启动间隔"""
        config = self.read_config()
        if 'Settings' in config and 'startup_interval' in config['Settings']:
            try:
                return int(config['Settings']['startup_interval'])
            except ValueError:
                return 1
        return 1
    
    def set_startup_interval(self, interval):
        """设置启动间隔"""
        config = self.read_config()
        if 'Settings' not in config:
            config['Settings'] = {}
        config['Settings']['startup_interval'] = str(interval)
        return self.write_config(config)
    
    def add_new_configuration(self, startup_order):
        """添加新配置"""
        config = self.read_config()
        
        # 获取现有配置名称
        config_names = []
        if 'Configurations' in config and 'names' in config['Configurations']:
            config_names = config['Configurations']['names'].split(',')
            config_names = [name.strip() for name in config_names if name.strip()]
        
        # 确保默认配置存在
        if '默认配置' not in config_names:
            config_names.append('默认配置')
        
        # 生成新的配置名称
        new_config_name = self._generate_new_config_name(config_names)
        config_names.append(new_config_name)
        
        # 更新Configurations节
        if 'Configurations' not in config:
            config['Configurations'] = {}
        config['Configurations']['names'] = ','.join(config_names)
        config['Configurations']['current'] = new_config_name
        
        # 保存新配置的启动顺序
        config[new_config_name] = {'startup_order': ','.join(startup_order)}
        
        if self.write_config(config):
            return new_config_name
        return None
    
    def _generate_new_config_name(self, existing_names):
        """生成新的配置名称"""
        max_num = 0
        for name in existing_names:
            if name.startswith('配置'):
                try:
                    num = int(name[2:])
                    if num > max_num:
                        max_num = num
                except ValueError:
                    pass
        return f'配置{max_num + 1}'


class SoftwareLauncher(QWidget):
    config_saved = pyqtSignal()

    def __init__(self):
        super().__init__()
        # 使用ConfigManager管理配置
        self.config_manager = ConfigManager()
        self.software_list = {}
        self.startup_interval = 1  # 默认启动间隔为1秒
        self.current_drive = os.path.splitdrive(os.path.abspath(__file__))[0]
        self.software_directory = self.config_manager.get_software_directory()
        self.startup_order = []
        self.initUI()
        self.load_config()
        self.load_software_from_directory()

    def initUI(self):
        # 缩小 UI 界面
        self.setWindowTitle('软件启动工具')
        self.setGeometry(800, 400, 400, 100)
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 8px;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QLabel {
                font-size: 14px;
                color: #333;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
            QListWidget {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        # 主布局采用水平布局，左侧放按钮，右侧放编辑窗口
        main_layout = QHBoxLayout()

        # 左侧按钮布局，竖向排列
        left_layout = QVBoxLayout()

        # 软件目录选择部分
        dir_layout = QVBoxLayout()
        dir_label = QLabel('软件目录:')
        dir_text = QLineEdit()
        dir_text.setReadOnly(True)
        dir_text.setText(self.software_directory)
        dir_button = QPushButton('批量导入目录')
        dir_button.clicked.connect(self.select_directory)
        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(dir_text)
        dir_layout.addWidget(dir_button)
        left_layout.addLayout(dir_layout)

        # 功能按钮区域
        self.open_dev_manager_btn = QPushButton('打开 网络适配器')
        self.open_dev_manager_btn.clicked.connect(self.open_device_manager_network)
        self.update_ax88179_driver_btn = QPushButton('更新AX88179网卡驱动')
        self.update_ax88179_driver_btn.clicked.connect(self.update_ax88179_driver)
        self.add_button = QPushButton('添加软件')
        self.add_button.clicked.connect(self.add_custom_software)
        # 添加按钮用于将软件添加到一键启动列表
        self.add_to_startup_btn = QPushButton('添加到一键启动')
        self.add_to_startup_btn.clicked.connect(self.add_to_startup_order)
        left_layout.addWidget(self.open_dev_manager_btn)
        left_layout.addWidget(self.update_ax88179_driver_btn)
        left_layout.addWidget(self.add_button)
        left_layout.addWidget(self.add_to_startup_btn)

        # 时间间隔设置
        interval_layout = QHBoxLayout()  # 改为水平布局，让标签和输入框横向排列
        interval_label = QLabel('启动间隔（秒）:')
        self.interval_input = QLineEdit(str(self.startup_interval))
        self.interval_input.editingFinished.connect(self.set_startup_interval)  # 绑定 editingFinished 信号
        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.interval_input)
        left_layout.addLayout(interval_layout)

        # 一键启动按钮
        start_btn = QPushButton('一键启动')
        start_btn.clicked.connect(self.one_click_start)
        left_layout.addWidget(start_btn)

        # 右侧编辑窗口布局
        right_layout = QVBoxLayout()

        # 备忘录区域
        memo_layout = QVBoxLayout()
        memo_label = QLabel('备忘录:')
        self.memo_text = QTextEdit()
        self.memo_text.setPlainText(self.config_manager.get_memo_text())
        # 添加右键菜单
        self.memo_text.setContextMenuPolicy(Qt.CustomContextMenu)
        self.memo_text.customContextMenuRequested.connect(self.show_memo_context_menu)
        memo_layout.addWidget(memo_label)
        memo_layout.addWidget(self.memo_text)
        right_layout.addLayout(memo_layout)

        # 软件列表区域 - 单独启动
        self.software_list_widget = QListWidget()
        self.software_list_widget.setSelectionMode(QListWidget.SingleSelection)
        self.software_list_widget.itemDoubleClicked.connect(self.launch_selected_software)
        # 绑定右键菜单事件
        self.software_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.software_list_widget.customContextMenuRequested.connect(self.show_single_launch_context_menu)
        right_layout.addWidget(self.software_list_widget)

        # 启动顺序设置和一键启动
        order_layout = QVBoxLayout()
        order_label = QLabel('设置启动顺序:')
        self.order_list_widget = QListWidget()
        self.order_list_widget.setSelectionMode(QListWidget.MultiSelection)
        self.order_list_widget.setDragEnabled(True)  # 启用拖拽
        self.order_list_widget.setDragDropMode(QListWidget.InternalMove)  # 设置内部移动模式
        # 绑定右键菜单事件
        self.order_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.order_list_widget.customContextMenuRequested.connect(self.show_one_click_launch_context_menu)

        order_layout.addWidget(order_label)
        order_layout.addWidget(self.order_list_widget)

        # 添加保存配置按钮
        save_order_config_btn = QPushButton('保存一键启动配置')
        save_order_config_btn.clicked.connect(self.save_config)
        order_layout.addWidget(save_order_config_btn)

        right_layout.addLayout(order_layout)

        # 将左右布局添加到主布局
        main_layout.addLayout(left_layout)
        main_layout.addLayout(right_layout)

        # 设置左右布局比例：按钮1，编辑框2
        main_layout.setStretch(0, 1)
        main_layout.setStretch(1, 2)

        self.setLayout(main_layout)

    def show_single_launch_context_menu(self, pos):
        item = self.software_list_widget.itemAt(pos)
        if item:
            menu = QMenu(self)
            open_dir_action = menu.addAction("打开目录")
            delete_action = menu.addAction("删除")
            action = menu.exec_(self.software_list_widget.mapToGlobal(pos))
            software_name = item.text()
            if action == open_dir_action:
                path = self.software_list.get(software_name)
                if path and os.path.exists(os.path.dirname(path)):
                    os.startfile(os.path.dirname(path))
                else:
                    QMessageBox.warning(self, "提示", "未找到该软件的目录")
            elif action == delete_action:
                if software_name in self.software_list:
                    del self.software_list[software_name]
                    self.config_manager.remove_software(software_name)
                self.software_list_widget.takeItem(self.software_list_widget.row(item))

    def show_one_click_launch_context_menu(self, pos):
        item = self.order_list_widget.itemAt(pos)
        if item:
            menu = QMenu(self)
            open_dir_action = menu.addAction("打开目录")
            delete_action = menu.addAction("删除")
            action = menu.exec_(self.order_list_widget.mapToGlobal(pos))
            software_name = item.text()
            if action == open_dir_action:
                path = self.software_list.get(software_name)
                if path and os.path.exists(os.path.dirname(path)):
                    os.startfile(os.path.dirname(path))
                else:
                    QMessageBox.warning(self, "提示", "未找到该软件的目录")
            elif action == delete_action:
                if software_name in self.startup_order:
                    self.startup_order.remove(software_name)
                self.order_list_widget.takeItem(self.order_list_widget.row(item))
                # 更新当前配置的启动顺序
                current_config = self.config_manager.get_current_configuration()
                self.config_manager.set_startup_order(current_config, self.startup_order)

    def select_directory(self):
        initial_dir = self.current_drive + '\\'
        directory = QFileDialog.getExistingDirectory(self, "选择软件目录", initial_dir)
        if directory:
            self.software_directory = directory
            self.config_manager.set_software_directory(directory)
            self.load_software_from_directory()

    def add_custom_software(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择EXE、INF或BAT文件', '', 'Executable Files (*.exe);;INF Files (*.inf);;BAT Files (*.bat)')
        if file_path:
            try:
                software_name = os.path.basename(file_path).split(".")[0]
                # 保存规范化后的路径
                normalized_path = os.path.normpath(file_path)
                self.software_list[software_name] = normalized_path
                self.software_list_widget.addItem(software_name)
                self.config_manager.add_software(software_name, normalized_path)
            except Exception as e:
                QMessageBox.critical(self, '错误', f'无法处理该文件：{e}')

    def launch_software(self, path):
        try:
            normalized_path = os.path.normpath(path)
            if normalized_path.lower().endswith(('.inf', '.bat')):
                # 使用绝对路径和正确的shell参数
                subprocess.Popen(['cmd.exe', '/c', f'"{normalized_path}"'], shell=True)
            elif normalized_path.lower().endswith('.exe'):
                # 获取程序所在目录作为工作目录
                working_dir = os.path.dirname(normalized_path)
                try:
                    # 尝试以管理员身份启动
                    ctypes.windll.shell32.ShellExecuteW(None, "runas", normalized_path, "", working_dir, 1)
                except Exception:
                    # 若失败，以普通方式启动
                    subprocess.Popen([f'"{normalized_path}"'], shell=True, cwd=working_dir)
            else:
                os.startfile(normalized_path)
        except Exception as e:
            QMessageBox.critical(self, '错误', f'无法启动软件: {str(e)}')

    def launch_selected_software(self, item):
        name = item.text()
        path = self.software_list.get(name)
        if path:
            self.launch_software(path)

    def set_startup_interval(self):
        try:
            interval = int(self.interval_input.text())
            self.startup_interval = interval
            self.config_manager.set_startup_interval(interval)
            QMessageBox.information(self, '提示', '启动间隔设置成功')
        except ValueError:
            QMessageBox.critical(self, '错误', '请输入有效的整数')
            self.interval_input.setText(str(self.startup_interval))  # 恢复之前的有效数值

    def one_click_start(self):
        # 获取当前列表中的所有项（按显示顺序）
        startup_order = []
        for i in range(self.order_list_widget.count()):
            startup_order.append(self.order_list_widget.item(i).text())

        if not startup_order:
            QMessageBox.warning(self, '警告', '请先选择要启动的软件')
            return

        for name in startup_order:
            path = self.software_list.get(name)
            if path:
                self.launch_software(path)
                time.sleep(self.startup_interval)

    def add_to_startup_order(self):
        selected_items = self.software_list_widget.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, '警告', '请先选择要添加到一键启动的软件')
            return

        for item in selected_items:
            software_name = item.text()
            if software_name not in self.startup_order:
                self.startup_order.append(software_name)
                self.order_list_widget.addItem(QListWidgetItem(software_name))

        # 更新当前配置的启动顺序
        current_config = self.config_manager.get_current_configuration()
        self.config_manager.set_startup_order(current_config, self.startup_order)

    def save_config(self):
        # 获取当前列表顺序
        current_order = []
        for i in range(self.order_list_widget.count()):
            current_order.append(self.order_list_widget.item(i).text())
        
        # 保存备忘录内容
        memo_text = self.memo_text.toPlainText()
        self.config_manager.set_memo_text(memo_text)
        
        # 创建新的配置
        new_config_name = self.config_manager.add_new_configuration(current_order)
        if new_config_name:
            QMessageBox.information(self, '成功', f'已创建新配置：{new_config_name}')
            self.config_saved.emit()  # 保存后发出信号
        else:
            QMessageBox.critical(self, '错误', '保存配置失败')

    def load_config(self):
        # 加载启动间隔
        self.startup_interval = self.config_manager.get_startup_interval()
        self.interval_input.setText(str(self.startup_interval))
        
        # 加载备忘录内容
        self.memo_text.setPlainText(self.config_manager.get_memo_text())
        
        # 加载软件列表
        self.software_list = self.config_manager.get_software_list()
        self.software_list_widget.clear()
        for name in self.software_list:
            self.software_list_widget.addItem(name)
        
        # 加载当前配置的启动顺序
        current_config = self.config_manager.get_current_configuration()
        self.startup_order = self.config_manager.get_startup_order(current_config)
        
        # 更新启动顺序列表
        self.order_list_widget.clear()
        for name in self.startup_order:
            if name in self.software_list:
                self.order_list_widget.addItem(QListWidgetItem(name))

    def load_software_from_directory(self):
        if not os.path.exists(self.software_directory):
            return

        self.software_list_widget.clear()
        self.software_list = {}
        for root, dirs, files in os.walk(self.software_directory):
            for file in files:
                if file.lower().endswith(('.exe', '.inf', '.bat')):
                    file_path = os.path.join(root, file)
                    software_name = os.path.basename(file_path).split(".")[0]
                    normalized_path = os.path.normpath(file_path)
                    self.software_list[software_name] = normalized_path
                    self.software_list_widget.addItem(software_name)
                    self.config_manager.add_software(software_name, normalized_path)

    def open_device_manager_network(self):
        try:
            os.system('devmgmt.msc /select @pci.sys,#{4d36e972-e325-11ce-bfc1-08002be10318}')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'无法打开设备管理器: {str(e)}')

    def update_ax88179_driver(self):
        try:
            powershell_script = """
            $device = Get-PnpDevice | Where-Object {$_.Name -like "*ASIX AX88179*"}
            if ($device) {
                $device | Update-PnpDevice -DriverUpdateLocation "C:\\Windows\\System32\\DriverStore\\FileRepository" -Confirm:$false
            } else {
                Write-Output "未找到 ASIX AX88179 网卡设备"
            }
            """
            result = subprocess.run(['powershell', '-Command', powershell_script], capture_output=True, text=True)
            if "未找到 ASIX AX88179 网卡设备" in result.stdout:
                QMessageBox.warning(self, '警告', '未找到 ASIX AX88179 网卡设备')
            else:
                QMessageBox.information(self, '提示', '已尝试更新 ASIX AX88179 网卡驱动')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'更新驱动时出错: {str(e)}')

    def show_memo_context_menu(self, pos):
        menu = QMenu(self)
        copy_action = menu.addAction("复制")
        paste_action = menu.addAction("粘贴")
        clear_action = menu.addAction("清空")
        action = menu.exec_(self.memo_text.mapToGlobal(pos))
        if action == copy_action:
            self.memo_text.copy()
        elif action == paste_action:
            self.memo_text.paste()
        elif action == clear_action:
            self.memo_text.clear()


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.secondary_window = None
        self.config_combo = None
        # 使用ConfigManager管理配置
        self.config_manager = ConfigManager()
        self.initUI()
        self.load_configurations()
        
        # 初始化时预加载当前配置
        current_config = self.config_manager.get_current_configuration()
        if current_config:
            # 创建二级窗口但不显示
            self.secondary_window = SoftwareLauncher()
            self.secondary_window.config_saved.connect(self.load_configurations)
            self.secondary_window.hide()
            # 预加载配置
            self.load_config_for_secondary_window(current_config)

    def initUI(self):
        self.setWindowTitle('启动')
        self.setGeometry(800, 500, 200, 200)

        layout = QVBoxLayout()

        # 配置选择区域
        config_layout = QHBoxLayout()
        config_label = QLabel('选择配置:')
        self.config_combo = QComboBox(self)
        self.config_combo.currentIndexChanged.connect(self.on_config_changed)
        load_config_btn = QPushButton('加载配置')
        load_config_btn.clicked.connect(self.load_selected_config)
        load_config_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                padding: 4px 8px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        config_layout.addWidget(config_label)
        config_layout.addWidget(self.config_combo)
        config_layout.addWidget(load_config_btn)
        layout.addLayout(config_layout)

        # 一键启动按钮
        one_click_start_btn = QPushButton('一键启动')
        one_click_start_btn.clicked.connect(self.one_click_start)
        # 设置按钮样式
        one_click_start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(one_click_start_btn)

        # 二级界面启动按钮
        open_secondary_btn = QPushButton('设置')
        open_secondary_btn.clicked.connect(self.open_secondary_window)
        # 设置按钮样式
        open_secondary_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(open_secondary_btn)

        self.setLayout(layout)

    def load_configurations(self):
        if self.config_combo:
            # 保存当前选中的配置名称
            current_selected = self.config_combo.currentText() if self.config_combo.currentIndex() >= 0 else ''
            
            self.config_combo.clear()
            
            # 获取所有配置名称
            configurations = self.config_manager.get_configurations()
            logger.info(f"加载配置列表: {configurations}")
            
            # 添加配置到下拉框
            for config_name in configurations:
                self.config_combo.addItem(config_name)
            
            # 设置当前选中的配置
            current_config = self.config_manager.get_current_configuration()
            logger.info(f"当前配置: {current_config}")
            
            index = self.config_combo.findText(current_config)
            if index >= 0:
                self.config_combo.setCurrentIndex(index)
            elif current_selected:
                # 尝试恢复之前选中的配置
                index = self.config_combo.findText(current_selected)
                if index >= 0:
                    self.config_combo.setCurrentIndex(index)
                elif self.config_combo.count() > 0:
                    self.config_combo.setCurrentIndex(0)
            elif self.config_combo.count() > 0:
                self.config_combo.setCurrentIndex(0)

    def on_config_changed(self, index):
        if index >= 0:
            config_name = self.config_combo.currentText()
            logger.info(f"配置已更改为: {config_name}")
            # 更新当前配置
            self.config_manager.set_current_configuration(config_name)

    def load_config_for_secondary_window(self, config_name):
        if not self.secondary_window:
            return
        
        # 确保二级窗口已加载软件列表
        if not hasattr(self.secondary_window, 'software_list') or not self.secondary_window.software_list:
            self.secondary_window.load_config()
        
        # 清空当前启动顺序列表
        self.secondary_window.order_list_widget.clear()
        self.secondary_window.startup_order = []
        
        # 加载该配置的启动顺序
        startup_order = self.config_manager.get_startup_order(config_name)
        self.secondary_window.startup_order = startup_order
        
        # 只添加存在于软件列表中的项目
        for name in startup_order:
            if name in self.secondary_window.software_list:
                self.secondary_window.order_list_widget.addItem(QListWidgetItem(name))
        
        # 更新启动间隔显示
        interval = self.config_manager.get_startup_interval()
        self.secondary_window.startup_interval = interval
        self.secondary_window.interval_input.setText(str(interval))
        
        # 更新备忘录内容
        memo_text = self.config_manager.get_memo_text()
        self.secondary_window.memo_text.setPlainText(memo_text)

    def one_click_start(self):
        if not self.secondary_window:
            self.secondary_window = SoftwareLauncher()
            self.secondary_window.config_saved.connect(self.load_configurations)  # 连接信号
            self.secondary_window.hide()  # 不显示窗口
        config_name = self.config_combo.currentText()
        self.load_config_for_secondary_window(config_name)
        self.secondary_window.one_click_start()

    def open_secondary_window(self):
        if not self.secondary_window:
            self.secondary_window = SoftwareLauncher()
            self.secondary_window.config_saved.connect(self.load_configurations)  # 连接信号
        self.secondary_window.show()
        
    def load_selected_config(self):
        if self.config_combo.currentIndex() >= 0:
            config_name = self.config_combo.currentText()
            logger.info(f"加载配置: {config_name}")
            
            if not self.secondary_window:
                self.secondary_window = SoftwareLauncher()
                self.secondary_window.config_saved.connect(self.load_configurations)  # 连接信号
            
            # 更新当前配置
            self.config_manager.set_current_configuration(config_name)
            
            # 加载配置到二级窗口
            self.load_config_for_secondary_window(config_name)
            QMessageBox.information(self, '提示', f'已加载配置：{config_name}')
            
            # 如果二级窗口已打开，则更新其UI
            if self.secondary_window.isVisible():
                self.secondary_window.order_list_widget.update()


if __name__ == '__main__':
    app = QApplication(sys.argv)
    main_window = MainWindow()
    main_window.show()
    sys.exit(app.exec_())