#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF文件名Excel匹配工具 - Qt版
功能：
1. 扫描DXF文件夹，获取文件名并去掉最后一个字符
2. 在Excel表格中查找匹配的条目并修改为原始文件名
3. 精确匹配，避免部分匹配错误
"""

import sys
import os
import glob
import datetime
import configparser
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QFormLayout, QGroupBox, QLabel, 
                             QLineEdit, QPushButton, QTextEdit, QProgressBar,
                             QFileDialog, QMessageBox, QCheckBox, QComboBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import pandas as pd
import openpyxl
from openpyxl import load_workbook


class ProcessingThread(QThread):
    """处理线程，避免界面卡顿"""
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(bool, str)  # 完成信号(成功/失败, 消息)
    
    def __init__(self, dxf_folder, excel_file, sheet_name, column_name):
        super().__init__()
        self.dxf_folder = dxf_folder
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.column_name = column_name
        self.processed_count = 0
        self.matched_count = 0
        self.log_messages = []
        
    def run(self):
        try:
            # 第一步：扫描DXF文件
            self.progress.emit("正在扫描DXF文件...")
            dxf_files = self.scan_dxf_files()
            
            if not dxf_files:
                self.finished.emit(False, "未找到任何DXF文件")
                return
            
            self.progress.emit(f"找到 {len(dxf_files)} 个DXF文件")
            self.log_messages.append(f"扫描到 {len(dxf_files)} 个DXF文件:")
            for original_name, processed_name in dxf_files:
                self.log_messages.append(f"  {original_name} -> 查找: {processed_name}")
            
            # 第二步：处理Excel文件
            self.progress.emit("正在读取Excel文件...")
            success = self.process_excel_file(dxf_files)
            
            if success:
                # 生成处理日志
                self.create_log_file(dxf_files)
                self.finished.emit(True, f"处理完成！匹配并修改了 {self.matched_count} 个条目")
            else:
                self.finished.emit(False, "Excel文件处理失败")
                
        except Exception as e:
            self.finished.emit(False, f"处理过程中出现错误: {str(e)}")
    
    def scan_dxf_files(self):
        """扫描DXF文件夹，获取文件名并处理"""
        dxf_files = []
        
        # 获取所有DXF文件
        pattern = os.path.join(self.dxf_folder, "*.dxf")
        files = glob.glob(pattern)
        
        for file_path in files:
            # 获取文件名（不包含扩展名）
            filename = os.path.splitext(os.path.basename(file_path))[0]
            
            # 去掉最后一个字符
            if len(filename) > 1:
                processed_name = filename[:-1]
                dxf_files.append((filename, processed_name))
            else:
                self.log_messages.append(f"警告: 文件名太短，跳过: {filename}")
        
        return dxf_files
    
    def process_excel_file(self, dxf_files):
        """处理Excel文件，查找并修改匹配的条目"""
        try:
            # 使用pandas统一处理不同格式的Excel文件
            file_ext = os.path.splitext(self.excel_file)[1].lower()

            # 首先尝试读取所有工作表名称
            try:
                excel_file = pd.ExcelFile(self.excel_file)
                available_sheets = excel_file.sheet_names

                # 检查工作表是否存在
                if self.sheet_name not in available_sheets:
                    self.progress.emit(f"错误: 工作表 '{self.sheet_name}' 不存在")
                    self.log_messages.append(f"可用工作表: {', '.join(available_sheets)}")
                    return False

                # 读取指定工作表
                df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)

            except Exception as e:
                self.progress.emit(f"读取Excel文件失败: {str(e)}")
                self.log_messages.append(f"读取Excel文件失败: {str(e)}")
                return False

            # 检查目标列是否存在
            if self.column_name not in df.columns:
                self.progress.emit(f"错误: 未找到列 '{self.column_name}'")
                self.log_messages.append(f"可用列名: {', '.join(df.columns)}")
                return False

            self.progress.emit(f"在列 '{self.column_name}' 中查找匹配项...")

            # 遍历DXF文件进行匹配和修改
            for original_name, processed_name in dxf_files:
                self.progress.emit(f"查找匹配项: {processed_name}")

                # 在DataFrame中查找精确匹配
                mask = df[self.column_name].astype(str).str.strip() == processed_name
                matched_indices = df[mask].index.tolist()

                if matched_indices:
                    # 修改匹配的条目
                    for idx in matched_indices:
                        old_value = df.loc[idx, self.column_name]
                        df.loc[idx, self.column_name] = original_name
                        self.matched_count += 1
                        self.log_messages.append(f"✓ 第{idx+2}行: '{old_value}' -> '{original_name}'")
                else:
                    self.log_messages.append(f"✗ 未找到匹配项: {processed_name}")

                self.processed_count += 1

            # 保存修改后的Excel文件
            self.progress.emit("正在保存Excel文件...")

            # 根据原文件格式选择保存方式
            if file_ext == '.xls':
                # 对于.xls文件，保存为.xlsx格式
                new_excel_file = self.excel_file[:-4] + '_modified.xlsx'
                with pd.ExcelWriter(new_excel_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name=self.sheet_name, index=False)
                self.progress.emit(f"✓ 文件已保存为: {new_excel_file}")
                self.log_messages.append(f"注意: 原.xls文件已转换为.xlsx格式保存")
            else:
                # 直接保存为原格式
                with pd.ExcelWriter(self.excel_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name=self.sheet_name, index=False)
                self.progress.emit(f"✓ 文件已保存: {self.excel_file}")

            return True

        except Exception as e:
            self.progress.emit(f"Excel处理错误: {str(e)}")
            self.log_messages.append(f"Excel处理错误: {str(e)}")
            return False


    
    def create_log_file(self, dxf_files):
        """创建处理日志文件"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            log_filename = f"DXF文件名Excel匹配处理日志_{timestamp}.txt"
            log_path = os.path.join(os.path.dirname(self.excel_file), log_filename)
            
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write("DXF文件名Excel匹配处理日志\n")
                f.write("=" * 50 + "\n")
                f.write(f"处理时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"DXF文件夹: {self.dxf_folder}\n")
                f.write(f"Excel文件: {self.excel_file}\n")
                f.write(f"工作表: {self.sheet_name}\n")
                f.write(f"目标列: {self.column_name}\n")
                f.write(f"处理文件数: {self.processed_count}\n")
                f.write(f"匹配修改数: {self.matched_count}\n")
                f.write("-" * 50 + "\n\n")
                
                f.write("详细处理记录:\n")
                for message in self.log_messages:
                    f.write(f"{message}\n")
            
            self.progress.emit(f"✓ 处理日志已保存: {log_filename}")
            
        except Exception as e:
            self.progress.emit(f"保存日志失败: {str(e)}")


class DXFExcelMatcherApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.processing_thread = None
        self.config = configparser.ConfigParser()
        self.config_file = "dxf_excel_matcher_config.ini"
        self.load_config()
    
    def init_ui(self):
        self.setWindowTitle("DXF文件名Excel匹配工具 - Qt版")
        self.setGeometry(500, 300, 800, 700)
        self.setMinimumSize(800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 说明信息
        info_group = QGroupBox("功能说明")
        info_layout = QVBoxLayout()
        info_text = QLabel("""
        <b>DXF文件名Excel匹配工具：</b><br>
        1. 扫描指定文件夹下的所有DXF文件<br>
        2. 获取每个DXF文件名（去掉.dxf后缀）并去掉最后一个字符<br>
        3. 在Excel表格中查找与处理后名称完全匹配的条目<br>
        4. 将匹配的条目修改为原始DXF文件名<br>
        <b>示例：</b> B35A.dxf → 查找"B35" → 修改为"B35A"<br>
        <b>支持格式：</b> .xlsx, .xls, .xlsm (旧版.xls会转换为.xlsx保存)<br>
        <b>注意：</b> 使用精确匹配，避免部分匹配错误
        """)
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)
        
        # 文件路径设置区域
        path_group = QGroupBox("文件路径设置")
        path_layout = QFormLayout()
        
        # DXF文件夹路径
        dxf_row = QHBoxLayout()
        self.dxf_path_edit = QLineEdit()
        self.dxf_browse_btn = QPushButton("浏览")
        self.dxf_browse_btn.clicked.connect(self.browse_dxf_folder)
        dxf_row.addWidget(self.dxf_path_edit)
        dxf_row.addWidget(self.dxf_browse_btn)
        path_layout.addRow("DXF文件夹:", dxf_row)
        
        # Excel文件路径
        excel_row = QHBoxLayout()
        self.excel_path_edit = QLineEdit()
        self.excel_browse_btn = QPushButton("浏览")
        self.excel_browse_btn.clicked.connect(self.browse_excel_file)
        excel_row.addWidget(self.excel_path_edit)
        excel_row.addWidget(self.excel_browse_btn)
        path_layout.addRow("Excel文件:", excel_row)
        
        path_group.setLayout(path_layout)
        main_layout.addWidget(path_group)
        
        # Excel设置区域
        excel_group = QGroupBox("Excel设置")
        excel_layout = QFormLayout()
        
        # 工作表名称
        self.sheet_name_edit = QLineEdit()
        self.sheet_name_edit.setText("Sheet1")
        excel_layout.addRow("工作表名称:", self.sheet_name_edit)
        
        # 目标列名
        self.column_name_edit = QLineEdit()
        self.column_name_edit.setText("零件编号")
        excel_layout.addRow("目标列名:", self.column_name_edit)
        
        excel_group.setLayout(excel_layout)
        main_layout.addWidget(excel_group)
        
        # 处理按钮
        button_layout = QHBoxLayout()
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; font-weight: bold; }")
        button_layout.addWidget(self.process_btn)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 日志显示区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)
        
        # 状态栏
        self.statusBar().showMessage("就绪")

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')

                # 加载路径设置
                if 'paths' in self.config:
                    self.dxf_path_edit.setText(self.config.get('paths', 'dxf_folder', fallback=''))
                    self.excel_path_edit.setText(self.config.get('paths', 'excel_file', fallback=''))

                # 加载Excel设置
                if 'excel' in self.config:
                    self.sheet_name_edit.setText(self.config.get('excel', 'sheet_name', fallback='Sheet1'))
                    self.column_name_edit.setText(self.config.get('excel', 'column_name', fallback='零件编号'))

        except Exception as e:
            self.log_text.append(f"加载配置文件失败: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            if 'paths' not in self.config:
                self.config.add_section('paths')
            if 'excel' not in self.config:
                self.config.add_section('excel')

            # 保存路径设置
            self.config.set('paths', 'dxf_folder', self.dxf_path_edit.text())
            self.config.set('paths', 'excel_file', self.excel_path_edit.text())

            # 保存Excel设置
            self.config.set('excel', 'sheet_name', self.sheet_name_edit.text())
            self.config.set('excel', 'column_name', self.column_name_edit.text())

            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)

        except Exception as e:
            self.log_text.append(f"保存配置文件失败: {str(e)}")

    def browse_dxf_folder(self):
        """浏览DXF文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择DXF文件夹")
        if folder:
            self.dxf_path_edit.setText(folder)
            # 检查文件夹中是否有DXF文件
            dxf_files = glob.glob(os.path.join(folder, "*.dxf"))
            if dxf_files:
                self.log_text.append(f"✓ DXF文件夹设置成功，找到 {len(dxf_files)} 个DXF文件")
            else:
                QMessageBox.warning(self, "警告", "选择的文件夹中没有找到DXF文件")

    def browse_excel_file(self):
        """浏览Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "",
            "Excel文件 (*.xlsx *.xls *.xlsm);;新版Excel (*.xlsx *.xlsm);;旧版Excel (*.xls);;所有文件 (*)"
        )
        if file_path:
            self.excel_path_edit.setText(file_path)
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext == '.xls':
                self.log_text.append(f"✓ Excel文件设置成功: {os.path.basename(file_path)} (旧版格式，将转换为.xlsx保存)")
            else:
                self.log_text.append(f"✓ Excel文件设置成功: {os.path.basename(file_path)}")

    def validate_inputs(self):
        """验证输入参数"""
        dxf_folder = self.dxf_path_edit.text().strip()
        excel_file = self.excel_path_edit.text().strip()
        sheet_name = self.sheet_name_edit.text().strip()
        column_name = self.column_name_edit.text().strip()

        if not dxf_folder:
            QMessageBox.critical(self, "错误", "请选择DXF文件夹")
            return False

        if not os.path.exists(dxf_folder):
            QMessageBox.critical(self, "错误", "DXF文件夹不存在")
            return False

        if not excel_file:
            QMessageBox.critical(self, "错误", "请选择Excel文件")
            return False

        if not os.path.exists(excel_file):
            QMessageBox.critical(self, "错误", "Excel文件不存在")
            return False

        if not sheet_name:
            QMessageBox.critical(self, "错误", "请输入工作表名称")
            return False

        if not column_name:
            QMessageBox.critical(self, "错误", "请输入目标列名")
            return False

        # 检查DXF文件夹中是否有DXF文件
        dxf_files = glob.glob(os.path.join(dxf_folder, "*.dxf"))
        if not dxf_files:
            QMessageBox.critical(self, "错误", "DXF文件夹中没有找到DXF文件")
            return False

        return True

    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return

        # 显示确认对话框
        dxf_folder = self.dxf_path_edit.text().strip()
        excel_file = self.excel_path_edit.text().strip()
        sheet_name = self.sheet_name_edit.text().strip()
        column_name = self.column_name_edit.text().strip()

        dxf_count = len(glob.glob(os.path.join(dxf_folder, "*.dxf")))

        reply = QMessageBox.question(
            self, "确认处理",
            f"即将处理以下内容：\n\n"
            f"DXF文件夹: {dxf_folder}\n"
            f"DXF文件数量: {dxf_count}\n"
            f"Excel文件: {os.path.basename(excel_file)}\n"
            f"工作表: {sheet_name}\n"
            f"目标列: {column_name}\n\n"
            f"确认开始处理吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 保存配置
        self.save_config()

        # 清空日志
        self.log_text.clear()

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 禁用按钮
        self.process_btn.setEnabled(False)
        self.process_btn.setText("处理中...")

        # 启动处理线程
        self.processing_thread = ProcessingThread(dxf_folder, excel_file, sheet_name, column_name)
        self.processing_thread.progress.connect(self.on_progress)
        self.processing_thread.finished.connect(self.on_finished)
        self.processing_thread.start()

        self.statusBar().showMessage("正在处理...")

    def on_progress(self, message):
        """更新进度信息"""
        self.log_text.append(message)
        self.statusBar().showMessage(message)
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def on_finished(self, success, message):
        """处理完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮
        self.process_btn.setEnabled(True)
        self.process_btn.setText("开始处理")

        # 显示结果
        if success:
            self.log_text.append(f"✓ {message}")
            self.statusBar().showMessage("处理完成", 3000)
            QMessageBox.information(self, "处理完成", message)
        else:
            self.log_text.append(f"✗ {message}")
            self.statusBar().showMessage("处理失败", 3000)
            QMessageBox.critical(self, "处理失败", message)

    def closeEvent(self, event):
        """关闭事件"""
        # 保存配置
        self.save_config()

        # 如果有处理线程在运行，询问是否确认关闭
        if self.processing_thread and self.processing_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认关闭",
                "处理正在进行中，确认关闭程序吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.processing_thread.terminate()
                self.processing_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    app = QApplication(sys.argv)

    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = DXFExcelMatcherApp()
    window.show()

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
