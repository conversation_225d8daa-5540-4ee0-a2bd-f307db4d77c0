#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理失败问题诊断和修复工具
系统性诊断批量零件编号处理工具中的Excel处理问题
"""

import os
import sys
import pandas as pd
import shutil
import glob
import traceback
from datetime import datetime
import openpyxl
from openpyxl import load_workbook
import stat

# 可选依赖
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

class ExcelFailureDiagnostic:
    """Excel处理失败诊断类"""

    def __init__(self):
        self.diagnostic_results = {}
        self.error_log = []
        self.fix_recommendations = []

    def run_comprehensive_diagnostic(self, excel_file_path=None, sheet_name=None,
                                   column_name=None, part_names=None):
        """运行全面的Excel处理诊断"""
        print("=" * 80)
        print("Excel文件处理失败问题 - 全面诊断工具")
        print("=" * 80)

        # 如果没有提供参数，创建测试环境
        if not excel_file_path:
            excel_file_path, sheet_name, column_name, part_names = self.create_test_environment()

        print(f"\n诊断目标:")
        print(f"  Excel文件: {excel_file_path}")
        print(f"  工作表: {sheet_name}")
        print(f"  目标列: {column_name}")
        print(f"  零件名称: {part_names}")

        # 执行分步诊断
        self.step1_file_existence_check(excel_file_path)
        self.step2_file_permissions_check(excel_file_path)
        self.step3_file_access_check(excel_file_path)
        self.step4_excel_structure_check(excel_file_path, sheet_name, column_name)
        self.step5_data_format_check(excel_file_path, sheet_name, column_name)
        self.step6_matching_logic_check(excel_file_path, sheet_name, column_name, part_names)
        self.step7_write_operations_check(excel_file_path, sheet_name, column_name)
        self.step8_full_process_simulation(excel_file_path, sheet_name, column_name, part_names)

        # 生成诊断报告
        self.generate_diagnostic_report()

        # 提供修复方案
        self.provide_fix_solutions()

        return self.diagnostic_results

    def create_test_environment(self):
        """创建测试环境"""
        print(f"\n{'='*20} 创建测试环境 {'='*20}")

        test_dir = os.path.join(os.path.dirname(__file__), "test_data", "excel_failure_test")
        os.makedirs(test_dir, exist_ok=True)

        # 创建测试Excel文件
        data = {
            '序号': [1, 2, 3, 4, 5],
            '零件编号': ['LP20', '2-C38', 'D12', 'A-E56', 'F23'],
            '零件名称': ['支撑板', '连接件', '固定块', '支架', '垫片'],
            '材质': ['Q235', 'Q345', 'Q235', 'Q345', 'Q235'],
            '数量': [2, 4, 1, 3, 8],
            '备注': ['', '', '', '', '']
        }

        df = pd.DataFrame(data)
        excel_file = os.path.join(test_dir, "失败诊断测试.xlsx")

        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Sheet1', index=False)

        print(f"✓ 创建测试Excel文件: {excel_file}")

        return excel_file, 'Sheet1', '零件编号', ['LP20A', 'C38B', 'D12C', 'E56D', 'F23E']

    def step1_file_existence_check(self, excel_file_path):
        """步骤1：检查文件存在性"""
        print(f"\n{'='*20} 步骤1：文件存在性检查 {'='*20}")

        try:
            if os.path.exists(excel_file_path):
                print(f"✓ 文件存在: {excel_file_path}")

                # 检查文件大小
                file_size = os.path.getsize(excel_file_path)
                print(f"✓ 文件大小: {file_size} 字节")

                if file_size == 0:
                    self.error_log.append("文件大小为0，可能是空文件")
                    self.fix_recommendations.append("检查文件是否正确创建，重新生成Excel文件")

                # 检查文件扩展名
                file_ext = os.path.splitext(excel_file_path)[1].lower()
                if file_ext not in ['.xlsx', '.xls', '.xlsm']:
                    self.error_log.append(f"文件扩展名不正确: {file_ext}")
                    self.fix_recommendations.append("确保文件扩展名为.xlsx、.xls或.xlsm")
                else:
                    print(f"✓ 文件扩展名正确: {file_ext}")

                self.diagnostic_results['file_exists'] = True

            else:
                print(f"✗ 文件不存在: {excel_file_path}")
                self.error_log.append(f"Excel文件不存在: {excel_file_path}")
                self.fix_recommendations.append("检查文件路径是否正确，确保文件存在")
                self.diagnostic_results['file_exists'] = False

        except Exception as e:
            print(f"✗ 文件存在性检查失败: {str(e)}")
            self.error_log.append(f"文件存在性检查异常: {str(e)}")
            self.diagnostic_results['file_exists'] = False

    def step2_file_permissions_check(self, excel_file_path):
        """步骤2：检查文件权限"""
        print(f"\n{'='*20} 步骤2：文件权限检查 {'='*20}")

        if not self.diagnostic_results.get('file_exists', False):
            print("跳过权限检查（文件不存在）")
            return

        try:
            # 检查读权限
            if os.access(excel_file_path, os.R_OK):
                print(f"✓ 文件可读")
                self.diagnostic_results['file_readable'] = True
            else:
                print(f"✗ 文件不可读")
                self.error_log.append("文件没有读权限")
                self.fix_recommendations.append("检查文件权限，确保程序有读取权限")
                self.diagnostic_results['file_readable'] = False

            # 检查写权限
            if os.access(excel_file_path, os.W_OK):
                print(f"✓ 文件可写")
                self.diagnostic_results['file_writable'] = True
            else:
                print(f"✗ 文件不可写")
                self.error_log.append("文件没有写权限")
                self.fix_recommendations.append("检查文件权限，确保程序有写入权限")
                self.diagnostic_results['file_writable'] = False

            # 检查文件属性
            file_stat = os.stat(excel_file_path)
            file_mode = stat.filemode(file_stat.st_mode)
            print(f"✓ 文件权限: {file_mode}")

            # 检查文件是否被锁定
            if os.name == 'nt':  # Windows系统
                try:
                    # 尝试以独占模式打开文件
                    with open(excel_file_path, 'r+b') as f:
                        pass
                    print(f"✓ 文件未被锁定")
                    self.diagnostic_results['file_locked'] = False
                except IOError:
                    print(f"✗ 文件可能被其他程序锁定")
                    self.error_log.append("文件被其他程序锁定（可能被Excel打开）")
                    self.fix_recommendations.append("关闭所有打开该文件的程序（如Excel）")
                    self.diagnostic_results['file_locked'] = True

        except Exception as e:
            print(f"✗ 权限检查失败: {str(e)}")
            self.error_log.append(f"权限检查异常: {str(e)}")

    def step3_file_access_check(self, excel_file_path):
        """步骤3：检查文件访问状态"""
        print(f"\n{'='*20} 步骤3：文件访问状态检查 {'='*20}")

        if not self.diagnostic_results.get('file_exists', False):
            print("跳过访问检查（文件不存在）")
            return

        try:
            # 检查文件是否被进程占用
            if PSUTIL_AVAILABLE:
                file_processes = []
                for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                    try:
                        if proc.info['open_files']:
                            for file_info in proc.info['open_files']:
                                if file_info.path == excel_file_path:
                                    file_processes.append({
                                        'pid': proc.info['pid'],
                                        'name': proc.info['name']
                                    })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                if file_processes:
                    print(f"✗ 文件被以下进程占用:")
                    for proc in file_processes:
                        print(f"    PID: {proc['pid']}, 进程: {proc['name']}")
                    self.error_log.append(f"文件被 {len(file_processes)} 个进程占用")
                    self.fix_recommendations.append("关闭占用文件的进程，特别是Excel程序")
                    self.diagnostic_results['file_in_use'] = True
                else:
                    print(f"✓ 文件未被其他进程占用")
                    self.diagnostic_results['file_in_use'] = False
            else:
                print(f"⚠ 无法检查进程占用（psutil未安装）")
                self.diagnostic_results['file_in_use'] = False

            # 检查磁盘空间
            disk_usage = shutil.disk_usage(os.path.dirname(excel_file_path))
            free_space_mb = disk_usage.free / (1024 * 1024)
            print(f"✓ 磁盘剩余空间: {free_space_mb:.1f} MB")

            if free_space_mb < 100:  # 少于100MB
                self.error_log.append(f"磁盘空间不足: {free_space_mb:.1f} MB")
                self.fix_recommendations.append("清理磁盘空间，确保有足够的空间进行文件操作")
                self.diagnostic_results['disk_space_ok'] = False
            else:
                self.diagnostic_results['disk_space_ok'] = True

        except Exception as e:
            print(f"✗ 文件访问检查失败: {str(e)}")
            self.error_log.append(f"文件访问检查异常: {str(e)}")

    def step4_excel_structure_check(self, excel_file_path, sheet_name, column_name):
        """步骤4：检查Excel结构"""
        print(f"\n{'='*20} 步骤4：Excel结构检查 {'='*20}")

        if not self.diagnostic_results.get('file_readable', False):
            print("跳过结构检查（文件不可读）")
            return

        try:
            # 使用pandas读取Excel文件
            print(f"测试pandas读取...")
            try:
                excel_file_obj = pd.ExcelFile(excel_file_path)
                available_sheets = excel_file_obj.sheet_names
                print(f"✓ pandas读取成功")
                print(f"✓ 可用工作表: {available_sheets}")

                # 检查目标工作表是否存在
                if sheet_name in available_sheets:
                    print(f"✓ 目标工作表存在: {sheet_name}")
                    self.diagnostic_results['sheet_exists'] = True
                else:
                    print(f"✗ 目标工作表不存在: {sheet_name}")
                    self.error_log.append(f"工作表 '{sheet_name}' 不存在")
                    self.fix_recommendations.append(f"检查工作表名称，可用工作表: {available_sheets}")
                    self.diagnostic_results['sheet_exists'] = False
                    return

                # 读取工作表数据
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                print(f"✓ 工作表数据读取成功")
                print(f"✓ 数据行数: {len(df)}")
                print(f"✓ 数据列数: {len(df.columns)}")
                print(f"✓ 列名: {list(df.columns)}")

                # 检查目标列是否存在
                if column_name in df.columns:
                    print(f"✓ 目标列存在: {column_name}")
                    self.diagnostic_results['column_exists'] = True

                    # 检查列数据
                    column_data = df[column_name]
                    print(f"✓ 目标列数据: {list(column_data)}")
                    print(f"✓ 目标列数据类型: {column_data.dtype}")

                    # 检查空值
                    null_count = column_data.isnull().sum()
                    if null_count > 0:
                        print(f"⚠ 目标列包含 {null_count} 个空值")
                        self.fix_recommendations.append(f"目标列包含空值，可能影响匹配")

                else:
                    print(f"✗ 目标列不存在: {column_name}")
                    self.error_log.append(f"列 '{column_name}' 不存在")
                    self.fix_recommendations.append(f"检查列名，可用列名: {list(df.columns)}")
                    self.diagnostic_results['column_exists'] = False

            except Exception as e:
                print(f"✗ pandas读取失败: {str(e)}")
                self.error_log.append(f"pandas读取Excel失败: {str(e)}")
                self.diagnostic_results['pandas_read_ok'] = False

            # 使用openpyxl读取Excel文件
            print(f"\n测试openpyxl读取...")
            try:
                wb = load_workbook(excel_file_path)
                print(f"✓ openpyxl读取成功")
                print(f"✓ 工作表列表: {wb.sheetnames}")

                if sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    print(f"✓ 工作表访问成功: {sheet_name}")
                    print(f"✓ 数据范围: {ws.dimensions}")
                    self.diagnostic_results['openpyxl_read_ok'] = True
                else:
                    print(f"✗ openpyxl无法访问工作表: {sheet_name}")
                    self.diagnostic_results['openpyxl_read_ok'] = False

            except Exception as e:
                print(f"✗ openpyxl读取失败: {str(e)}")
                self.error_log.append(f"openpyxl读取Excel失败: {str(e)}")
                self.diagnostic_results['openpyxl_read_ok'] = False

        except Exception as e:
            print(f"✗ Excel结构检查失败: {str(e)}")
            self.error_log.append(f"Excel结构检查异常: {str(e)}")
            traceback.print_exc()

    def step5_data_format_check(self, excel_file_path, sheet_name, column_name):
        """步骤5：检查数据格式"""
        print(f"\n{'='*20} 步骤5：数据格式检查 {'='*20}")

        if not self.diagnostic_results.get('column_exists', False):
            print("跳过数据格式检查（目标列不存在）")
            return

        try:
            df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
            column_data = df[column_name]

            print(f"数据格式分析:")
            print(f"  数据类型: {column_data.dtype}")
            print(f"  总行数: {len(column_data)}")
            print(f"  非空行数: {column_data.notna().sum()}")
            print(f"  空值行数: {column_data.isna().sum()}")

            # 检查数据类型
            if column_data.dtype == 'object':
                print(f"✓ 数据类型为文本（object）")
                self.diagnostic_results['data_type_ok'] = True
            else:
                print(f"⚠ 数据类型为 {column_data.dtype}，可能影响字符串匹配")
                self.fix_recommendations.append(f"目标列数据类型为 {column_data.dtype}，建议转换为文本类型")
                self.diagnostic_results['data_type_ok'] = False

            # 检查数据内容
            unique_values = column_data.dropna().unique()
            print(f"✓ 唯一值数量: {len(unique_values)}")
            print(f"✓ 数据样本: {list(unique_values[:10])}")

            # 检查特殊字符
            special_chars_found = []
            for value in column_data.dropna():
                str_value = str(value)
                if any(char in str_value for char in ['\n', '\r', '\t']):
                    special_chars_found.append(str_value)

            if special_chars_found:
                print(f"⚠ 发现包含特殊字符的数据: {special_chars_found[:5]}")
                self.fix_recommendations.append("数据中包含换行符或制表符，可能影响匹配")
            else:
                print(f"✓ 数据中无特殊字符")

            # 检查数据长度
            max_length = column_data.dropna().astype(str).str.len().max()
            min_length = column_data.dropna().astype(str).str.len().min()
            print(f"✓ 数据长度范围: {min_length} - {max_length}")

            self.diagnostic_results['data_format_ok'] = True

        except Exception as e:
            print(f"✗ 数据格式检查失败: {str(e)}")
            self.error_log.append(f"数据格式检查异常: {str(e)}")
            self.diagnostic_results['data_format_ok'] = False

    def step6_matching_logic_check(self, excel_file_path, sheet_name, column_name, part_names):
        """步骤6：检查匹配逻辑"""
        print(f"\n{'='*20} 步骤6：匹配逻辑检查 {'='*20}")

        if not self.diagnostic_results.get('data_format_ok', False):
            print("跳过匹配逻辑检查（数据格式问题）")
            return

        try:
            # 导入处理类
            from 批量零件编号处理工具_Qt import ProcessingThread

            # 创建临时处理实例
            temp_dir = os.path.dirname(excel_file_path)
            processor = ProcessingThread(part_names, temp_dir, temp_dir, sheet_name, column_name)

            # 解析零件名称
            processed_parts = processor.parse_part_names()
            print(f"✓ 零件名称解析成功: {len(processed_parts)} 个")

            # 读取Excel数据
            df = pd.read_excel(excel_file_path, sheet_name=sheet_name)

            # 测试匹配逻辑
            print(f"\n匹配测试结果:")
            total_matches = 0

            for part_info in processed_parts:
                match_target = part_info['match_target']
                original_name = part_info['original_name']

                # 使用灵活匹配逻辑
                matched_indices = processor.find_flexible_matches(df, match_target)
                matched_values = [df.loc[idx, column_name] for idx in matched_indices]

                if matched_indices:
                    print(f"  ✓ {original_name} (目标: {match_target}) → 匹配: {matched_values}")
                    total_matches += len(matched_indices)
                else:
                    print(f"  ✗ {original_name} (目标: {match_target}) → 无匹配")

            print(f"\n匹配统计:")
            print(f"  总零件数: {len(processed_parts)}")
            print(f"  总匹配数: {total_matches}")
            print(f"  匹配率: {total_matches/len(processed_parts)*100:.1f}%")

            if total_matches > 0:
                print(f"✓ 匹配逻辑工作正常")
                self.diagnostic_results['matching_logic_ok'] = True
            else:
                print(f"✗ 匹配逻辑无匹配结果")
                self.error_log.append("匹配逻辑没有找到任何匹配项")
                self.fix_recommendations.append("检查零件编号格式是否与Excel中的数据匹配")
                self.diagnostic_results['matching_logic_ok'] = False

        except Exception as e:
            print(f"✗ 匹配逻辑检查失败: {str(e)}")
            self.error_log.append(f"匹配逻辑检查异常: {str(e)}")
            self.diagnostic_results['matching_logic_ok'] = False
            traceback.print_exc()

    def step7_write_operations_check(self, excel_file_path, sheet_name, column_name):
        """步骤7：检查写入操作"""
        print(f"\n{'='*20} 步骤7：写入操作检查 {'='*20}")

        if not self.diagnostic_results.get('file_writable', False):
            print("跳过写入检查（文件不可写）")
            return

        try:
            # 创建测试副本
            test_file = excel_file_path.replace('.xlsx', '_写入测试.xlsx')
            shutil.copy2(excel_file_path, test_file)
            print(f"✓ 创建测试副本: {os.path.basename(test_file)}")

            # 测试pandas写入
            print(f"\n测试pandas写入...")
            try:
                df = pd.read_excel(test_file, sheet_name=sheet_name)

                # 修改一个测试值
                if len(df) > 0 and column_name in df.columns:
                    original_value = df.loc[0, column_name]
                    test_value = f"{original_value}_测试"
                    df.loc[0, column_name] = test_value

                    # 写入文件
                    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                        df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # 验证写入
                    df_verify = pd.read_excel(test_file, sheet_name=sheet_name)
                    if df_verify.loc[0, column_name] == test_value:
                        print(f"✓ pandas写入验证成功")
                        self.diagnostic_results['pandas_write_ok'] = True
                    else:
                        print(f"✗ pandas写入验证失败")
                        self.diagnostic_results['pandas_write_ok'] = False
                else:
                    print(f"⚠ 无法进行写入测试（数据为空）")
                    self.diagnostic_results['pandas_write_ok'] = False

            except Exception as e:
                print(f"✗ pandas写入测试失败: {str(e)}")
                self.error_log.append(f"pandas写入失败: {str(e)}")
                self.diagnostic_results['pandas_write_ok'] = False

            # 测试openpyxl写入
            print(f"\n测试openpyxl写入...")
            try:
                wb = load_workbook(test_file)
                ws = wb[sheet_name]

                # 修改一个测试值
                if ws.max_row > 1:
                    original_value = ws.cell(row=2, column=1).value
                    test_value = f"{original_value}_openpyxl测试"
                    ws.cell(row=2, column=1, value=test_value)

                    # 保存文件
                    wb.save(test_file)

                    # 验证写入
                    wb_verify = load_workbook(test_file)
                    ws_verify = wb_verify[sheet_name]
                    if ws_verify.cell(row=2, column=1).value == test_value:
                        print(f"✓ openpyxl写入验证成功")
                        self.diagnostic_results['openpyxl_write_ok'] = True
                    else:
                        print(f"✗ openpyxl写入验证失败")
                        self.diagnostic_results['openpyxl_write_ok'] = False
                else:
                    print(f"⚠ 无法进行openpyxl写入测试（数据为空）")
                    self.diagnostic_results['openpyxl_write_ok'] = False

            except Exception as e:
                print(f"✗ openpyxl写入测试失败: {str(e)}")
                self.error_log.append(f"openpyxl写入失败: {str(e)}")
                self.diagnostic_results['openpyxl_write_ok'] = False

            # 清理测试文件
            try:
                os.remove(test_file)
                print(f"✓ 清理测试文件")
            except:
                pass

        except Exception as e:
            print(f"✗ 写入操作检查失败: {str(e)}")
            self.error_log.append(f"写入操作检查异常: {str(e)}")
            traceback.print_exc()

    def step8_full_process_simulation(self, excel_file_path, sheet_name, column_name, part_names):
        """步骤8：完整流程模拟"""
        print(f"\n{'='*20} 步骤8：完整流程模拟 {'='*20}")

        # 检查前置条件
        required_checks = ['file_exists', 'file_readable', 'file_writable', 'sheet_exists',
                          'column_exists', 'matching_logic_ok']

        missing_requirements = [check for check in required_checks
                              if not self.diagnostic_results.get(check, False)]

        if missing_requirements:
            print(f"跳过完整流程模拟，缺少前置条件: {missing_requirements}")
            return

        try:
            # 导入处理类
            from 批量零件编号处理工具_Qt import ProcessingThread

            # 创建测试副本
            test_file = excel_file_path.replace('.xlsx', '_完整测试.xlsx')
            shutil.copy2(excel_file_path, test_file)

            test_dir = os.path.dirname(test_file)
            output_dir = os.path.join(test_dir, "simulation_output")
            os.makedirs(output_dir, exist_ok=True)

            print(f"✓ 创建完整测试环境")
            print(f"  测试文件: {os.path.basename(test_file)}")
            print(f"  输出目录: {output_dir}")

            # 创建处理实例
            processor = ProcessingThread(part_names, test_dir, output_dir, sheet_name, column_name)

            # 记录处理前状态
            df_before = pd.read_excel(test_file, sheet_name=sheet_name)
            original_data = list(df_before[column_name])
            print(f"✓ 处理前数据: {original_data}")

            # 执行完整处理流程
            print(f"\n执行完整处理流程...")

            # 解析零件名称
            processed_parts = processor.parse_part_names()
            print(f"  零件解析: {len(processed_parts)} 个")

            # 处理Excel文件
            excel_success = processor.process_excel_files(processed_parts)
            print(f"  Excel处理: {'成功' if excel_success else '失败'}")

            if excel_success:
                # 检查处理结果
                df_after = pd.read_excel(test_file, sheet_name=sheet_name)
                updated_data = list(df_after[column_name])
                print(f"  处理后数据: {updated_data}")

                # 统计变化
                changes = sum(1 for a, b in zip(original_data, updated_data) if a != b)
                print(f"  数据变化数: {changes}")

                # 检查备份文件
                backup_file = test_file + '.backup'
                if os.path.exists(backup_file):
                    print(f"  ✓ 备份文件已创建")
                else:
                    print(f"  ✗ 备份文件未创建")

                # 检查统计信息
                print(f"  处理统计:")
                print(f"    匹配数: {processor.stats['excel_matched']}")
                print(f"    更新数: {processor.stats['excel_updated']}")
                print(f"    错误数: {len(processor.stats['errors'])}")

                if processor.stats['excel_updated'] > 0:
                    print(f"✓ 完整流程模拟成功")
                    self.diagnostic_results['full_process_ok'] = True
                else:
                    print(f"✗ 完整流程模拟失败（无更新）")
                    self.diagnostic_results['full_process_ok'] = False
                    self.error_log.append("完整流程执行但没有更新任何数据")
            else:
                print(f"✗ 完整流程模拟失败")
                self.diagnostic_results['full_process_ok'] = False
                self.error_log.append("完整流程执行失败")

                # 记录错误信息
                if processor.stats['errors']:
                    for error in processor.stats['errors']:
                        self.error_log.append(f"流程错误: {error}")

            # 清理测试文件
            try:
                os.remove(test_file)
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                if os.path.exists(output_dir):
                    shutil.rmtree(output_dir)
                print(f"✓ 清理测试环境")
            except:
                pass

        except Exception as e:
            print(f"✗ 完整流程模拟失败: {str(e)}")
            self.error_log.append(f"完整流程模拟异常: {str(e)}")
            self.diagnostic_results['full_process_ok'] = False
            traceback.print_exc()

    def generate_diagnostic_report(self):
        """生成诊断报告"""
        print(f"\n{'='*20} 诊断报告 {'='*20}")

        # 统计结果
        total_checks = len(self.diagnostic_results)
        passed_checks = sum(1 for result in self.diagnostic_results.values() if result)
        failed_checks = total_checks - passed_checks

        print(f"诊断统计:")
        print(f"  总检查项: {total_checks}")
        print(f"  通过项: {passed_checks}")
        print(f"  失败项: {failed_checks}")
        print(f"  成功率: {passed_checks/total_checks*100:.1f}%")

        print(f"\n详细结果:")
        check_descriptions = {
            'file_exists': '文件存在性',
            'file_readable': '文件可读性',
            'file_writable': '文件可写性',
            'file_locked': '文件锁定状态',
            'file_in_use': '文件占用状态',
            'disk_space_ok': '磁盘空间',
            'sheet_exists': '工作表存在性',
            'column_exists': '目标列存在性',
            'pandas_read_ok': 'pandas读取',
            'openpyxl_read_ok': 'openpyxl读取',
            'data_type_ok': '数据类型',
            'data_format_ok': '数据格式',
            'matching_logic_ok': '匹配逻辑',
            'pandas_write_ok': 'pandas写入',
            'openpyxl_write_ok': 'openpyxl写入',
            'full_process_ok': '完整流程'
        }

        for key, description in check_descriptions.items():
            if key in self.diagnostic_results:
                status = "✓ 正常" if self.diagnostic_results[key] else "✗ 异常"
                print(f"  {description}: {status}")

        # 显示错误信息
        if self.error_log:
            print(f"\n发现的问题:")
            for i, error in enumerate(self.error_log, 1):
                print(f"  {i}. {error}")

    def provide_fix_solutions(self):
        """提供修复方案"""
        print(f"\n{'='*20} 修复方案 {'='*20}")

        if not self.error_log:
            print(f"✅ 未发现问题，Excel处理功能应该正常工作")
            return

        print(f"根据诊断结果，提供以下修复建议:")

        # 按优先级排序修复建议
        priority_fixes = []

        # 高优先级问题
        if not self.diagnostic_results.get('file_exists', True):
            priority_fixes.append("🔴 高优先级：文件不存在 - 检查文件路径，确保文件存在")

        if not self.diagnostic_results.get('file_readable', True):
            priority_fixes.append("🔴 高优先级：文件不可读 - 检查文件权限，确保程序有读取权限")

        if not self.diagnostic_results.get('file_writable', True):
            priority_fixes.append("🔴 高优先级：文件不可写 - 检查文件权限，确保程序有写入权限")

        if self.diagnostic_results.get('file_locked', False):
            priority_fixes.append("🔴 高优先级：文件被锁定 - 关闭所有打开该文件的程序（如Excel）")

        if not self.diagnostic_results.get('sheet_exists', True):
            priority_fixes.append("🟡 中优先级：工作表不存在 - 检查工作表名称是否正确")

        if not self.diagnostic_results.get('column_exists', True):
            priority_fixes.append("🟡 中优先级：目标列不存在 - 检查列名是否正确")

        if not self.diagnostic_results.get('matching_logic_ok', True):
            priority_fixes.append("🟡 中优先级：匹配逻辑无结果 - 检查零件编号格式是否与Excel数据匹配")

        # 低优先级问题
        if not self.diagnostic_results.get('data_type_ok', True):
            priority_fixes.append("🟢 低优先级：数据类型问题 - 考虑将目标列转换为文本类型")

        # 显示修复建议
        if priority_fixes:
            for i, fix in enumerate(priority_fixes, 1):
                print(f"  {i}. {fix}")

        # 显示通用修复建议
        print(f"\n通用修复建议:")
        for i, recommendation in enumerate(self.fix_recommendations, 1):
            print(f"  {i}. {recommendation}")

        # 提供测试验证步骤
        print(f"\n验证修复效果的步骤:")
        print(f"  1. 根据上述建议修复问题")
        print(f"  2. 重新运行诊断工具验证修复效果")
        print(f"  3. 使用小规模测试数据验证功能")
        print(f"  4. 检查处理日志确认无错误")
        print(f"  5. 验证备份文件和输出文件的正确性")


def main():
    """主函数"""
    diagnostic = ExcelFailureDiagnostic()

    # 可以在这里指定具体的文件进行诊断
    # excel_file_path = "path/to/your/excel/file.xlsx"
    # sheet_name = "Sheet1"
    # column_name = "零件编号"
    # part_names = ["LP20A", "C38B", "D12C"]

    # 或者使用默认测试环境
    results = diagnostic.run_comprehensive_diagnostic()

    return results


if __name__ == '__main__':
    try:
        results = main()
        print(f"\n🎯 诊断完成！")

        # 判断整体状态
        critical_checks = ['file_exists', 'file_readable', 'file_writable', 'sheet_exists', 'column_exists']
        critical_passed = all(results.get(check, False) for check in critical_checks)

        if critical_passed:
            print(f"✅ 关键检查项全部通过，Excel处理功能应该可以正常工作")
        else:
            print(f"❌ 发现关键问题，需要修复后才能正常使用Excel处理功能")

    except Exception as e:
        print(f"\n💥 诊断过程中出现异常: {str(e)}")
        traceback.print_exc()