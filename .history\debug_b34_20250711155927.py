#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试B34.dxf文件检测问题
"""

import sys
import os

# 添加py源码目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'py源码'))

try:
    from 检查dxf图形是否有多个图形 import DXFCheckThread
    import ezdxf
    print("✓ 成功导入所需模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)

def analyze_b34_file():
    """分析B34.dxf文件"""
    file_path = r"C:\Users\<USER>\Desktop\250540科索沃仓库排版\新建文件夹\NC_dxf\B34.dxf"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        return
    
    print(f"分析文件: {file_path}")
    
    try:
        # 读取DXF文件
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        print(f"\n=== 文件基本信息 ===")
        print(f"DXF版本: {doc.dxfversion}")
        
        # 获取所有实体
        all_entities = list(msp)
        print(f"总实体数量: {len(all_entities)}")
        
        # 分类统计所有实体
        all_entity_types = {}
        for entity in all_entities:
            entity_type = entity.dxftype()
            all_entity_types[entity_type] = all_entity_types.get(entity_type, 0) + 1
        
        print(f"\n=== 所有实体类型统计 ===")
        for entity_type, count in sorted(all_entity_types.items()):
            print(f"  {entity_type}: {count}个")
        
        # 获取图形实体（排除文本、标注等）
        graphic_entities = []
        for entity in msp:
            if entity.dxftype() not in ['TEXT', 'MTEXT', 'DIMENSION', 'INSERT', 'ATTRIB', 'ATTDEF']:
                graphic_entities.append(entity)
        
        print(f"\n=== 图形实体信息 ===")
        print(f"图形实体数量: {len(graphic_entities)}")
        
        # 分类统计图形实体
        graphic_entity_types = {}
        for entity in graphic_entities:
            entity_type = entity.dxftype()
            graphic_entity_types[entity_type] = graphic_entity_types.get(entity_type, 0) + 1
        
        for entity_type, count in sorted(graphic_entity_types.items()):
            print(f"  {entity_type}: {count}个")
        
        # 详细分析每个图形实体
        print(f"\n=== 详细实体分析 ===")
        for i, entity in enumerate(graphic_entities):
            entity_type = entity.dxftype()
            print(f"{i+1}. {entity_type}")
            
            if entity_type == 'CIRCLE':
                center = entity.dxf.center
                radius = entity.dxf.radius
                print(f"   中心: ({center.x:.2f}, {center.y:.2f}), 半径: {radius:.2f}")
            
            elif entity_type == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                print(f"   起点: ({start.x:.2f}, {start.y:.2f}), 终点: ({end.x:.2f}, {end.y:.2f})")
            
            elif entity_type == 'ARC':
                center = entity.dxf.center
                radius = entity.dxf.radius
                start_angle = entity.dxf.start_angle
                end_angle = entity.dxf.end_angle
                print(f"   中心: ({center.x:.2f}, {center.y:.2f}), 半径: {radius:.2f}")
                print(f"   起始角度: {start_angle:.2f}°, 结束角度: {end_angle:.2f}°")
            
            elif entity_type in ['POLYLINE', 'LWPOLYLINE']:
                try:
                    # 检查是否是封闭的多段线
                    is_closed = entity.is_closed if hasattr(entity, 'is_closed') else False
                    print(f"   是否封闭: {is_closed}")

                    if hasattr(entity, 'get_points'):
                        points = list(entity.get_points())
                        print(f"   顶点数: {len(points)}")

                        # 分析形状特征
                        if len(points) >= 8:  # 可能是圆形
                            # 计算中心点和半径
                            x_coords = [p[0] if isinstance(p, (list, tuple)) else p.x for p in points]
                            y_coords = [p[1] if isinstance(p, (list, tuple)) else p.y for p in points]
                            center_x = (min(x_coords) + max(x_coords)) / 2
                            center_y = (min(y_coords) + max(y_coords)) / 2

                            # 计算到中心的距离
                            distances = []
                            for p in points:
                                px = p[0] if isinstance(p, (list, tuple)) else p.x
                                py = p[1] if isinstance(p, (list, tuple)) else p.y
                                dist = ((px - center_x)**2 + (py - center_y)**2)**0.5
                                distances.append(dist)

                            avg_radius = sum(distances) / len(distances)
                            radius_variance = sum((d - avg_radius)**2 for d in distances) / len(distances)

                            print(f"   中心: ({center_x:.2f}, {center_y:.2f})")
                            print(f"   平均半径: {avg_radius:.2f}")
                            print(f"   半径方差: {radius_variance:.2f}")

                            # 如果半径方差很小，可能是圆形
                            if radius_variance < avg_radius * 0.1:  # 10%的容差
                                print(f"   *** 可能是圆形多段线 ***")

                        if len(points) <= 8:  # 显示所有点
                            for j, point in enumerate(points):
                                if isinstance(point, (list, tuple)):
                                    print(f"     点{j+1}: ({point[0]:.2f}, {point[1]:.2f})")
                                else:
                                    print(f"     点{j+1}: ({point.x:.2f}, {point.y:.2f})")
                except Exception as e:
                    print(f"   获取顶点失败: {e}")
        
        # 测试检测算法
        print(f"\n=== 检测算法测试 ===")
        checker = DXFCheckThread(".")
        
        # 测试嵌套图形检测
        is_nested = checker.check_nested_graphics_pattern(graphic_entities)
        print(f"嵌套图形检测结果: {is_nested}")
        
        # 分别测试各个子方法
        frame_entities = []
        closed_entities = []
        
        for entity in graphic_entities:
            entity_type = entity.dxftype()
            if entity_type in ['LINE', 'POLYLINE', 'LWPOLYLINE']:
                frame_entities.append(entity)
            elif entity_type in ['CIRCLE', 'ARC']:
                closed_entities.append(entity)
        
        print(f"外框实体数量: {len(frame_entities)}")
        print(f"封闭图形数量: {len(closed_entities)}")
        
        if len(closed_entities) >= 2:
            # 测试规律分布
            is_regular = checker.check_regular_distribution(closed_entities)
            print(f"规律分布检测: {is_regular}")
            
            # 测试空间分离
            is_separated = checker.check_spatial_separation(closed_entities)
            print(f"空间分离检测: {is_separated}")
            
            # 测试独立图形计数
            independent_count = checker.count_independent_graphics(closed_entities)
            print(f"独立图形数量: {independent_count}")
        
        # 测试外框检测
        if frame_entities:
            outer_frame = checker.detect_outer_frame(frame_entities)
            print(f"外框检测结果: {outer_frame is not None}")
            if outer_frame:
                print(f"外框边界: {outer_frame}")
        
        # 测试整体多图形检测
        is_multiple = checker.check_multiple_graphics(graphic_entities)
        print(f"整体多图形检测结果: {is_multiple}")
        
        # 如果没有检测出来，分析原因
        if not is_multiple:
            print(f"\n=== 未检测出多图形的可能原因分析 ===")
            
            if len(graphic_entities) < 3:
                print("- 图形实体数量不足（少于3个）")
            
            if len(closed_entities) < 2:
                print("- 封闭图形数量不足（少于2个）")
            
            if len(closed_entities) >= 2:
                # 检查圆圈是否真的是嵌套的
                print("- 分析圆圈关系:")
                for i, circle1 in enumerate(closed_entities):
                    for j, circle2 in enumerate(closed_entities):
                        if i >= j:
                            continue
                        
                        bbox1 = checker.get_entity_bounding_box(circle1)
                        bbox2 = checker.get_entity_bounding_box(circle2)
                        
                        if bbox1 and bbox2:
                            # 检查包含关系
                            contained1 = checker.is_bbox_contained_loose(bbox1, bbox2)
                            contained2 = checker.is_bbox_contained_loose(bbox2, bbox1)
                            overlap = checker.are_entities_overlapping_or_nested(bbox1, bbox2)
                            distance = checker.calculate_bbox_distance(bbox1, bbox2)
                            
                            print(f"  圆{i+1} vs 圆{j+1}:")
                            print(f"    包含关系: {contained1 or contained2}")
                            print(f"    重叠关系: {overlap}")
                            print(f"    距离: {distance:.2f}")
        
    except Exception as e:
        print(f"✗ 分析文件时出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_b34_file()
