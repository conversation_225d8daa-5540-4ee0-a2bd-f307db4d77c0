#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
终极修复版Python打包工具打包脚本
彻底解决pyimod02_importers模块缺失问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_ultimate_spec():
    """创建终极修复版的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

block_cipher = None

# 收集所有PyInstaller相关模块
pyinstaller_modules = collect_submodules('PyInstaller')

a = Analysis(
    ['Python打包工具.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('mylogo.ico', '.'),
        ('使用说明.md', '.'),
    ],
    hiddenimports=[
        # PyQt6相关
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'PyQt6.sip',
        
        # Python标准库
        'json',
        'subprocess',
        'pathlib',
        'datetime',
        'shutil',
        'threading',
        'queue',
        'configparser',
        'tempfile',
        'glob',
        'fnmatch',
        'zipfile',
        'tarfile',
        
        # 图像处理
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        
        # 导入相关
        'importlib',
        'importlib.util',
        'importlib.metadata',
        'importlib.machinery',
        'pkg_resources',
        'pkgutil',
        
        # PyInstaller相关 - 关键修复
        'PyInstaller',
        'PyInstaller.loader',
        'PyInstaller.loader.pyimod01_os_path',
        'PyInstaller.loader.pyimod02_importers',
        'PyInstaller.loader.pyimod03_importers',
        'PyInstaller.loader.pyiboot01_bootstrap',
        
        # 运行时钩子
        'pyi_rth_inspect',
        'pyi_rth_pkgutil',
        'pyi_rth_pyqt6',
        
        # 编码相关
        'encodings',
        'encodings.utf_8',
        'encodings.cp1252',
        'encodings.latin_1',
        
        # 其他常用库
        'platform',
        'locale',
        'ctypes',
        'ctypes.util',
        'distutils',
        'distutils.util',
    ] + pyinstaller_modules,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Python打包工具_终极版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 完全禁用UPX
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='mylogo.ico',
    version='version_info.txt'
)
'''
    
    with open('Python打包工具_终极版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 终极修复版spec文件已创建")
    return 'Python打包工具_终极版.spec'

def create_version_file():
    """创建版本信息文件"""
    version_content = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 1, 0),
    prodvers=(1, 0, 1, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [
            StringStruct(u'CompanyName', u'Python工具箱'),
            StringStruct(u'FileDescription', u'Python全自动打包工具 - 终极修复版'),
            StringStruct(u'FileVersion', u'*******'),
            StringStruct(u'InternalName', u'Python打包工具'),
            StringStruct(u'LegalCopyright', u'Copyright (C) 2024 西昌·水西丁'),
            StringStruct(u'OriginalFilename', u'Python打包工具_终极版.exe'),
            StringStruct(u'ProductName', u'Python全自动打包工具'),
            StringStruct(u'ProductVersion', u'*******')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print("✅ 版本信息文件已创建")

def check_environment():
    """检查和准备环境"""
    print("检查打包环境...")
    
    # 检查PyInstaller版本
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装")
        return False
    
    # 检查PyQt6
    try:
        import PyQt6
        print(f"✅ PyQt6版本: {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError:
        print("❌ PyQt6未安装")
        return False
    
    return True

def build_ultimate_version():
    """使用终极修复版spec文件进行打包"""
    print("\n开始终极修复版打包...")
    
    if not check_environment():
        return False
    
    # 创建spec文件和版本文件
    spec_file = create_ultimate_spec()
    create_version_file()
    
    # 彻底清理之前的构建
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 清理{dir_name}目录")
    
    # 删除之前的exe文件
    old_exe = 'dist/Python打包工具_终极版.exe'
    if os.path.exists(old_exe):
        os.remove(old_exe)
        print("✅ 删除旧的exe文件")
    
    # 使用spec文件打包，设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    
    command = [
        'pyinstaller', 
        '--clean',
        '--noconfirm',  # 不询问覆盖
        spec_file
    ]
    
    print(f"执行命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            errors='ignore',  # 忽略编码错误
            env=env
        )
        
        if result.returncode == 0:
            print("✅ 终极修复版打包成功！")
            return True
        else:
            print(f"❌ 打包失败:")
            if result.stderr:
                print(f"错误输出: {result.stderr}")
            if result.stdout:
                print(f"标准输出: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def create_startup_script():
    """创建启动脚本"""
    script_content = '''@echo off
chcp 65001 >nul
title Python全自动打包工具 - 终极修复版

echo.
echo ==========================================
echo    Python全自动打包工具 v1.0.1 终极修复版
echo ==========================================
echo.

cd /d "%~dp0"

if exist "dist\\Python打包工具_终极版.exe" (
    echo [信息] 启动终极修复版打包工具...
    echo [信息] 此版本已解决模块缺失问题
    echo.
    start "" "dist\\Python打包工具_终极版.exe"
    echo [信息] 程序已启动！
    echo.
    echo 如果程序正常启动，此窗口可以关闭
    timeout /t 5 >nul
) else (
    echo [错误] 未找到终极修复版exe文件！
    echo 请确保 dist\\Python打包工具_终极版.exe 存在
    echo.
    pause
)
'''
    
    with open('启动终极版.bat', 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print("✅ 创建启动脚本")

def test_and_package():
    """测试并创建最终包"""
    print("\n测试生成的exe文件...")
    
    exe_path = Path("dist/Python打包工具_终极版.exe")
    if not exe_path.exists():
        print("❌ 未找到生成的exe文件")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    print(f"文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
    
    # 复制必要文件
    files_to_copy = [
        ('使用说明.md', 'dist/使用说明.md'),
        ('README.md', 'dist/README.md'),
        ('测试程序.py', 'dist/测试程序.py'),
        ('mylogo.ico', 'dist/mylogo.ico')
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            try:
                shutil.copy2(src, dst)
                print(f"✅ 复制: {src}")
            except Exception as e:
                print(f"⚠️ 复制失败 {src}: {e}")
    
    # 创建终极版说明
    readme_content = """Python全自动打包工具 v1.0.1 - 终极修复版
============================================

本版本彻底解决了pyimod02_importers模块缺失问题！

修复内容：
✅ 完全解决pyimod02_importers模块缺失错误
✅ 添加了完整的PyInstaller模块导入
✅ 优化了模块加载机制
✅ 禁用了可能导致问题的UPX压缩
✅ 改进了编码处理

使用方法：
1. 双击运行 Python打包工具_终极版.exe
2. 或者运行 启动终极版.bat

如果还有问题：
1. 确保系统安装了Visual C++运行库
2. 以管理员身份运行
3. 检查防病毒软件是否误报

作者: 西昌·水西丁
版本: v1.0.1 终极修复版
日期: 2024年7月
"""
    
    with open('dist/终极版说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建终极版说明文件")
    return True

def cleanup():
    """清理临时文件"""
    print("\n清理临时文件...")
    
    files_to_remove = [
        'version_info.txt',
        'Python打包工具_终极版.spec'
    ]
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✅ 删除: {file_name}")
            except:
                print(f"⚠️ 无法删除: {file_name}")

def main():
    """主函数"""
    print("=" * 70)
    print("    Python打包工具 - 终极修复版打包脚本")
    print("    彻底解决 pyimod02_importers 模块缺失问题")
    print("=" * 70)
    
    # 检查源文件
    if not os.path.exists('Python打包工具.py'):
        print("❌ 源文件 Python打包工具.py 不存在")
        return False
    
    # 执行终极修复版打包
    if not build_ultimate_version():
        print("\n❌ 终极修复版打包失败！")
        return False
    
    # 测试和打包
    if not test_and_package():
        print("\n⚠️ 后处理出现问题")
    
    # 创建启动脚本
    create_startup_script()
    
    # 清理临时文件
    cleanup()
    
    print("\n" + "=" * 70)
    print("✅ 终极修复版打包完成！")
    print("📁 exe文件: dist/Python打包工具_终极版.exe")
    print("🚀 启动脚本: 启动终极版.bat")
    print("📖 说明文档: dist/终极版说明.txt")
    print("=" * 70)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 终极修复版打包成功！")
        print("💡 这个版本应该彻底解决了模块缺失问题")
    else:
        print("\n💥 终极修复版打包失败")
    
    input("\n按回车键退出...")
