"""
快速黑色背景修复脚本
直接运行此脚本来快速验证和修复界面中的黑色背景问题
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def apply_emergency_white_background():
    """应用紧急白色背景修复"""
    print("=== 应用紧急白色背景修复 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 全局样式修复
        emergency_style = """
        * {
            background-color: #ffffff;
            color: #333333;
            border: none;
        }

        QMainWindow {
            background-color: #ffffff;
            color: #333333;
            border: none;
        }

        QMainWindow::separator {
            background-color: #e1e1e1;
            width: 1px;
            height: 1px;
        }

        QWidget {
            background-color: #ffffff;
            color: #333333;
            border: none;
        }
        
        QListWidget {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #d1d1d1;
            selection-background-color: #cce8ff;
        }
        
        QListWidget::item {
            background-color: #ffffff;
            color: #333333;
            padding: 4px;
        }
        
        QListWidget::item:hover {
            background-color: #e1e1e1;
        }
        
        QListWidget::item:selected {
            background-color: #cce8ff;
            color: #333333;
        }
        
        QTableWidget {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #d1d1d1;
            gridline-color: #e0e0e0;
            selection-background-color: #cce8ff;
        }
        
        QTableWidget::item {
            background-color: #ffffff;
            color: #333333;
            padding: 6px;
        }
        
        QTableWidget::item:hover {
            background-color: #e1e1e1;
        }
        
        QTableWidget::item:selected {
            background-color: #cce8ff;
            color: #333333;
        }
        
        QTabWidget {
            background-color: #ffffff;
            border: 1px solid #d1d1d1;
        }
        
        QTabWidget::pane {
            background-color: #ffffff;
            border: 1px solid #d1d1d1;
        }
        
        QTabBar::tab {
            background-color: #f5f5f5;
            color: #333333;
            border: 1px solid #d1d1d1;
            padding: 6px 12px;
        }
        
        QTabBar::tab:selected {
            background-color: #ffffff;
            color: #333333;
        }
        
        QTabBar::tab:hover {
            background-color: #e1e1e1;
        }
        
        QTextEdit {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #d1d1d1;
        }
        
        QLineEdit {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #d1d1d1;
        }
        
        QGroupBox {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #d1d1d1;
        }
        
        QPushButton {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #d1d1d1;
            padding: 6px 12px;
        }
        
        QPushButton:hover {
            background-color: #e1e1e1;
        }
        
        QSplitter {
            background-color: #ffffff;
        }
        
        QSplitter::handle {
            background-color: #e1e1e1;
            border: 1px solid #d1d1d1;
        }
        
        QFrame {
            background-color: #ffffff;
            color: #333333;
        }
        """
        
        # 应用全局样式
        app.setStyleSheet(emergency_style)
        print("✓ 紧急白色背景样式已应用")
        
        return True
        
    except Exception as e:
        print(f"✗ 紧急修复失败: {e}")
        return False


def test_and_run_main_program():
    """测试并运行主程序"""
    print("\n=== 测试并运行主程序 ===")
    
    try:
        # 先应用紧急修复
        apply_emergency_white_background()
        
        # 导入主程序
        import 批量搜索_优化版
        
        print("✓ 主程序导入成功")
        
        # 创建应用实例
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = 批量搜索_优化版.OptimizedBatchSearchApp()
        
        print("✓ 主窗口创建成功")
        
        # 显示窗口
        main_window.show()
        
        print("✓ 主窗口已显示")
        print("\n🎉 程序启动成功！")
        print("如果仍有黑色区域，请检查以下组件:")
        print("1. 搜索历史列表")
        print("2. 搜索结果列表")
        print("3. 统计信息表格")
        print("4. 标签页内容")
        
        # 运行应用
        if not app.exec_():
            print("程序正常退出")
        
        return True
        
    except Exception as e:
        print(f"✗ 程序运行失败: {e}")
        return False


def quick_diagnostic():
    """快速诊断"""
    print("=== 快速诊断黑色背景问题 ===")
    
    # 检查关键文件
    files_to_check = [
        "批量搜索_优化版.py",
        "enhanced_ui_components.py", 
        "ui_styles.py"
    ]
    
    for filename in files_to_check:
        filepath = os.path.join(os.path.dirname(__file__), filename)
        if os.path.exists(filepath):
            print(f"✓ {filename} 存在")
        else:
            print(f"✗ {filename} 缺失")
    
    # 检查PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5 可用")
    except ImportError:
        print("✗ PyQt5 不可用")
        return False
    
    return True


def main():
    """主函数"""
    print("批量搜索优化版 - 快速黑色背景修复工具")
    print("=" * 50)
    
    # 快速诊断
    if not quick_diagnostic():
        print("诊断失败，请检查环境配置")
        return
    
    print("\n选择操作:")
    print("1. 仅应用紧急白色背景修复")
    print("2. 运行完整的背景修复测试")
    print("3. 直接启动主程序（推荐）")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            apply_emergency_white_background()
            print("\n紧急修复完成，请手动启动主程序")
            
        elif choice == "2":
            print("\n运行完整测试...")
            os.system("python test_background_fix.py")
            
        elif choice == "3":
            print("\n启动主程序...")
            test_and_run_main_program()
            
        else:
            print("无效选择，直接启动主程序...")
            test_and_run_main_program()
            
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n操作失败: {e}")


if __name__ == "__main__":
    main()
